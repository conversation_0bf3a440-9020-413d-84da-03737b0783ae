{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ArrowDown10 = createLucideIcon(\"ArrowDown10\", [[\"path\", {\n  d: \"m3 16 4 4 4-4\",\n  key: \"1co6wj\"\n}], [\"path\", {\n  d: \"M7 20V4\",\n  key: \"1yoxec\"\n}], [\"path\", {\n  d: \"M17 10V4h-2\",\n  key: \"zcsr5x\"\n}], [\"path\", {\n  d: \"M15 10h4\",\n  key: \"id2lce\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"14\",\n  width: \"4\",\n  height: \"6\",\n  ry: \"2\",\n  key: \"33xykx\"\n}]]);\nexport { ArrowDown10 as default };", "map": {"version": 3, "names": ["ArrowDown10", "createLucideIcon", "d", "key", "x", "y", "width", "height", "ry"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\arrow-down-1-0.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowDown10\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAxNiA0IDQgNC00IiAvPgogIDxwYXRoIGQ9Ik03IDIwVjQiIC8+CiAgPHBhdGggZD0iTTE3IDEwVjRoLTIiIC8+CiAgPHBhdGggZD0iTTE1IDEwaDQiIC8+CiAgPHJlY3QgeD0iMTUiIHk9IjE0IiB3aWR0aD0iNCIgaGVpZ2h0PSI2IiByeT0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-down-1-0\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDown10 = createLucideIcon('ArrowDown10', [\n  ['path', { d: 'm3 16 4 4 4-4', key: '1co6wj' }],\n  ['path', { d: 'M7 20V4', key: '1yoxec' }],\n  ['path', { d: 'M17 10V4h-2', key: 'zcsr5x' }],\n  ['path', { d: 'M15 10h4', key: 'id2lce' }],\n  ['rect', { x: '15', y: '14', width: '4', height: '6', ry: '2', key: '33xykx' }],\n]);\n\nexport default ArrowDown10;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAG;EAAMC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}