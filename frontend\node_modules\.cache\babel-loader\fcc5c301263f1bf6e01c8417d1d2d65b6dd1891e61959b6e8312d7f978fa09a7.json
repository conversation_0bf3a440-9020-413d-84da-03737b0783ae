{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport FormControlContext from './FormControlContext';\nexport default function useFormControl() {\n  return React.useContext(FormControlContext);\n}", "map": {"version": 3, "names": ["React", "FormControlContext", "useFormControl", "useContext"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/FormControl/useFormControl.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport FormControlContext from './FormControlContext';\nexport default function useFormControl() {\n  return React.useContext(FormControlContext);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,OAAOF,KAAK,CAACG,UAAU,CAACF,kBAAkB,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}