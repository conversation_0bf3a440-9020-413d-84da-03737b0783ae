{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Expand = createLucideIcon(\"Expand\", [[\"path\", {\n  d: \"m21 21-6-6m6 6v-4.8m0 4.8h-4.8\",\n  key: \"1c15vz\"\n}], [\"path\", {\n  d: \"M3 16.2V21m0 0h4.8M3 21l6-6\",\n  key: \"1fsnz2\"\n}], [\"path\", {\n  d: \"M21 7.8V3m0 0h-4.8M21 3l-6 6\",\n  key: \"hawz9i\"\n}], [\"path\", {\n  d: \"M3 7.8V3m0 0h4.8M3 3l6 6\",\n  key: \"u9ee12\"\n}]]);\nexport { Expand as default };", "map": {"version": 3, "names": ["Expand", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\expand.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Expand\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNi02bTYgNnYtNC44bTAgNC44aC00LjgiIC8+CiAgPHBhdGggZD0iTTMgMTYuMlYyMW0wIDBoNC44TTMgMjFsNi02IiAvPgogIDxwYXRoIGQ9Ik0yMSA3LjhWM20wIDBoLTQuOE0yMSAzbC02IDYiIC8+CiAgPHBhdGggZD0iTTMgNy44VjNtMCAwaDQuOE0zIDNsNiA2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/expand\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Expand = createLucideIcon('Expand', [\n  ['path', { d: 'm21 21-6-6m6 6v-4.8m0 4.8h-4.8', key: '1c15vz' }],\n  ['path', { d: 'M3 16.2V21m0 0h4.8M3 21l6-6', key: '1fsnz2' }],\n  ['path', { d: 'M21 7.8V3m0 0h-4.8M21 3l-6 6', key: 'hawz9i' }],\n  ['path', { d: 'M3 7.8V3m0 0h4.8M3 3l6 6', key: 'u9ee12' }],\n]);\n\nexport default Expand;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}