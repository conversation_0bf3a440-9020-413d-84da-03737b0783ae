{"ast": null, "code": "import { useLayoutEffect, useEffect } from 'react';\nimport { isBrowser } from './is-browser.mjs';\nconst useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\nexport { useIsomorphicLayoutEffect };", "map": {"version": 3, "names": ["useLayoutEffect", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "useIsomorphicLayoutEffect"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs"], "sourcesContent": ["import { useLayoutEffect, useEffect } from 'react';\nimport { isBrowser } from './is-browser.mjs';\n\nconst useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\n\nexport { useIsomorphicLayoutEffect };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAE5C,MAAMC,yBAAyB,GAAGD,SAAS,GAAGF,eAAe,GAAGC,SAAS;AAEzE,SAASE,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}