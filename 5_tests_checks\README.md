# 5. Tests & Checks

This folder contains all testing scripts and validation tools.

## Files:
- `atlas_beginner_test_suite.py` - Beginner-style test questions
- `atlas_comprehensive_beginner_test.py` - Comprehensive beginner testing
- `atlas_comprehensive_test.py` - Full system testing
- `final_test.py` - Final validation tests
- `manual_test.py` - Manual testing scripts
- `quick_response_test.py` - Quick response validation
- `simple_manual_test.py` - Simple manual tests
- `simple_test.py` - Basic functionality tests
- `test_optimized_trading_god.py` - Trading God optimization tests
- `test_trading_god.py` - Trading God functionality tests
- `test_trading_god_format.py` - Trading God format validation

## Purpose:
These files ensure A.T.L.A.S. works correctly by testing all 25+ capabilities with specific questions and validating the Stock Market God response format. Run these regularly to maintain 100% system reliability.
