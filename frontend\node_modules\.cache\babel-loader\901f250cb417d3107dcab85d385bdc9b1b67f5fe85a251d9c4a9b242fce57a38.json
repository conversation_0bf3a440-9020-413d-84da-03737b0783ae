{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Tablets = createLucideIcon(\"Tablets\", [[\"circle\", {\n  cx: \"7\",\n  cy: \"7\",\n  r: \"5\",\n  key: \"x29byf\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"17\",\n  r: \"5\",\n  key: \"1op1d2\"\n}], [\"path\", {\n  d: \"M12 17h10\",\n  key: \"ls21zv\"\n}], [\"path\", {\n  d: \"m3.46 10.54 7.08-7.08\",\n  key: \"1rehiu\"\n}]]);\nexport { Tablets as default };", "map": {"version": 3, "names": ["Tablets", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\tablets.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Tablets\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI3IiBjeT0iNyIgcj0iNSIgLz4KICA8Y2lyY2xlIGN4PSIxNyIgY3k9IjE3IiByPSI1IiAvPgogIDxwYXRoIGQ9Ik0xMiAxN2gxMCIgLz4KICA8cGF0aCBkPSJtMy40NiAxMC41NCA3LjA4LTcuMDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/tablets\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tablets = createLucideIcon('Tablets', [\n  ['circle', { cx: '7', cy: '7', r: '5', key: 'x29byf' }],\n  ['circle', { cx: '17', cy: '17', r: '5', key: '1op1d2' }],\n  ['path', { d: 'M12 17h10', key: 'ls21zv' }],\n  ['path', { d: 'm3.46 10.54 7.08-7.08', key: '1rehiu' }],\n]);\n\nexport default Tablets;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}