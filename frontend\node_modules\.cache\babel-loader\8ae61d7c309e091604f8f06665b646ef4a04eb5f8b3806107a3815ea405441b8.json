{"ast": null, "code": "export { default as common } from './common';\nexport { default as red } from './red';\nexport { default as pink } from './pink';\nexport { default as purple } from './purple';\nexport { default as deepPurple } from './deepPurple';\nexport { default as indigo } from './indigo';\nexport { default as blue } from './blue';\nexport { default as lightBlue } from './lightBlue';\nexport { default as cyan } from './cyan';\nexport { default as teal } from './teal';\nexport { default as green } from './green';\nexport { default as lightGreen } from './lightGreen';\nexport { default as lime } from './lime';\nexport { default as yellow } from './yellow';\nexport { default as amber } from './amber';\nexport { default as orange } from './orange';\nexport { default as deepOrange } from './deepOrange';\nexport { default as brown } from './brown';\nexport { default as grey } from './grey';\nexport { default as blueGrey } from './blueGrey';", "map": {"version": 3, "names": ["default", "common", "red", "pink", "purple", "deepPurple", "indigo", "blue", "lightBlue", "cyan", "teal", "green", "lightGreen", "lime", "yellow", "amber", "orange", "deepOrange", "brown", "grey", "blue<PERSON>rey"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/colors/index.js"], "sourcesContent": ["export { default as common } from './common';\nexport { default as red } from './red';\nexport { default as pink } from './pink';\nexport { default as purple } from './purple';\nexport { default as deepPurple } from './deepPurple';\nexport { default as indigo } from './indigo';\nexport { default as blue } from './blue';\nexport { default as lightBlue } from './lightBlue';\nexport { default as cyan } from './cyan';\nexport { default as teal } from './teal';\nexport { default as green } from './green';\nexport { default as lightGreen } from './lightGreen';\nexport { default as lime } from './lime';\nexport { default as yellow } from './yellow';\nexport { default as amber } from './amber';\nexport { default as orange } from './orange';\nexport { default as deepOrange } from './deepOrange';\nexport { default as brown } from './brown';\nexport { default as grey } from './grey';\nexport { default as blueGrey } from './blueGrey';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,MAAM,QAAQ,UAAU;AAC5C,SAASD,OAAO,IAAIE,GAAG,QAAQ,OAAO;AACtC,SAASF,OAAO,IAAIG,IAAI,QAAQ,QAAQ;AACxC,SAASH,OAAO,IAAII,MAAM,QAAQ,UAAU;AAC5C,SAASJ,OAAO,IAAIK,UAAU,QAAQ,cAAc;AACpD,SAASL,OAAO,IAAIM,MAAM,QAAQ,UAAU;AAC5C,SAASN,OAAO,IAAIO,IAAI,QAAQ,QAAQ;AACxC,SAASP,OAAO,IAAIQ,SAAS,QAAQ,aAAa;AAClD,SAASR,OAAO,IAAIS,IAAI,QAAQ,QAAQ;AACxC,SAAST,OAAO,IAAIU,IAAI,QAAQ,QAAQ;AACxC,SAASV,OAAO,IAAIW,KAAK,QAAQ,SAAS;AAC1C,SAASX,OAAO,IAAIY,UAAU,QAAQ,cAAc;AACpD,SAASZ,OAAO,IAAIa,IAAI,QAAQ,QAAQ;AACxC,SAASb,OAAO,IAAIc,MAAM,QAAQ,UAAU;AAC5C,SAASd,OAAO,IAAIe,KAAK,QAAQ,SAAS;AAC1C,SAASf,OAAO,IAAIgB,MAAM,QAAQ,UAAU;AAC5C,SAAShB,OAAO,IAAIiB,UAAU,QAAQ,cAAc;AACpD,SAASjB,OAAO,IAAIkB,KAAK,QAAQ,SAAS;AAC1C,SAASlB,OAAO,IAAImB,IAAI,QAAQ,QAAQ;AACxC,SAASnB,OAAO,IAAIoB,QAAQ,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}