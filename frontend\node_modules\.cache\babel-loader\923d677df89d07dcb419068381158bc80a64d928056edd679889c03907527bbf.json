{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst List = createLucideIcon(\"List\", [[\"line\", {\n  x1: \"8\",\n  x2: \"21\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"7ey8pc\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"21\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"rjfblc\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"21\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"c3b1m8\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"3.01\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"1g7gq3\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"3.01\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1pjlvk\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"3.01\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"28t2mc\"\n}]]);\nexport { List as default };", "map": {"version": 3, "names": ["List", "createLucideIcon", "x1", "x2", "y1", "y2", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\list.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name List\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iOCIgeDI9IjIxIiB5MT0iNiIgeTI9IjYiIC8+CiAgPGxpbmUgeDE9IjgiIHgyPSIyMSIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjgiIHgyPSIyMSIgeTE9IjE4IiB5Mj0iMTgiIC8+CiAgPGxpbmUgeDE9IjMiIHgyPSIzLjAxIiB5MT0iNiIgeTI9IjYiIC8+CiAgPGxpbmUgeDE9IjMiIHgyPSIzLjAxIiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMyIgeDI9IjMuMDEiIHkxPSIxOCIgeTI9IjE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/list\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst List = createLucideIcon('List', [\n  ['line', { x1: '8', x2: '21', y1: '6', y2: '6', key: '7ey8pc' }],\n  ['line', { x1: '8', x2: '21', y1: '12', y2: '12', key: 'rjfblc' }],\n  ['line', { x1: '8', x2: '21', y1: '18', y2: '18', key: 'c3b1m8' }],\n  ['line', { x1: '3', x2: '3.01', y1: '6', y2: '6', key: '1g7gq3' }],\n  ['line', { x1: '3', x2: '3.01', y1: '12', y2: '12', key: '1pjlvk' }],\n  ['line', { x1: '3', x2: '3.01', y1: '18', y2: '18', key: '28t2mc' }],\n]);\n\nexport default List;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,EACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}