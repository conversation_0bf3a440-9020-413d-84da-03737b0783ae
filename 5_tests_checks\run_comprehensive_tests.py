#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive Test Runner
Executes all 80+ validation checks with detailed reporting
"""

import sys
import os
import json
import time
from datetime import datetime

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '1_main_chat_engine'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '4_helper_tools'))

from comprehensive_atlas_test_suite import ATLASTestSuite

def check_atlas_server():
    """Check if A.T.L.A.S. server is running"""
    import requests
    try:
        response = requests.get("http://localhost:8080/api/v1/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def generate_html_report(results):
    """Generate HTML test report"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>A.T.L.A.S. Test Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
            .summary { background: #ecf0f1; padding: 15px; margin: 20px 0; border-radius: 5px; }
            .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
            .passed { background: #d5f4e6; border-left: 4px solid #27ae60; }
            .failed { background: #fadbd8; border-left: 4px solid #e74c3c; }
            .exception { background: #fdeaa7; border-left: 4px solid #f39c12; }
            .metric { display: inline-block; margin: 10px 20px 10px 0; }
            .metric-value { font-size: 24px; font-weight: bold; }
            .metric-label { font-size: 12px; color: #7f8c8d; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔮 A.T.L.A.S. Comprehensive Test Report</h1>
            <p>Generated: {timestamp}</p>
        </div>
        
        <div class="summary">
            <h2>📊 Test Summary</h2>
            <div class="metric">
                <div class="metric-value">{total_tests}</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="metric-value" style="color: #27ae60;">{passed}</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value" style="color: #e74c3c;">{failed}</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value" style="color: #3498db;">{pass_rate:.1f}%</div>
                <div class="metric-label">Pass Rate</div>
            </div>
        </div>
        
        <div class="test-results">
            <h2>🧪 Detailed Results</h2>
            {test_details}
        </div>
    </body>
    </html>
    """
    
    # Generate test details HTML
    test_details = ""
    for test_name, result in results["results"].items():
        status = result["status"].lower()
        css_class = status if status in ["passed", "failed", "exception"] else "failed"
        
        test_details += f"""
        <div class="test-result {css_class}">
            <h3>{'✅' if status == 'passed' else '❌' if status == 'failed' else '💥'} {test_name}</h3>
            <p><strong>Status:</strong> {result["status"]}</p>
            {f'<p><strong>Error:</strong> {result.get("error", "")}</p>' if result.get("error") else ""}
            {f'<p><strong>Details:</strong> {json.dumps(result, indent=2)}</p>' if len(str(result)) < 500 else ""}
        </div>
        """
    
    html_content = html_template.format(
        timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        total_tests=results["total_tests"],
        passed=results["passed"],
        failed=results["failed"],
        pass_rate=results["pass_rate"],
        test_details=test_details
    )
    
    # Save HTML report
    report_path = os.path.join(os.path.dirname(__file__), "atlas_test_report.html")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    return report_path

def main():
    """Main test execution"""
    print("🚀 A.T.L.A.S. COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    print("Validating all 80+ system capabilities...")
    print()
    
    # Check if server is running
    print("🔍 Checking A.T.L.A.S. server status...")
    if not check_atlas_server():
        print("❌ A.T.L.A.S. server is not running!")
        print("Please start the server first:")
        print("   python 1_main_chat_engine/atlas_server.py")
        print("   or")
        print("   python start_production.py")
        return 1
    
    print("✅ A.T.L.A.S. server is running")
    print()
    
    # Run comprehensive tests
    start_time = time.time()
    suite = ATLASTestSuite()
    results = suite.run_all_tests()
    end_time = time.time()
    
    # Generate reports
    print("\n📄 Generating test reports...")
    
    # JSON report
    json_report_path = os.path.join(os.path.dirname(__file__), "atlas_test_results.json")
    with open(json_report_path, "w") as f:
        json.dump(results, f, indent=2, default=str)
    print(f"   📄 JSON Report: {json_report_path}")
    
    # HTML report
    html_report_path = generate_html_report(results)
    print(f"   🌐 HTML Report: {html_report_path}")
    
    # Summary
    execution_time = end_time - start_time
    print(f"\n⏱️  Total execution time: {execution_time:.2f} seconds")
    print(f"🎯 Final result: {results['pass_rate']:.1f}% pass rate")
    
    # Return appropriate exit code
    if results["pass_rate"] >= 90:
        print("🎉 EXCELLENT! A.T.L.A.S. is production ready!")
        return 0
    elif results["pass_rate"] >= 80:
        print("✅ GOOD! Minor issues to address.")
        return 0
    else:
        print("❌ CRITICAL ISSUES! Major fixes required.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
