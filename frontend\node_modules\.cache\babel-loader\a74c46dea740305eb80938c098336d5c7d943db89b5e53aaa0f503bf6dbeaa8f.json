{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M8.57 12.09 7 10H4v2.28l2 .67zm3.96 2.68-2.58-1.03L6 15.05l-2-.66v1.89l2 .67zM15 4.65V2.64l-2.58-.52-1.42.95v1.86l1.23.82zm-9 14.4-2-.66V20h14l-2.97-3.96zm12-9.09v1.08l.8.64 2.2-.98V9.6l-2-.44z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 12 8 8H2v14h20l-6-8zm-7-2h3l1.57 2.09-2.57.86-2-.67zm0 4.39 2 .67 3.95-1.32 2.58 1.03L6 16.95l-2-.67zM4 20v-1.61l2 .67 9.03-3.01L18 20zM17 6V1l-5-1-3 2v4l3 2zm-6-2.93 1.42-.95 2.58.52v2.01l-2.77 1.11L11 4.93zM18.5 7 16 9v3l2.5 2 4.5-2V8zm2.5 3.7-2.2.98-.8-.64V9.96l1-.8 2 .44z\"\n}, \"1\")], 'LandslideTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/LandslideTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M8.57 12.09 7 10H4v2.28l2 .67zm3.96 2.68-2.58-1.03L6 15.05l-2-.66v1.89l2 .67zM15 4.65V2.64l-2.58-.52-1.42.95v1.86l1.23.82zm-9 14.4-2-.66V20h14l-2.97-3.96zm12-9.09v1.08l.8.64 2.2-.98V9.6l-2-.44z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 12 8 8H2v14h20l-6-8zm-7-2h3l1.57 2.09-2.57.86-2-.67zm0 4.39 2 .67 3.95-1.32 2.58 1.03L6 16.95l-2-.67zM4 20v-1.61l2 .67 9.03-3.01L18 20zM17 6V1l-5-1-3 2v4l3 2zm-6-2.93 1.42-.95 2.58.52v2.01l-2.77 1.11L11 4.93zM18.5 7 16 9v3l2.5 2 4.5-2V8zm2.5 3.7-2.2.98-.8-.64V9.96l1-.8 2 .44z\"\n}, \"1\")], 'LandslideTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,mMAAmM;EACtMC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}