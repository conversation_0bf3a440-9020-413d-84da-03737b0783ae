{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.alpha = alpha;\nexports.blend = blend;\nexports.colorChannel = void 0;\nexports.darken = darken;\nexports.decomposeColor = decomposeColor;\nexports.emphasize = emphasize;\nexports.getContrastRatio = getContrastRatio;\nexports.getLuminance = getLuminance;\nexports.hexToRgb = hexToRgb;\nexports.hslToRgb = hslToRgb;\nexports.lighten = lighten;\nexports.private_safeAlpha = private_safeAlpha;\nexports.private_safeColorChannel = void 0;\nexports.private_safeDarken = private_safeDarken;\nexports.private_safeEmphasize = private_safeEmphasize;\nexports.private_safeLighten = private_safeLighten;\nexports.recomposeColor = recomposeColor;\nexports.rgbToHex = rgbToHex;\nvar _formatMuiErrorMessage2 = _interopRequireDefault(require(\"@mui/utils/formatMuiErrorMessage\"));\nvar _clamp = _interopRequireDefault(require(\"@mui/utils/clamp\"));\n/* eslint-disable @typescript-eslint/naming-convention */\n\n/**\n * Returns a number whose value is limited to the given range.\n * @param {number} value The value to be clamped\n * @param {number} min The lower boundary of the output range\n * @param {number} max The upper boundary of the output range\n * @returns {number} A number in the range [min, max]\n */\nfunction clampWrapper(value) {\n  let min = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let max = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value < min || value > max) {\n      console.error(\"MUI: The value provided \".concat(value, \" is out of range [\").concat(min, \", \").concat(max, \"].\"));\n    }\n  }\n  return (0, _clamp.default)(value, min, max);\n}\n\n/**\n * Converts a color from CSS hex format to CSS rgb format.\n * @param {string} color - Hex color, i.e. #nnn or #nnnnnn\n * @returns {string} A CSS rgb color string\n */\nfunction hexToRgb(color) {\n  color = color.slice(1);\n  const re = new RegExp(\".{1,\".concat(color.length >= 6 ? 2 : 1, \"}\"), 'g');\n  let colors = color.match(re);\n  if (colors && colors[0].length === 1) {\n    colors = colors.map(n => n + n);\n  }\n  return colors ? \"rgb\".concat(colors.length === 4 ? 'a' : '', \"(\").concat(colors.map((n, index) => {\n    return index < 3 ? parseInt(n, 16) : Math.round(parseInt(n, 16) / 255 * 1000) / 1000;\n  }).join(', '), \")\") : '';\n}\nfunction intToHex(int) {\n  const hex = int.toString(16);\n  return hex.length === 1 ? \"0\".concat(hex) : hex;\n}\n\n/**\n * Returns an object with the type and values of a color.\n *\n * Note: Does not support rgb % values.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {object} - A MUI color object: {type: string, values: number[]}\n */\nfunction decomposeColor(color) {\n  // Idempotent\n  if (color.type) {\n    return color;\n  }\n  if (color.charAt(0) === '#') {\n    return decomposeColor(hexToRgb(color));\n  }\n  const marker = color.indexOf('(');\n  const type = color.substring(0, marker);\n  if (['rgb', 'rgba', 'hsl', 'hsla', 'color'].indexOf(type) === -1) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: Unsupported `\".concat(color, \"` color.\\nThe following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().\") : (0, _formatMuiErrorMessage2.default)(9, color));\n  }\n  let values = color.substring(marker + 1, color.length - 1);\n  let colorSpace;\n  if (type === 'color') {\n    values = values.split(' ');\n    colorSpace = values.shift();\n    if (values.length === 4 && values[3].charAt(0) === '/') {\n      values[3] = values[3].slice(1);\n    }\n    if (['srgb', 'display-p3', 'a98-rgb', 'prophoto-rgb', 'rec-2020'].indexOf(colorSpace) === -1) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: unsupported `\".concat(colorSpace, \"` color space.\\nThe following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.\") : (0, _formatMuiErrorMessage2.default)(10, colorSpace));\n    }\n  } else {\n    values = values.split(',');\n  }\n  values = values.map(value => parseFloat(value));\n  return {\n    type,\n    values,\n    colorSpace\n  };\n}\n\n/**\n * Returns a channel created from the input color.\n *\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {string} - The channel for the color, that can be used in rgba or hsla colors\n */\nconst colorChannel = color => {\n  const decomposedColor = decomposeColor(color);\n  return decomposedColor.values.slice(0, 3).map((val, idx) => decomposedColor.type.indexOf('hsl') !== -1 && idx !== 0 ? \"\".concat(val, \"%\") : val).join(' ');\n};\nexports.colorChannel = colorChannel;\nconst private_safeColorChannel = (color, warning) => {\n  try {\n    return colorChannel(color);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n};\n\n/**\n * Converts a color object with type and values to a string.\n * @param {object} color - Decomposed color\n * @param {string} color.type - One of: 'rgb', 'rgba', 'hsl', 'hsla', 'color'\n * @param {array} color.values - [n,n,n] or [n,n,n,n]\n * @returns {string} A CSS color string\n */\nexports.private_safeColorChannel = private_safeColorChannel;\nfunction recomposeColor(color) {\n  const {\n    type,\n    colorSpace\n  } = color;\n  let {\n    values\n  } = color;\n  if (type.indexOf('rgb') !== -1) {\n    // Only convert the first 3 values to int (i.e. not alpha)\n    values = values.map((n, i) => i < 3 ? parseInt(n, 10) : n);\n  } else if (type.indexOf('hsl') !== -1) {\n    values[1] = \"\".concat(values[1], \"%\");\n    values[2] = \"\".concat(values[2], \"%\");\n  }\n  if (type.indexOf('color') !== -1) {\n    values = \"\".concat(colorSpace, \" \").concat(values.join(' '));\n  } else {\n    values = \"\".concat(values.join(', '));\n  }\n  return \"\".concat(type, \"(\").concat(values, \")\");\n}\n\n/**\n * Converts a color from CSS rgb format to CSS hex format.\n * @param {string} color - RGB color, i.e. rgb(n, n, n)\n * @returns {string} A CSS rgb color string, i.e. #nnnnnn\n */\nfunction rgbToHex(color) {\n  // Idempotent\n  if (color.indexOf('#') === 0) {\n    return color;\n  }\n  const {\n    values\n  } = decomposeColor(color);\n  return \"#\".concat(values.map((n, i) => intToHex(i === 3 ? Math.round(255 * n) : n)).join(''));\n}\n\n/**\n * Converts a color from hsl format to rgb format.\n * @param {string} color - HSL color values\n * @returns {string} rgb color values\n */\nfunction hslToRgb(color) {\n  color = decomposeColor(color);\n  const {\n    values\n  } = color;\n  const h = values[0];\n  const s = values[1] / 100;\n  const l = values[2] / 100;\n  const a = s * Math.min(l, 1 - l);\n  const f = function (n) {\n    let k = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (n + h / 30) % 12;\n    return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  };\n  let type = 'rgb';\n  const rgb = [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];\n  if (color.type === 'hsla') {\n    type += 'a';\n    rgb.push(values[3]);\n  }\n  return recomposeColor({\n    type,\n    values: rgb\n  });\n}\n/**\n * The relative brightness of any point in a color space,\n * normalized to 0 for darkest black and 1 for lightest white.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {number} The relative brightness of the color in the range 0 - 1\n */\nfunction getLuminance(color) {\n  color = decomposeColor(color);\n  let rgb = color.type === 'hsl' || color.type === 'hsla' ? decomposeColor(hslToRgb(color)).values : color.values;\n  rgb = rgb.map(val => {\n    if (color.type !== 'color') {\n      val /= 255; // normalized\n    }\n    return val <= 0.03928 ? val / 12.92 : ((val + 0.055) / 1.055) ** 2.4;\n  });\n\n  // Truncate at 3 digits\n  return Number((0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2]).toFixed(3));\n}\n\n/**\n * Calculates the contrast ratio between two colors.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} foreground - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @param {string} background - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @returns {number} A contrast ratio value in the range 0 - 21.\n */\nfunction getContrastRatio(foreground, background) {\n  const lumA = getLuminance(foreground);\n  const lumB = getLuminance(background);\n  return (Math.max(lumA, lumB) + 0.05) / (Math.min(lumA, lumB) + 0.05);\n}\n\n/**\n * Sets the absolute transparency of a color.\n * Any existing alpha values are overwritten.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} value - value to set the alpha channel to in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction alpha(color, value) {\n  color = decomposeColor(color);\n  value = clampWrapper(value);\n  if (color.type === 'rgb' || color.type === 'hsl') {\n    color.type += 'a';\n  }\n  if (color.type === 'color') {\n    color.values[3] = \"/\".concat(value);\n  } else {\n    color.values[3] = value;\n  }\n  return recomposeColor(color);\n}\nfunction private_safeAlpha(color, value, warning) {\n  try {\n    return alpha(color, value);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darkens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction darken(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] *= 1 - coefficient;\n  } else if (color.type.indexOf('rgb') !== -1 || color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] *= 1 - coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nfunction private_safeDarken(color, coefficient, warning) {\n  try {\n    return darken(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Lightens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction lighten(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] += (100 - color.values[2]) * coefficient;\n  } else if (color.type.indexOf('rgb') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (255 - color.values[i]) * coefficient;\n    }\n  } else if (color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (1 - color.values[i]) * coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nfunction private_safeLighten(color, coefficient, warning) {\n  try {\n    return lighten(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darken or lighten a color, depending on its luminance.\n * Light colors are darkened, dark colors are lightened.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient=0.15 - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction emphasize(color) {\n  let coefficient = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.15;\n  return getLuminance(color) > 0.5 ? darken(color, coefficient) : lighten(color, coefficient);\n}\nfunction private_safeEmphasize(color, coefficient, warning) {\n  try {\n    return emphasize(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Blend a transparent overlay color with a background color, resulting in a single\n * RGB color.\n * @param {string} background - CSS color\n * @param {string} overlay - CSS color\n * @param {number} opacity - Opacity multiplier in the range 0 - 1\n * @param {number} [gamma=1.0] - Gamma correction factor. For gamma-correct blending, 2.2 is usual.\n */\nfunction blend(background, overlay, opacity) {\n  let gamma = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1.0;\n  const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n  const backgroundColor = decomposeColor(background);\n  const overlayColor = decomposeColor(overlay);\n  const rgb = [blendChannel(backgroundColor.values[0], overlayColor.values[0]), blendChannel(backgroundColor.values[1], overlayColor.values[1]), blendChannel(backgroundColor.values[2], overlayColor.values[2])];\n  return recomposeColor({\n    type: 'rgb',\n    values: rgb\n  });\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "alpha", "blend", "colorChannel", "darken", "decomposeColor", "emphasize", "getContrastRatio", "getLuminance", "hexToRgb", "hslToRgb", "lighten", "private_safeAlpha", "private_safeColorChannel", "private_safeDarken", "private_safeEmphasize", "private_safeLighten", "recomposeColor", "rgbToHex", "_formatMuiErrorMessage2", "_clamp", "clampWrapper", "min", "arguments", "length", "undefined", "max", "process", "env", "NODE_ENV", "console", "error", "concat", "default", "color", "slice", "re", "RegExp", "colors", "match", "map", "n", "index", "parseInt", "Math", "round", "join", "intToHex", "int", "hex", "toString", "type", "char<PERSON>t", "marker", "indexOf", "substring", "Error", "values", "colorSpace", "split", "shift", "parseFloat", "decomposedColor", "val", "idx", "warning", "warn", "i", "h", "s", "l", "a", "f", "k", "rgb", "push", "Number", "toFixed", "foreground", "background", "lumA", "lumB", "coefficient", "overlay", "opacity", "gamma", "blendChannel", "b", "o", "backgroundColor", "overlayColor"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/system/colorManipulator.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.alpha = alpha;\nexports.blend = blend;\nexports.colorChannel = void 0;\nexports.darken = darken;\nexports.decomposeColor = decomposeColor;\nexports.emphasize = emphasize;\nexports.getContrastRatio = getContrastRatio;\nexports.getLuminance = getLuminance;\nexports.hexToRgb = hexToRgb;\nexports.hslToRgb = hslToRgb;\nexports.lighten = lighten;\nexports.private_safeAlpha = private_safeAlpha;\nexports.private_safeColorChannel = void 0;\nexports.private_safeDarken = private_safeDarken;\nexports.private_safeEmphasize = private_safeEmphasize;\nexports.private_safeLighten = private_safeLighten;\nexports.recomposeColor = recomposeColor;\nexports.rgbToHex = rgbToHex;\nvar _formatMuiErrorMessage2 = _interopRequireDefault(require(\"@mui/utils/formatMuiErrorMessage\"));\nvar _clamp = _interopRequireDefault(require(\"@mui/utils/clamp\"));\n/* eslint-disable @typescript-eslint/naming-convention */\n\n/**\n * Returns a number whose value is limited to the given range.\n * @param {number} value The value to be clamped\n * @param {number} min The lower boundary of the output range\n * @param {number} max The upper boundary of the output range\n * @returns {number} A number in the range [min, max]\n */\nfunction clampWrapper(value, min = 0, max = 1) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (value < min || value > max) {\n      console.error(`MUI: The value provided ${value} is out of range [${min}, ${max}].`);\n    }\n  }\n  return (0, _clamp.default)(value, min, max);\n}\n\n/**\n * Converts a color from CSS hex format to CSS rgb format.\n * @param {string} color - Hex color, i.e. #nnn or #nnnnnn\n * @returns {string} A CSS rgb color string\n */\nfunction hexToRgb(color) {\n  color = color.slice(1);\n  const re = new RegExp(`.{1,${color.length >= 6 ? 2 : 1}}`, 'g');\n  let colors = color.match(re);\n  if (colors && colors[0].length === 1) {\n    colors = colors.map(n => n + n);\n  }\n  return colors ? `rgb${colors.length === 4 ? 'a' : ''}(${colors.map((n, index) => {\n    return index < 3 ? parseInt(n, 16) : Math.round(parseInt(n, 16) / 255 * 1000) / 1000;\n  }).join(', ')})` : '';\n}\nfunction intToHex(int) {\n  const hex = int.toString(16);\n  return hex.length === 1 ? `0${hex}` : hex;\n}\n\n/**\n * Returns an object with the type and values of a color.\n *\n * Note: Does not support rgb % values.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {object} - A MUI color object: {type: string, values: number[]}\n */\nfunction decomposeColor(color) {\n  // Idempotent\n  if (color.type) {\n    return color;\n  }\n  if (color.charAt(0) === '#') {\n    return decomposeColor(hexToRgb(color));\n  }\n  const marker = color.indexOf('(');\n  const type = color.substring(0, marker);\n  if (['rgb', 'rgba', 'hsl', 'hsla', 'color'].indexOf(type) === -1) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Unsupported \\`${color}\\` color.\nThe following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().` : (0, _formatMuiErrorMessage2.default)(9, color));\n  }\n  let values = color.substring(marker + 1, color.length - 1);\n  let colorSpace;\n  if (type === 'color') {\n    values = values.split(' ');\n    colorSpace = values.shift();\n    if (values.length === 4 && values[3].charAt(0) === '/') {\n      values[3] = values[3].slice(1);\n    }\n    if (['srgb', 'display-p3', 'a98-rgb', 'prophoto-rgb', 'rec-2020'].indexOf(colorSpace) === -1) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: unsupported \\`${colorSpace}\\` color space.\nThe following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.` : (0, _formatMuiErrorMessage2.default)(10, colorSpace));\n    }\n  } else {\n    values = values.split(',');\n  }\n  values = values.map(value => parseFloat(value));\n  return {\n    type,\n    values,\n    colorSpace\n  };\n}\n\n/**\n * Returns a channel created from the input color.\n *\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {string} - The channel for the color, that can be used in rgba or hsla colors\n */\nconst colorChannel = color => {\n  const decomposedColor = decomposeColor(color);\n  return decomposedColor.values.slice(0, 3).map((val, idx) => decomposedColor.type.indexOf('hsl') !== -1 && idx !== 0 ? `${val}%` : val).join(' ');\n};\nexports.colorChannel = colorChannel;\nconst private_safeColorChannel = (color, warning) => {\n  try {\n    return colorChannel(color);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n};\n\n/**\n * Converts a color object with type and values to a string.\n * @param {object} color - Decomposed color\n * @param {string} color.type - One of: 'rgb', 'rgba', 'hsl', 'hsla', 'color'\n * @param {array} color.values - [n,n,n] or [n,n,n,n]\n * @returns {string} A CSS color string\n */\nexports.private_safeColorChannel = private_safeColorChannel;\nfunction recomposeColor(color) {\n  const {\n    type,\n    colorSpace\n  } = color;\n  let {\n    values\n  } = color;\n  if (type.indexOf('rgb') !== -1) {\n    // Only convert the first 3 values to int (i.e. not alpha)\n    values = values.map((n, i) => i < 3 ? parseInt(n, 10) : n);\n  } else if (type.indexOf('hsl') !== -1) {\n    values[1] = `${values[1]}%`;\n    values[2] = `${values[2]}%`;\n  }\n  if (type.indexOf('color') !== -1) {\n    values = `${colorSpace} ${values.join(' ')}`;\n  } else {\n    values = `${values.join(', ')}`;\n  }\n  return `${type}(${values})`;\n}\n\n/**\n * Converts a color from CSS rgb format to CSS hex format.\n * @param {string} color - RGB color, i.e. rgb(n, n, n)\n * @returns {string} A CSS rgb color string, i.e. #nnnnnn\n */\nfunction rgbToHex(color) {\n  // Idempotent\n  if (color.indexOf('#') === 0) {\n    return color;\n  }\n  const {\n    values\n  } = decomposeColor(color);\n  return `#${values.map((n, i) => intToHex(i === 3 ? Math.round(255 * n) : n)).join('')}`;\n}\n\n/**\n * Converts a color from hsl format to rgb format.\n * @param {string} color - HSL color values\n * @returns {string} rgb color values\n */\nfunction hslToRgb(color) {\n  color = decomposeColor(color);\n  const {\n    values\n  } = color;\n  const h = values[0];\n  const s = values[1] / 100;\n  const l = values[2] / 100;\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  let type = 'rgb';\n  const rgb = [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];\n  if (color.type === 'hsla') {\n    type += 'a';\n    rgb.push(values[3]);\n  }\n  return recomposeColor({\n    type,\n    values: rgb\n  });\n}\n/**\n * The relative brightness of any point in a color space,\n * normalized to 0 for darkest black and 1 for lightest white.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {number} The relative brightness of the color in the range 0 - 1\n */\nfunction getLuminance(color) {\n  color = decomposeColor(color);\n  let rgb = color.type === 'hsl' || color.type === 'hsla' ? decomposeColor(hslToRgb(color)).values : color.values;\n  rgb = rgb.map(val => {\n    if (color.type !== 'color') {\n      val /= 255; // normalized\n    }\n    return val <= 0.03928 ? val / 12.92 : ((val + 0.055) / 1.055) ** 2.4;\n  });\n\n  // Truncate at 3 digits\n  return Number((0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2]).toFixed(3));\n}\n\n/**\n * Calculates the contrast ratio between two colors.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} foreground - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @param {string} background - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @returns {number} A contrast ratio value in the range 0 - 21.\n */\nfunction getContrastRatio(foreground, background) {\n  const lumA = getLuminance(foreground);\n  const lumB = getLuminance(background);\n  return (Math.max(lumA, lumB) + 0.05) / (Math.min(lumA, lumB) + 0.05);\n}\n\n/**\n * Sets the absolute transparency of a color.\n * Any existing alpha values are overwritten.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} value - value to set the alpha channel to in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction alpha(color, value) {\n  color = decomposeColor(color);\n  value = clampWrapper(value);\n  if (color.type === 'rgb' || color.type === 'hsl') {\n    color.type += 'a';\n  }\n  if (color.type === 'color') {\n    color.values[3] = `/${value}`;\n  } else {\n    color.values[3] = value;\n  }\n  return recomposeColor(color);\n}\nfunction private_safeAlpha(color, value, warning) {\n  try {\n    return alpha(color, value);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darkens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction darken(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] *= 1 - coefficient;\n  } else if (color.type.indexOf('rgb') !== -1 || color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] *= 1 - coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nfunction private_safeDarken(color, coefficient, warning) {\n  try {\n    return darken(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Lightens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction lighten(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] += (100 - color.values[2]) * coefficient;\n  } else if (color.type.indexOf('rgb') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (255 - color.values[i]) * coefficient;\n    }\n  } else if (color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (1 - color.values[i]) * coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nfunction private_safeLighten(color, coefficient, warning) {\n  try {\n    return lighten(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darken or lighten a color, depending on its luminance.\n * Light colors are darkened, dark colors are lightened.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient=0.15 - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nfunction emphasize(color, coefficient = 0.15) {\n  return getLuminance(color) > 0.5 ? darken(color, coefficient) : lighten(color, coefficient);\n}\nfunction private_safeEmphasize(color, coefficient, warning) {\n  try {\n    return emphasize(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Blend a transparent overlay color with a background color, resulting in a single\n * RGB color.\n * @param {string} background - CSS color\n * @param {string} overlay - CSS color\n * @param {number} opacity - Opacity multiplier in the range 0 - 1\n * @param {number} [gamma=1.0] - Gamma correction factor. For gamma-correct blending, 2.2 is usual.\n */\nfunction blend(background, overlay, opacity, gamma = 1.0) {\n  const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n  const backgroundColor = decomposeColor(background);\n  const overlayColor = decomposeColor(overlay);\n  const rgb = [blendChannel(backgroundColor.values[0], overlayColor.values[0]), blendChannel(backgroundColor.values[1], overlayColor.values[1]), blendChannel(backgroundColor.values[2], overlayColor.values[2])];\n  return recomposeColor({\n    type: 'rgb',\n    values: rgb\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AACpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrBF,OAAO,CAACG,KAAK,GAAGA,KAAK;AACrBH,OAAO,CAACI,YAAY,GAAG,KAAK,CAAC;AAC7BJ,OAAO,CAACK,MAAM,GAAGA,MAAM;AACvBL,OAAO,CAACM,cAAc,GAAGA,cAAc;AACvCN,OAAO,CAACO,SAAS,GAAGA,SAAS;AAC7BP,OAAO,CAACQ,gBAAgB,GAAGA,gBAAgB;AAC3CR,OAAO,CAACS,YAAY,GAAGA,YAAY;AACnCT,OAAO,CAACU,QAAQ,GAAGA,QAAQ;AAC3BV,OAAO,CAACW,QAAQ,GAAGA,QAAQ;AAC3BX,OAAO,CAACY,OAAO,GAAGA,OAAO;AACzBZ,OAAO,CAACa,iBAAiB,GAAGA,iBAAiB;AAC7Cb,OAAO,CAACc,wBAAwB,GAAG,KAAK,CAAC;AACzCd,OAAO,CAACe,kBAAkB,GAAGA,kBAAkB;AAC/Cf,OAAO,CAACgB,qBAAqB,GAAGA,qBAAqB;AACrDhB,OAAO,CAACiB,mBAAmB,GAAGA,mBAAmB;AACjDjB,OAAO,CAACkB,cAAc,GAAGA,cAAc;AACvClB,OAAO,CAACmB,QAAQ,GAAGA,QAAQ;AAC3B,IAAIC,uBAAuB,GAAGxB,sBAAsB,CAACC,OAAO,CAAC,kCAAkC,CAAC,CAAC;AACjG,IAAIwB,MAAM,GAAGzB,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyB,YAAYA,CAACrB,KAAK,EAAoB;EAAA,IAAlBsB,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,GAAG,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC3C,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI7B,KAAK,GAAGsB,GAAG,IAAItB,KAAK,GAAG0B,GAAG,EAAE;MAC9BI,OAAO,CAACC,KAAK,4BAAAC,MAAA,CAA4BhC,KAAK,wBAAAgC,MAAA,CAAqBV,GAAG,QAAAU,MAAA,CAAKN,GAAG,OAAI,CAAC;IACrF;EACF;EACA,OAAO,CAAC,CAAC,EAAEN,MAAM,CAACa,OAAO,EAAEjC,KAAK,EAAEsB,GAAG,EAAEI,GAAG,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASjB,QAAQA,CAACyB,KAAK,EAAE;EACvBA,KAAK,GAAGA,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;EACtB,MAAMC,EAAE,GAAG,IAAIC,MAAM,QAAAL,MAAA,CAAQE,KAAK,CAACV,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAK,GAAG,CAAC;EAC/D,IAAIc,MAAM,GAAGJ,KAAK,CAACK,KAAK,CAACH,EAAE,CAAC;EAC5B,IAAIE,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,CAACd,MAAM,KAAK,CAAC,EAAE;IACpCc,MAAM,GAAGA,MAAM,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,GAAGA,CAAC,CAAC;EACjC;EACA,OAAOH,MAAM,SAAAN,MAAA,CAASM,MAAM,CAACd,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,OAAAQ,MAAA,CAAIM,MAAM,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;IAC/E,OAAOA,KAAK,GAAG,CAAC,GAAGC,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACF,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI;EACtF,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,SAAM,EAAE;AACvB;AACA,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,GAAG,GAAGD,GAAG,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC5B,OAAOD,GAAG,CAACzB,MAAM,KAAK,CAAC,OAAAQ,MAAA,CAAOiB,GAAG,IAAKA,GAAG;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5C,cAAcA,CAAC6B,KAAK,EAAE;EAC7B;EACA,IAAIA,KAAK,CAACiB,IAAI,EAAE;IACd,OAAOjB,KAAK;EACd;EACA,IAAIA,KAAK,CAACkB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3B,OAAO/C,cAAc,CAACI,QAAQ,CAACyB,KAAK,CAAC,CAAC;EACxC;EACA,MAAMmB,MAAM,GAAGnB,KAAK,CAACoB,OAAO,CAAC,GAAG,CAAC;EACjC,MAAMH,IAAI,GAAGjB,KAAK,CAACqB,SAAS,CAAC,CAAC,EAAEF,MAAM,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACC,OAAO,CAACH,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IAChE,MAAM,IAAIK,KAAK,CAAC7B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,wBAAAG,MAAA,CAAyBE,KAAK,4GACO,CAAC,CAAC,EAAEf,uBAAuB,CAACc,OAAO,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC;EAC3I;EACA,IAAIuB,MAAM,GAAGvB,KAAK,CAACqB,SAAS,CAACF,MAAM,GAAG,CAAC,EAAEnB,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC;EAC1D,IAAIkC,UAAU;EACd,IAAIP,IAAI,KAAK,OAAO,EAAE;IACpBM,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;IAC1BD,UAAU,GAAGD,MAAM,CAACG,KAAK,CAAC,CAAC;IAC3B,IAAIH,MAAM,CAACjC,MAAM,KAAK,CAAC,IAAIiC,MAAM,CAAC,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACtDK,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACtB,KAAK,CAAC,CAAC,CAAC;IAChC;IACA,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC,CAACmB,OAAO,CAACI,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5F,MAAM,IAAIF,KAAK,CAAC7B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,wBAAAG,MAAA,CAAyB0B,UAAU,oHACE,CAAC,CAAC,EAAEvC,uBAAuB,CAACc,OAAO,EAAE,EAAE,EAAEyB,UAAU,CAAC,CAAC;IACjJ;EACF,CAAC,MAAM;IACLD,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAC5B;EACAF,MAAM,GAAGA,MAAM,CAACjB,GAAG,CAACxC,KAAK,IAAI6D,UAAU,CAAC7D,KAAK,CAAC,CAAC;EAC/C,OAAO;IACLmD,IAAI;IACJM,MAAM;IACNC;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMvD,YAAY,GAAG+B,KAAK,IAAI;EAC5B,MAAM4B,eAAe,GAAGzD,cAAc,CAAC6B,KAAK,CAAC;EAC7C,OAAO4B,eAAe,CAACL,MAAM,CAACtB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACK,GAAG,CAAC,CAACuB,GAAG,EAAEC,GAAG,KAAKF,eAAe,CAACX,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAIU,GAAG,KAAK,CAAC,MAAAhC,MAAA,CAAM+B,GAAG,SAAMA,GAAG,CAAC,CAACjB,IAAI,CAAC,GAAG,CAAC;AAClJ,CAAC;AACD/C,OAAO,CAACI,YAAY,GAAGA,YAAY;AACnC,MAAMU,wBAAwB,GAAGA,CAACqB,KAAK,EAAE+B,OAAO,KAAK;EACnD,IAAI;IACF,OAAO9D,YAAY,CAAC+B,KAAK,CAAC;EAC5B,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd,IAAIkC,OAAO,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACoC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAO/B,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAnC,OAAO,CAACc,wBAAwB,GAAGA,wBAAwB;AAC3D,SAASI,cAAcA,CAACiB,KAAK,EAAE;EAC7B,MAAM;IACJiB,IAAI;IACJO;EACF,CAAC,GAAGxB,KAAK;EACT,IAAI;IACFuB;EACF,CAAC,GAAGvB,KAAK;EACT,IAAIiB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9B;IACAG,MAAM,GAAGA,MAAM,CAACjB,GAAG,CAAC,CAACC,CAAC,EAAE0B,CAAC,KAAKA,CAAC,GAAG,CAAC,GAAGxB,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC,GAAGA,CAAC,CAAC;EAC5D,CAAC,MAAM,IAAIU,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACrCG,MAAM,CAAC,CAAC,CAAC,MAAAzB,MAAA,CAAMyB,MAAM,CAAC,CAAC,CAAC,MAAG;IAC3BA,MAAM,CAAC,CAAC,CAAC,MAAAzB,MAAA,CAAMyB,MAAM,CAAC,CAAC,CAAC,MAAG;EAC7B;EACA,IAAIN,IAAI,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IAChCG,MAAM,MAAAzB,MAAA,CAAM0B,UAAU,OAAA1B,MAAA,CAAIyB,MAAM,CAACX,IAAI,CAAC,GAAG,CAAC,CAAE;EAC9C,CAAC,MAAM;IACLW,MAAM,MAAAzB,MAAA,CAAMyB,MAAM,CAACX,IAAI,CAAC,IAAI,CAAC,CAAE;EACjC;EACA,UAAAd,MAAA,CAAUmB,IAAI,OAAAnB,MAAA,CAAIyB,MAAM;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASvC,QAAQA,CAACgB,KAAK,EAAE;EACvB;EACA,IAAIA,KAAK,CAACoB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAOpB,KAAK;EACd;EACA,MAAM;IACJuB;EACF,CAAC,GAAGpD,cAAc,CAAC6B,KAAK,CAAC;EACzB,WAAAF,MAAA,CAAWyB,MAAM,CAACjB,GAAG,CAAC,CAACC,CAAC,EAAE0B,CAAC,KAAKpB,QAAQ,CAACoB,CAAC,KAAK,CAAC,GAAGvB,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGJ,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC;AACvF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASpC,QAAQA,CAACwB,KAAK,EAAE;EACvBA,KAAK,GAAG7B,cAAc,CAAC6B,KAAK,CAAC;EAC7B,MAAM;IACJuB;EACF,CAAC,GAAGvB,KAAK;EACT,MAAMkC,CAAC,GAAGX,MAAM,CAAC,CAAC,CAAC;EACnB,MAAMY,CAAC,GAAGZ,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;EACzB,MAAMa,CAAC,GAAGb,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;EACzB,MAAMc,CAAC,GAAGF,CAAC,GAAGzB,IAAI,CAACtB,GAAG,CAACgD,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;EAChC,MAAME,CAAC,GAAG,SAAAA,CAAC/B,CAAC;IAAA,IAAEgC,CAAC,GAAAlD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAACkB,CAAC,GAAG2B,CAAC,GAAG,EAAE,IAAI,EAAE;IAAA,OAAKE,CAAC,GAAGC,CAAC,GAAG3B,IAAI,CAAClB,GAAG,CAACkB,IAAI,CAACtB,GAAG,CAACmD,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAA;EACvF,IAAItB,IAAI,GAAG,KAAK;EAChB,MAAMuB,GAAG,GAAG,CAAC9B,IAAI,CAACC,KAAK,CAAC2B,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE5B,IAAI,CAACC,KAAK,CAAC2B,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE5B,IAAI,CAACC,KAAK,CAAC2B,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;EACpF,IAAItC,KAAK,CAACiB,IAAI,KAAK,MAAM,EAAE;IACzBA,IAAI,IAAI,GAAG;IACXuB,GAAG,CAACC,IAAI,CAAClB,MAAM,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,OAAOxC,cAAc,CAAC;IACpBkC,IAAI;IACJM,MAAM,EAAEiB;EACV,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlE,YAAYA,CAAC0B,KAAK,EAAE;EAC3BA,KAAK,GAAG7B,cAAc,CAAC6B,KAAK,CAAC;EAC7B,IAAIwC,GAAG,GAAGxC,KAAK,CAACiB,IAAI,KAAK,KAAK,IAAIjB,KAAK,CAACiB,IAAI,KAAK,MAAM,GAAG9C,cAAc,CAACK,QAAQ,CAACwB,KAAK,CAAC,CAAC,CAACuB,MAAM,GAAGvB,KAAK,CAACuB,MAAM;EAC/GiB,GAAG,GAAGA,GAAG,CAAClC,GAAG,CAACuB,GAAG,IAAI;IACnB,IAAI7B,KAAK,CAACiB,IAAI,KAAK,OAAO,EAAE;MAC1BY,GAAG,IAAI,GAAG,CAAC,CAAC;IACd;IACA,OAAOA,GAAG,IAAI,OAAO,GAAGA,GAAG,GAAG,KAAK,GAAG,CAAC,CAACA,GAAG,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG;EACtE,CAAC,CAAC;;EAEF;EACA,OAAOa,MAAM,CAAC,CAAC,MAAM,GAAGF,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC,EAAEG,OAAO,CAAC,CAAC,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStE,gBAAgBA,CAACuE,UAAU,EAAEC,UAAU,EAAE;EAChD,MAAMC,IAAI,GAAGxE,YAAY,CAACsE,UAAU,CAAC;EACrC,MAAMG,IAAI,GAAGzE,YAAY,CAACuE,UAAU,CAAC;EACrC,OAAO,CAACnC,IAAI,CAAClB,GAAG,CAACsD,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI,KAAKrC,IAAI,CAACtB,GAAG,CAAC0D,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShF,KAAKA,CAACiC,KAAK,EAAElC,KAAK,EAAE;EAC3BkC,KAAK,GAAG7B,cAAc,CAAC6B,KAAK,CAAC;EAC7BlC,KAAK,GAAGqB,YAAY,CAACrB,KAAK,CAAC;EAC3B,IAAIkC,KAAK,CAACiB,IAAI,KAAK,KAAK,IAAIjB,KAAK,CAACiB,IAAI,KAAK,KAAK,EAAE;IAChDjB,KAAK,CAACiB,IAAI,IAAI,GAAG;EACnB;EACA,IAAIjB,KAAK,CAACiB,IAAI,KAAK,OAAO,EAAE;IAC1BjB,KAAK,CAACuB,MAAM,CAAC,CAAC,CAAC,OAAAzB,MAAA,CAAOhC,KAAK,CAAE;EAC/B,CAAC,MAAM;IACLkC,KAAK,CAACuB,MAAM,CAAC,CAAC,CAAC,GAAGzD,KAAK;EACzB;EACA,OAAOiB,cAAc,CAACiB,KAAK,CAAC;AAC9B;AACA,SAAStB,iBAAiBA,CAACsB,KAAK,EAAElC,KAAK,EAAEiE,OAAO,EAAE;EAChD,IAAI;IACF,OAAOhE,KAAK,CAACiC,KAAK,EAAElC,KAAK,CAAC;EAC5B,CAAC,CAAC,OAAO+B,KAAK,EAAE;IACd,IAAIkC,OAAO,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACoC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAO/B,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9B,MAAMA,CAAC8B,KAAK,EAAEgD,WAAW,EAAE;EAClChD,KAAK,GAAG7B,cAAc,CAAC6B,KAAK,CAAC;EAC7BgD,WAAW,GAAG7D,YAAY,CAAC6D,WAAW,CAAC;EACvC,IAAIhD,KAAK,CAACiB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCpB,KAAK,CAACuB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGyB,WAAW;EACpC,CAAC,MAAM,IAAIhD,KAAK,CAACiB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAIpB,KAAK,CAACiB,IAAI,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACjF,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BjC,KAAK,CAACuB,MAAM,CAACU,CAAC,CAAC,IAAI,CAAC,GAAGe,WAAW;IACpC;EACF;EACA,OAAOjE,cAAc,CAACiB,KAAK,CAAC;AAC9B;AACA,SAASpB,kBAAkBA,CAACoB,KAAK,EAAEgD,WAAW,EAAEjB,OAAO,EAAE;EACvD,IAAI;IACF,OAAO7D,MAAM,CAAC8B,KAAK,EAAEgD,WAAW,CAAC;EACnC,CAAC,CAAC,OAAOnD,KAAK,EAAE;IACd,IAAIkC,OAAO,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACoC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAO/B,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASvB,OAAOA,CAACuB,KAAK,EAAEgD,WAAW,EAAE;EACnChD,KAAK,GAAG7B,cAAc,CAAC6B,KAAK,CAAC;EAC7BgD,WAAW,GAAG7D,YAAY,CAAC6D,WAAW,CAAC;EACvC,IAAIhD,KAAK,CAACiB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCpB,KAAK,CAACuB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAGvB,KAAK,CAACuB,MAAM,CAAC,CAAC,CAAC,IAAIyB,WAAW;EAC1D,CAAC,MAAM,IAAIhD,KAAK,CAACiB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IAC3C,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BjC,KAAK,CAACuB,MAAM,CAACU,CAAC,CAAC,IAAI,CAAC,GAAG,GAAGjC,KAAK,CAACuB,MAAM,CAACU,CAAC,CAAC,IAAIe,WAAW;IAC1D;EACF,CAAC,MAAM,IAAIhD,KAAK,CAACiB,IAAI,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7C,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BjC,KAAK,CAACuB,MAAM,CAACU,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGjC,KAAK,CAACuB,MAAM,CAACU,CAAC,CAAC,IAAIe,WAAW;IACxD;EACF;EACA,OAAOjE,cAAc,CAACiB,KAAK,CAAC;AAC9B;AACA,SAASlB,mBAAmBA,CAACkB,KAAK,EAAEgD,WAAW,EAAEjB,OAAO,EAAE;EACxD,IAAI;IACF,OAAOtD,OAAO,CAACuB,KAAK,EAAEgD,WAAW,CAAC;EACpC,CAAC,CAAC,OAAOnD,KAAK,EAAE;IACd,IAAIkC,OAAO,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACoC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAO/B,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5B,SAASA,CAAC4B,KAAK,EAAsB;EAAA,IAApBgD,WAAW,GAAA3D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAC1C,OAAOf,YAAY,CAAC0B,KAAK,CAAC,GAAG,GAAG,GAAG9B,MAAM,CAAC8B,KAAK,EAAEgD,WAAW,CAAC,GAAGvE,OAAO,CAACuB,KAAK,EAAEgD,WAAW,CAAC;AAC7F;AACA,SAASnE,qBAAqBA,CAACmB,KAAK,EAAEgD,WAAW,EAAEjB,OAAO,EAAE;EAC1D,IAAI;IACF,OAAO3D,SAAS,CAAC4B,KAAK,EAAEgD,WAAW,CAAC;EACtC,CAAC,CAAC,OAAOnD,KAAK,EAAE;IACd,IAAIkC,OAAO,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACoC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAO/B,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShC,KAAKA,CAAC6E,UAAU,EAAEI,OAAO,EAAEC,OAAO,EAAe;EAAA,IAAbC,KAAK,GAAA9D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EACtD,MAAM+D,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK5C,IAAI,CAACC,KAAK,CAAC,CAAC0C,CAAC,KAAK,CAAC,GAAGF,KAAK,CAAC,IAAI,CAAC,GAAGD,OAAO,CAAC,GAAGI,CAAC,KAAK,CAAC,GAAGH,KAAK,CAAC,GAAGD,OAAO,KAAKC,KAAK,CAAC;EACnH,MAAMI,eAAe,GAAGpF,cAAc,CAAC0E,UAAU,CAAC;EAClD,MAAMW,YAAY,GAAGrF,cAAc,CAAC8E,OAAO,CAAC;EAC5C,MAAMT,GAAG,GAAG,CAACY,YAAY,CAACG,eAAe,CAAChC,MAAM,CAAC,CAAC,CAAC,EAAEiC,YAAY,CAACjC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE6B,YAAY,CAACG,eAAe,CAAChC,MAAM,CAAC,CAAC,CAAC,EAAEiC,YAAY,CAACjC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE6B,YAAY,CAACG,eAAe,CAAChC,MAAM,CAAC,CAAC,CAAC,EAAEiC,YAAY,CAACjC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/M,OAAOxC,cAAc,CAAC;IACpBkC,IAAI,EAAE,KAAK;IACXM,MAAM,EAAEiB;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}