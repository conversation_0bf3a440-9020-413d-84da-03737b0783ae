{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Anvil = createLucideIcon(\"Anvil\", [[\"path\", {\n  d: \"M7 10c-2.8 0-5-2.2-5-5h5\",\n  key: \"1d6adc\"\n}], [\"path\", {\n  d: \"M7 4v8h7a8 8 0 0 0 8-8Z\",\n  key: \"uu98hv\"\n}], [\"path\", {\n  d: \"M9 12v5\",\n  key: \"3anwtq\"\n}], [\"path\", {\n  d: \"M15 12v5\",\n  key: \"5xh3zn\"\n}], [\"path\", {\n  d: \"M5 20a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v1H5Z\",\n  key: \"10a9tj\"\n}]]);\nexport { Anvil as default };", "map": {"version": 3, "names": ["An<PERSON>", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\anvil.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Anvil\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAxMGMtMi44IDAtNS0yLjItNS01aDUiIC8+CiAgPHBhdGggZD0iTTcgNHY4aDdhOCA4IDAgMCAwIDgtOFoiIC8+CiAgPHBhdGggZD0iTTkgMTJ2NSIgLz4KICA8cGF0aCBkPSJNMTUgMTJ2NSIgLz4KICA8cGF0aCBkPSJNNSAyMGEzIDMgMCAwIDEgMy0zaDhhMyAzIDAgMCAxIDMgM3YxSDVaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/anvil\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Anvil = createLucideIcon('Anvil', [\n  ['path', { d: 'M7 10c-2.8 0-5-2.2-5-5h5', key: '1d6adc' }],\n  ['path', { d: 'M7 4v8h7a8 8 0 0 0 8-8Z', key: 'uu98hv' }],\n  ['path', { d: 'M9 12v5', key: '3anwtq' }],\n  ['path', { d: 'M15 12v5', key: '5xh3zn' }],\n  ['path', { d: 'M5 20a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v1H5Z', key: '10a9tj' }],\n]);\n\nexport default Anvil;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}