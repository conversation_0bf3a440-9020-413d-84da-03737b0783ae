{"ast": null, "code": "'use client';\n\nexport { default } from './Grid2';\nexport * from './Grid2Props';\nexport { default as grid2Classes } from './grid2Classes';\nexport * from './grid2Classes';", "map": {"version": 3, "names": ["default", "grid2Classes"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Unstable_Grid2/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Grid2';\nexport * from './Grid2Props';\nexport { default as grid2Classes } from './grid2Classes';\nexport * from './grid2Classes';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,cAAc,cAAc;AAC5B,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}