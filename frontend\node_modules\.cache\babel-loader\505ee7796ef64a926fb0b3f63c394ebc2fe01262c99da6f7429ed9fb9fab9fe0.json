{"ast": null, "code": "'use client';\n\nexport { default } from './Table';\nexport { default as tableClasses } from './tableClasses';\nexport * from './tableClasses';", "map": {"version": 3, "names": ["default", "tableClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Table/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Table';\nexport { default as tableClasses } from './tableClasses';\nexport * from './tableClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}