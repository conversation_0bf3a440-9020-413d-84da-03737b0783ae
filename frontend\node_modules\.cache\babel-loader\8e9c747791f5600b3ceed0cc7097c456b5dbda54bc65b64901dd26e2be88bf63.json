{"ast": null, "code": "export function size(_a) {\n  var width = _a.width,\n    height = _a.height;\n  if (width < 0) {\n    throw new Error('Negative width is not allowed for Size');\n  }\n  if (height < 0) {\n    throw new Error('Negative height is not allowed for Size');\n  }\n  return {\n    width: width,\n    height: height\n  };\n}\nexport function equalSizes(first, second) {\n  return first.width === second.width && first.height === second.height;\n}", "map": {"version": 3, "names": ["size", "_a", "width", "height", "Error", "equalSizes", "first", "second"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/fancy-canvas/size.mjs"], "sourcesContent": ["export function size(_a) {\n    var width = _a.width, height = _a.height;\n    if (width < 0) {\n        throw new Error('Negative width is not allowed for Size');\n    }\n    if (height < 0) {\n        throw new Error('Negative height is not allowed for Size');\n    }\n    return {\n        width: width,\n        height: height,\n    };\n}\nexport function equalSizes(first, second) {\n    return (first.width === second.width) &&\n        (first.height === second.height);\n}\n"], "mappings": "AAAA,OAAO,SAASA,IAAIA,CAACC,EAAE,EAAE;EACrB,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;IAAEC,MAAM,GAAGF,EAAE,CAACE,MAAM;EACxC,IAAID,KAAK,GAAG,CAAC,EAAE;IACX,MAAM,IAAIE,KAAK,CAAC,wCAAwC,CAAC;EAC7D;EACA,IAAID,MAAM,GAAG,CAAC,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;EAC9D;EACA,OAAO;IACHF,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACZ,CAAC;AACL;AACA,OAAO,SAASE,UAAUA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACtC,OAAQD,KAAK,CAACJ,KAAK,KAAKK,MAAM,CAACL,KAAK,IAC/BI,KAAK,CAACH,MAAM,KAAKI,MAAM,CAACJ,MAAO;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}