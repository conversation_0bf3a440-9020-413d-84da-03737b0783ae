{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CornerLeftUp = createLucideIcon(\"CornerLeftUp\", [[\"polyline\", {\n  points: \"14 9 9 4 4 9\",\n  key: \"m9oyvo\"\n}], [\"path\", {\n  d: \"M20 20h-7a4 4 0 0 1-4-4V4\",\n  key: \"1blwi3\"\n}]]);\nexport { CornerLeftUp as default };", "map": {"version": 3, "names": ["CornerLeftUp", "createLucideIcon", "points", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\corner-left-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CornerLeftUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNCA5IDkgNCA0IDkiIC8+CiAgPHBhdGggZD0iTTIwIDIwaC03YTQgNCAwIDAgMS00LTRWNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/corner-left-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerLeftUp = createLucideIcon('CornerLeftUp', [\n  ['polyline', { points: '14 9 9 4 4 9', key: 'm9oyvo' }],\n  ['path', { d: 'M20 20h-7a4 4 0 0 1-4-4V4', key: '1blwi3' }],\n]);\n\nexport default CornerLeftUp;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,UAAY;EAAEC,MAAA,EAAQ,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}