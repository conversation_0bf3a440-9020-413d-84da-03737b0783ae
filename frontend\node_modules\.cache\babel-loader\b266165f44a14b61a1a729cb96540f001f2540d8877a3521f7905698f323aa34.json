{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAccordionActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionActions', slot);\n}\nconst accordionActionsClasses = generateUtilityClasses('MuiAccordionActions', ['root', 'spacing']);\nexport default accordionActionsClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getAccordionActionsUtilityClass", "slot", "accordionActionsClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/AccordionActions/accordionActionsClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAccordionActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionActions', slot);\n}\nconst accordionActionsClasses = generateUtilityClasses('MuiAccordionActions', ['root', 'spacing']);\nexport default accordionActionsClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOF,oBAAoB,CAAC,qBAAqB,EAAEE,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGJ,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAClG,eAAeI,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}