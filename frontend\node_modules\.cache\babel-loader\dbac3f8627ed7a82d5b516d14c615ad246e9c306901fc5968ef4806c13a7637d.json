{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import{motion,AnimatePresence}from'framer-motion';import{createChart}from'lightweight-charts';import{Send,TrendingUp}from'lucide-react';import SpaceBackground from'./SpaceBackground';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AtlasInterface=()=>{const[messages,setMessages]=useState([{id:1,type:'system',content:\"Certainly! Here is the latest stock quote for AAPL:\",timestamp:new Date()},{id:2,type:'stock-quote',symbol:'AAPL',price:149.36,change:1.32,changePercent:1.32,company:'Apple Inc.',chartData:null,// Will be generated when chart initializes\ntimestamp:new Date()}]);const[inputMessage,setInputMessage]=useState('');const[isTyping,setIsTyping]=useState(false);const chartRef=useRef(null);const chartInstanceRef=useRef(null);// Initialize chart when stock quote message is rendered\nuseEffect(()=>{const stockMessage=messages.find(msg=>msg.type==='stock-quote');if(stockMessage&&chartRef.current&&!chartInstanceRef.current){const chartData=stockMessage.chartData||generateMockChartData();initializeChart(chartData);}},[messages]);const initializeChart=data=>{if(chartInstanceRef.current){chartInstanceRef.current.remove();}const chart=createChart(chartRef.current,{width:chartRef.current.clientWidth||260,height:100,layout:{background:{color:'transparent'},textColor:'#67e8f9',fontSize:10},grid:{vertLines:{color:'rgba(6, 182, 212, 0.05)'},horzLines:{color:'rgba(6, 182, 212, 0.05)'}},crosshair:{mode:0},rightPriceScale:{borderColor:'rgba(6, 182, 212, 0.2)',textColor:'#67e8f9',visible:false},timeScale:{borderColor:'rgba(6, 182, 212, 0.2)',textColor:'#67e8f9',timeVisible:false,secondsVisible:false,visible:false},handleScroll:false,handleScale:false});const candlestickSeries=chart.addCandlestickSeries({upColor:'#10b981',downColor:'#ef4444',borderDownColor:'#ef4444',borderUpColor:'#10b981',wickDownColor:'#ef4444',wickUpColor:'#10b981'});candlestickSeries.setData(data);chartInstanceRef.current=chart;// Auto-fit content\nchart.timeScale().fitContent();// Handle resize\nconst resizeObserver=new ResizeObserver(entries=>{if(entries.length===0||entries[0].target!==chartRef.current)return;const{width}=entries[0].contentRect;chart.applyOptions({width});});if(chartRef.current){resizeObserver.observe(chartRef.current);}};const handleSendMessage=async()=>{if(!inputMessage.trim())return;const newMessage={id:Date.now(),type:'user',content:inputMessage,timestamp:new Date()};setMessages(prev=>[...prev,newMessage]);const messageToSend=inputMessage;setInputMessage('');setIsTyping(true);try{// Call the real Holly AI backend\nconst response=await fetch('/api/v1/holly/chat',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({message:messageToSend,user_context:{timestamp:new Date().toISOString(),session_id:'atlas_session',interface:'atlas'}})});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const data=await response.json();setIsTyping(false);// Create Holly's response message\nconst hollyResponse={id:Date.now()+1,type:'system',content:data.response||\"I'm having trouble processing that request right now.\",timestamp:new Date(),response_type:data.type,requires_action:data.requires_action,trading_plan:data.trading_plan,plan_id:data.plan_id,function_called:data.function_called};setMessages(prev=>[...prev,hollyResponse]);// If Holly provided a stock quote, add it as a separate message\nif(data.type==='stock_quote'&&data.trading_plan){const stockMessage={id:Date.now()+2,type:'stock-quote',symbol:data.trading_plan.symbol||'AAPL',price:data.trading_plan.current_price||149.36,change:data.trading_plan.price_change||1.32,changePercent:data.trading_plan.price_change_percent||1.32,company:data.trading_plan.company_name||'Apple Inc.',chartData:generateMockChartData(),timestamp:new Date()};setMessages(prev=>[...prev,stockMessage]);}else if(messageToSend.toLowerCase().includes('aapl')||messageToSend.toLowerCase().includes('apple')){// Show AAPL quote for demo purposes to match reference image\nconst stockMessage={id:Date.now()+2,type:'stock-quote',symbol:'AAPL',price:149.36,change:1.32,changePercent:1.32,company:'Apple Inc.',chartData:generateMockChartData(),timestamp:new Date()};setMessages(prev=>[...prev,stockMessage]);}}catch(error){console.error('Error calling Holly AI:',error);setIsTyping(false);const errorMessage={id:Date.now()+1,type:'system',content:\"I'm having trouble connecting to my AI brain right now. Please try again in a moment.\",timestamp:new Date()};setMessages(prev=>[...prev,errorMessage]);}};const handleKeyPress=e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendMessage();}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\",children:[/*#__PURE__*/_jsx(SpaceBackground,{}),/*#__PURE__*/_jsx(\"div\",{className:\"relative z-10 min-h-screen flex flex-col\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.8},className:\"flex-1 flex flex-col h-full\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.95},animate:{opacity:1,scale:1},transition:{duration:0.6,delay:0.2},className:\"atlas-card-fullscreen flex flex-col h-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-6 px-6 border-b border-cyan-500/20\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1\",children:\"A.T.L.A.S\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-cyan-300/70 text-sm\",children:\"Stock Analysis Chatbot\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-6 py-4 overflow-y-auto space-y-4\",children:[/*#__PURE__*/_jsx(AnimatePresence,{children:messages.map(message=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:0.3},children:[message.type==='system'&&/*#__PURE__*/_jsx(\"div\",{className:\"text-cyan-100/90 text-sm leading-relaxed\",children:message.content}),message.type==='user'&&/*#__PURE__*/_jsx(\"div\",{className:\"text-right\",children:/*#__PURE__*/_jsx(\"div\",{className:\"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm max-w-xs\",children:message.content})}),message.type==='stock-quote'&&/*#__PURE__*/_jsxs(\"div\",{className:\"stock-quote-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-white mb-1\",children:message.symbol}),/*#__PURE__*/_jsx(\"div\",{className:\"text-cyan-300/70 text-xs\",children:message.company})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-white\",children:message.price}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end text-xs text-green-400\",children:[/*#__PURE__*/_jsx(TrendingUp,{className:\"w-3 h-3 mr-1\"}),\"+\",message.changePercent,\"%\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"chart-container mb-4\",children:/*#__PURE__*/_jsx(\"div\",{ref:chartRef,className:\"w-full\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",children:\"Show earnings\"}),/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",children:\"Analyze trend\"})]})]})]},message.id))}),/*#__PURE__*/_jsx(AnimatePresence,{children:isTyping&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:\"flex items-center space-x-2 text-cyan-400\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",style:{animationDelay:'0.1s'}}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",style:{animationDelay:'0.2s'}})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"A.T.L.A.S is typing...\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-t border-cyan-500/20 bg-slate-900/50 backdrop-blur-sm\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:inputMessage,onChange:e=>setInputMessage(e.target.value),onKeyPress:handleKeyPress,placeholder:\"Send a message...\",className:\"atlas-input\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleSendMessage,className:\"atlas-send-btn\",children:/*#__PURE__*/_jsx(Send,{className:\"w-4 h-4 text-white\"})})]})})]})})})]});};// Generate mock chart data with upward trend like AAPL\nfunction generateMockChartData(){const data=[];let basePrice=145;const endPrice=149.36;const dataPoints=30;for(let i=0;i<dataPoints;i++){const time=Math.floor(Date.now()/1000)-(dataPoints-i)*1800;// 30-minute intervals\nconst progress=i/(dataPoints-1);// Create upward trend with some volatility\nconst trendPrice=basePrice+(endPrice-basePrice)*progress;const volatility=0.008;// Reduced volatility for smoother trend\nconst randomChange=(Math.random()-0.5)*volatility*trendPrice;const open=i===0?basePrice:data[i-1].close;const close=trendPrice+randomChange;const high=Math.max(open,close)+Math.random()*0.3;const low=Math.min(open,close)-Math.random()*0.2;data.push({time,open:parseFloat(open.toFixed(2)),high:parseFloat(high.toFixed(2)),low:parseFloat(low.toFixed(2)),close:parseFloat(close.toFixed(2))});}return data;}export default AtlasInterface;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "createChart", "Send", "TrendingUp", "SpaceBackground", "jsx", "_jsx", "jsxs", "_jsxs", "AtlasInterface", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "symbol", "price", "change", "changePercent", "company", "chartData", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "chartRef", "chartInstanceRef", "stockMessage", "find", "msg", "current", "generateMockChartData", "initializeChart", "data", "remove", "chart", "width", "clientWidth", "height", "layout", "background", "color", "textColor", "fontSize", "grid", "vertLines", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "visible", "timeScale", "timeVisible", "secondsVisible", "handleScroll", "handleScale", "candlestickSeries", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "setData", "<PERSON><PERSON><PERSON><PERSON>", "resizeObserver", "ResizeObserver", "entries", "length", "target", "contentRect", "applyOptions", "observe", "handleSendMessage", "trim", "newMessage", "now", "prev", "messageToSend", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "message", "user_context", "toISOString", "session_id", "interface", "ok", "Error", "concat", "status", "json", "hollyResponse", "response_type", "requires_action", "trading_plan", "plan_id", "function_called", "current_price", "price_change", "price_change_percent", "company_name", "toLowerCase", "includes", "error", "console", "errorMessage", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "delay", "map", "exit", "ref", "style", "animationDelay", "value", "onChange", "onKeyPress", "placeholder", "onClick", "basePrice", "endPrice", "dataPoints", "i", "time", "Math", "floor", "progress", "trendPrice", "volatility", "randomChange", "random", "open", "close", "high", "max", "low", "min", "push", "parseFloat", "toFixed"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AtlasInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { createChart } from 'lightweight-charts';\nimport { Send, TrendingUp } from 'lucide-react';\nimport SpaceBackground from './SpaceBackground';\n\nconst AtlasInterface = () => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: 'system',\n      content: \"Certainly! Here is the latest stock quote for AAPL:\",\n      timestamp: new Date()\n    },\n    {\n      id: 2,\n      type: 'stock-quote',\n      symbol: 'AAPL',\n      price: 149.36,\n      change: 1.32,\n      changePercent: 1.32,\n      company: 'Apple Inc.',\n      chartData: null, // Will be generated when chart initializes\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const chartRef = useRef(null);\n  const chartInstanceRef = useRef(null);\n\n  // Initialize chart when stock quote message is rendered\n  useEffect(() => {\n    const stockMessage = messages.find(msg => msg.type === 'stock-quote');\n    if (stockMessage && chartRef.current && !chartInstanceRef.current) {\n      const chartData = stockMessage.chartData || generateMockChartData();\n      initializeChart(chartData);\n    }\n  }, [messages]);\n\n  const initializeChart = (data) => {\n    if (chartInstanceRef.current) {\n      chartInstanceRef.current.remove();\n    }\n\n    const chart = createChart(chartRef.current, {\n      width: chartRef.current.clientWidth || 260,\n      height: 100,\n      layout: {\n        background: { color: 'transparent' },\n        textColor: '#67e8f9',\n        fontSize: 10,\n      },\n      grid: {\n        vertLines: { color: 'rgba(6, 182, 212, 0.05)' },\n        horzLines: { color: 'rgba(6, 182, 212, 0.05)' },\n      },\n      crosshair: {\n        mode: 0,\n      },\n      rightPriceScale: {\n        borderColor: 'rgba(6, 182, 212, 0.2)',\n        textColor: '#67e8f9',\n        visible: false,\n      },\n      timeScale: {\n        borderColor: 'rgba(6, 182, 212, 0.2)',\n        textColor: '#67e8f9',\n        timeVisible: false,\n        secondsVisible: false,\n        visible: false,\n      },\n      handleScroll: false,\n      handleScale: false,\n    });\n\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#10b981',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#10b981',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#10b981',\n    });\n\n    candlestickSeries.setData(data);\n    chartInstanceRef.current = chart;\n\n    // Auto-fit content\n    chart.timeScale().fitContent();\n\n    // Handle resize\n    const resizeObserver = new ResizeObserver(entries => {\n      if (entries.length === 0 || entries[0].target !== chartRef.current) return;\n      const { width } = entries[0].contentRect;\n      chart.applyOptions({ width });\n    });\n\n    if (chartRef.current) {\n      resizeObserver.observe(chartRef.current);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const newMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    const messageToSend = inputMessage;\n    setInputMessage('');\n    setIsTyping(true);\n\n    try {\n      // Call the real Holly AI backend\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'atlas_session',\n            interface: 'atlas'\n          }\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      setIsTyping(false);\n\n      // Create Holly's response message\n      const hollyResponse = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: data.response || \"I'm having trouble processing that request right now.\",\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n\n      setMessages(prev => [...prev, hollyResponse]);\n\n      // If Holly provided a stock quote, add it as a separate message\n      if (data.type === 'stock_quote' && data.trading_plan) {\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: data.trading_plan.symbol || 'AAPL',\n          price: data.trading_plan.current_price || 149.36,\n          change: data.trading_plan.price_change || 1.32,\n          changePercent: data.trading_plan.price_change_percent || 1.32,\n          company: data.trading_plan.company_name || 'Apple Inc.',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      } else if (messageToSend.toLowerCase().includes('aapl') || messageToSend.toLowerCase().includes('apple')) {\n        // Show AAPL quote for demo purposes to match reference image\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: 'AAPL',\n          price: 149.36,\n          change: 1.32,\n          changePercent: 1.32,\n          company: 'Apple Inc.',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      }\n\n    } catch (error) {\n      console.error('Error calling Holly AI:', error);\n      setIsTyping(false);\n\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: \"I'm having trouble connecting to my AI brain right now. Please try again in a moment.\",\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\">\n      <SpaceBackground />\n\n      {/* Main Container - Full Page */}\n      <div className=\"relative z-10 min-h-screen flex flex-col\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"flex-1 flex flex-col h-full\"\n        >\n          {/* Main Chat Interface - Full Height */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"atlas-card-fullscreen flex flex-col h-full\"\n          >\n            {/* Header */}\n            <div className=\"text-center py-6 px-6 border-b border-cyan-500/20\">\n              <h1 className=\"text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1\">\n                A.T.L.A.S\n              </h1>\n              <p className=\"text-cyan-300/70 text-sm\">\n                Stock Analysis Chatbot\n              </p>\n            </div>\n\n            {/* Messages Container - Scrollable */}\n            <div className=\"flex-1 px-6 py-4 overflow-y-auto space-y-4\"\n          >\n              <AnimatePresence>\n                {messages.map((message) => (\n                  <motion.div\n                    key={message.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    {message.type === 'system' && (\n                      <div className=\"text-cyan-100/90 text-sm leading-relaxed\">\n                        {message.content}\n                      </div>\n                    )}\n\n                    {message.type === 'user' && (\n                      <div className=\"text-right\">\n                        <div className=\"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm max-w-xs\">\n                          {message.content}\n                        </div>\n                      </div>\n                    )}\n\n                    {message.type === 'stock-quote' && (\n                      <div className=\"stock-quote-card\">\n                        {/* Stock Header */}\n                        <div className=\"flex items-start justify-between mb-4\">\n                          <div>\n                            <div className=\"text-xl font-bold text-white mb-1\">\n                              {message.symbol}\n                            </div>\n                            <div className=\"text-cyan-300/70 text-xs\">\n                              {message.company}\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-xl font-bold text-white\">\n                              {message.price}\n                            </div>\n                            <div className=\"flex items-center justify-end text-xs text-green-400\">\n                              <TrendingUp className=\"w-3 h-3 mr-1\" />\n                              +{message.changePercent}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Chart */}\n                        <div className=\"chart-container mb-4\">\n                          <div ref={chartRef} className=\"w-full\" />\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex gap-2\">\n                          <button className=\"action-btn\">\n                            Show earnings\n                          </button>\n                          <button className=\"action-btn\">\n                            Analyze trend\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n\n              {/* Typing Indicator */}\n              <AnimatePresence>\n                {isTyping && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"flex items-center space-x-2 text-cyan-400\"\n                  >\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                    </div>\n                    <span className=\"text-sm\">A.T.L.A.S is typing...</span>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Input Area - Fixed at Bottom */}\n            <div className=\"px-6 py-4 border-t border-cyan-500/20 bg-slate-900/50 backdrop-blur-sm\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  value={inputMessage}\n                  onChange={(e) => setInputMessage(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  placeholder=\"Send a message...\"\n                  className=\"atlas-input\"\n                />\n                <button\n                  onClick={handleSendMessage}\n                  className=\"atlas-send-btn\"\n                >\n                  <Send className=\"w-4 h-4 text-white\" />\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\n// Generate mock chart data with upward trend like AAPL\nfunction generateMockChartData() {\n  const data = [];\n  let basePrice = 145;\n  const endPrice = 149.36;\n  const dataPoints = 30;\n\n  for (let i = 0; i < dataPoints; i++) {\n    const time = Math.floor(Date.now() / 1000) - (dataPoints - i) * 1800; // 30-minute intervals\n    const progress = i / (dataPoints - 1);\n\n    // Create upward trend with some volatility\n    const trendPrice = basePrice + (endPrice - basePrice) * progress;\n    const volatility = 0.008; // Reduced volatility for smoother trend\n    const randomChange = (Math.random() - 0.5) * volatility * trendPrice;\n\n    const open = i === 0 ? basePrice : data[i - 1].close;\n    const close = trendPrice + randomChange;\n    const high = Math.max(open, close) + Math.random() * 0.3;\n    const low = Math.min(open, close) - Math.random() * 0.2;\n\n    data.push({\n      time,\n      open: parseFloat(open.toFixed(2)),\n      high: parseFloat(high.toFixed(2)),\n      low: parseFloat(low.toFixed(2)),\n      close: parseFloat(close.toFixed(2)),\n    });\n  }\n\n  return data;\n}\n\nexport default AtlasInterface;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,WAAW,KAAQ,oBAAoB,CAChD,OAASC,IAAI,CAAEC,UAAU,KAAQ,cAAc,CAC/C,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGf,QAAQ,CAAC,CACvC,CACEgB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,qDAAqD,CAC9DC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBI,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,IAAI,CACZC,aAAa,CAAE,IAAI,CACnBC,OAAO,CAAE,YAAY,CACrBC,SAAS,CAAE,IAAI,CAAE;AACjBP,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACF,CAAC,CACF,KAAM,CAACO,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAA+B,QAAQ,CAAG7B,MAAM,CAAC,IAAI,CAAC,CAC7B,KAAM,CAAA8B,gBAAgB,CAAG9B,MAAM,CAAC,IAAI,CAAC,CAErC;AACAD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgC,YAAY,CAAGnB,QAAQ,CAACoB,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAClB,IAAI,GAAK,aAAa,CAAC,CACrE,GAAIgB,YAAY,EAAIF,QAAQ,CAACK,OAAO,EAAI,CAACJ,gBAAgB,CAACI,OAAO,CAAE,CACjE,KAAM,CAAAV,SAAS,CAAGO,YAAY,CAACP,SAAS,EAAIW,qBAAqB,CAAC,CAAC,CACnEC,eAAe,CAACZ,SAAS,CAAC,CAC5B,CACF,CAAC,CAAE,CAACZ,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAwB,eAAe,CAAIC,IAAI,EAAK,CAChC,GAAIP,gBAAgB,CAACI,OAAO,CAAE,CAC5BJ,gBAAgB,CAACI,OAAO,CAACI,MAAM,CAAC,CAAC,CACnC,CAEA,KAAM,CAAAC,KAAK,CAAGpC,WAAW,CAAC0B,QAAQ,CAACK,OAAO,CAAE,CAC1CM,KAAK,CAAEX,QAAQ,CAACK,OAAO,CAACO,WAAW,EAAI,GAAG,CAC1CC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,CACNC,UAAU,CAAE,CAAEC,KAAK,CAAE,aAAc,CAAC,CACpCC,SAAS,CAAE,SAAS,CACpBC,QAAQ,CAAE,EACZ,CAAC,CACDC,IAAI,CAAE,CACJC,SAAS,CAAE,CAAEJ,KAAK,CAAE,yBAA0B,CAAC,CAC/CK,SAAS,CAAE,CAAEL,KAAK,CAAE,yBAA0B,CAChD,CAAC,CACDM,SAAS,CAAE,CACTC,IAAI,CAAE,CACR,CAAC,CACDC,eAAe,CAAE,CACfC,WAAW,CAAE,wBAAwB,CACrCR,SAAS,CAAE,SAAS,CACpBS,OAAO,CAAE,KACX,CAAC,CACDC,SAAS,CAAE,CACTF,WAAW,CAAE,wBAAwB,CACrCR,SAAS,CAAE,SAAS,CACpBW,WAAW,CAAE,KAAK,CAClBC,cAAc,CAAE,KAAK,CACrBH,OAAO,CAAE,KACX,CAAC,CACDI,YAAY,CAAE,KAAK,CACnBC,WAAW,CAAE,KACf,CAAC,CAAC,CAEF,KAAM,CAAAC,iBAAiB,CAAGtB,KAAK,CAACuB,oBAAoB,CAAC,CACnDC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,SAAS,CACpBC,eAAe,CAAE,SAAS,CAC1BC,aAAa,CAAE,SAAS,CACxBC,aAAa,CAAE,SAAS,CACxBC,WAAW,CAAE,SACf,CAAC,CAAC,CAEFP,iBAAiB,CAACQ,OAAO,CAAChC,IAAI,CAAC,CAC/BP,gBAAgB,CAACI,OAAO,CAAGK,KAAK,CAEhC;AACAA,KAAK,CAACiB,SAAS,CAAC,CAAC,CAACc,UAAU,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAAC,cAAc,CAACC,OAAO,EAAI,CACnD,GAAIA,OAAO,CAACC,MAAM,GAAK,CAAC,EAAID,OAAO,CAAC,CAAC,CAAC,CAACE,MAAM,GAAK9C,QAAQ,CAACK,OAAO,CAAE,OACpE,KAAM,CAAEM,KAAM,CAAC,CAAGiC,OAAO,CAAC,CAAC,CAAC,CAACG,WAAW,CACxCrC,KAAK,CAACsC,YAAY,CAAC,CAAErC,KAAM,CAAC,CAAC,CAC/B,CAAC,CAAC,CAEF,GAAIX,QAAQ,CAACK,OAAO,CAAE,CACpBqC,cAAc,CAACO,OAAO,CAACjD,QAAQ,CAACK,OAAO,CAAC,CAC1C,CACF,CAAC,CAED,KAAM,CAAA6C,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACtD,YAAY,CAACuD,IAAI,CAAC,CAAC,CAAE,OAE1B,KAAM,CAAAC,UAAU,CAAG,CACjBnE,EAAE,CAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,CACdnE,IAAI,CAAE,MAAM,CACZC,OAAO,CAAES,YAAY,CACrBR,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CAEDL,WAAW,CAACsE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEF,UAAU,CAAC,CAAC,CAC1C,KAAM,CAAAG,aAAa,CAAG3D,YAAY,CAClCC,eAAe,CAAC,EAAE,CAAC,CACnBE,WAAW,CAAC,IAAI,CAAC,CAEjB,GAAI,CACF;AACA,KAAM,CAAAyD,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,oBAAoB,CAAE,CACjDC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,OAAO,CAAER,aAAa,CACtBS,YAAY,CAAE,CACZ5E,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC4E,WAAW,CAAC,CAAC,CACnCC,UAAU,CAAE,eAAe,CAC3BC,SAAS,CAAE,OACb,CACF,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACX,QAAQ,CAACY,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,wBAAAC,MAAA,CAAwBd,QAAQ,CAACe,MAAM,CAAE,CAAC,CAC3D,CAEA,KAAM,CAAA/D,IAAI,CAAG,KAAM,CAAAgD,QAAQ,CAACgB,IAAI,CAAC,CAAC,CAClCzE,WAAW,CAAC,KAAK,CAAC,CAElB;AACA,KAAM,CAAA0E,aAAa,CAAG,CACpBxF,EAAE,CAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBnE,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAEqB,IAAI,CAACgD,QAAQ,EAAI,uDAAuD,CACjFpE,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBqF,aAAa,CAAElE,IAAI,CAACtB,IAAI,CACxByF,eAAe,CAAEnE,IAAI,CAACmE,eAAe,CACrCC,YAAY,CAAEpE,IAAI,CAACoE,YAAY,CAC/BC,OAAO,CAAErE,IAAI,CAACqE,OAAO,CACrBC,eAAe,CAAEtE,IAAI,CAACsE,eACxB,CAAC,CAED9F,WAAW,CAACsE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEmB,aAAa,CAAC,CAAC,CAE7C;AACA,GAAIjE,IAAI,CAACtB,IAAI,GAAK,aAAa,EAAIsB,IAAI,CAACoE,YAAY,CAAE,CACpD,KAAM,CAAA1E,YAAY,CAAG,CACnBjB,EAAE,CAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBnE,IAAI,CAAE,aAAa,CACnBI,MAAM,CAAEkB,IAAI,CAACoE,YAAY,CAACtF,MAAM,EAAI,MAAM,CAC1CC,KAAK,CAAEiB,IAAI,CAACoE,YAAY,CAACG,aAAa,EAAI,MAAM,CAChDvF,MAAM,CAAEgB,IAAI,CAACoE,YAAY,CAACI,YAAY,EAAI,IAAI,CAC9CvF,aAAa,CAAEe,IAAI,CAACoE,YAAY,CAACK,oBAAoB,EAAI,IAAI,CAC7DvF,OAAO,CAAEc,IAAI,CAACoE,YAAY,CAACM,YAAY,EAAI,YAAY,CACvDvF,SAAS,CAAEW,qBAAqB,CAAC,CAAC,CAClClB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACDL,WAAW,CAACsE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEpD,YAAY,CAAC,CAAC,CAC9C,CAAC,IAAM,IAAIqD,aAAa,CAAC4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAI7B,aAAa,CAAC4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAE,CACxG;AACA,KAAM,CAAAlF,YAAY,CAAG,CACnBjB,EAAE,CAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBnE,IAAI,CAAE,aAAa,CACnBI,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,IAAI,CACZC,aAAa,CAAE,IAAI,CACnBC,OAAO,CAAE,YAAY,CACrBC,SAAS,CAAEW,qBAAqB,CAAC,CAAC,CAClClB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACDL,WAAW,CAACsE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEpD,YAAY,CAAC,CAAC,CAC9C,CAEF,CAAE,MAAOmF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CtF,WAAW,CAAC,KAAK,CAAC,CAElB,KAAM,CAAAwF,YAAY,CAAG,CACnBtG,EAAE,CAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBnE,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,uFAAuF,CAChGC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACDL,WAAW,CAACsE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEiC,YAAY,CAAC,CAAC,CAC9C,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIC,CAAC,EAAK,CAC5B,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,EAAI,CAACD,CAAC,CAACE,QAAQ,CAAE,CACpCF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClB1C,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAED,mBACErE,KAAA,QAAKgH,SAAS,CAAC,mGAAmG,CAAAC,QAAA,eAChHnH,IAAA,CAACF,eAAe,GAAE,CAAC,cAGnBE,IAAA,QAAKkH,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDnH,IAAA,CAACP,MAAM,CAAC2H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BR,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAGvCjH,KAAA,CAACT,MAAM,CAAC2H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEK,KAAK,CAAE,IAAK,CAAE,CACrCH,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEK,KAAK,CAAE,CAAE,CAAE,CAClCF,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEE,KAAK,CAAE,GAAI,CAAE,CAC1CV,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAGtDjH,KAAA,QAAKgH,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEnH,IAAA,OAAIkH,SAAS,CAAC,4GAA4G,CAAAC,QAAA,CAAC,WAE3H,CAAI,CAAC,cACLnH,IAAA,MAAGkH,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,wBAExC,CAAG,CAAC,EACD,CAAC,cAGNjH,KAAA,QAAKgH,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAEzDnH,IAAA,CAACN,eAAe,EAAAyH,QAAA,CACb/G,QAAQ,CAACyH,GAAG,CAAEzC,OAAO,eACpBlF,KAAA,CAACT,MAAM,CAAC2H,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BO,IAAI,CAAE,CAAER,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,EAE7B/B,OAAO,CAAC7E,IAAI,GAAK,QAAQ,eACxBP,IAAA,QAAKkH,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CACtD/B,OAAO,CAAC5E,OAAO,CACb,CACN,CAEA4E,OAAO,CAAC7E,IAAI,GAAK,MAAM,eACtBP,IAAA,QAAKkH,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBnH,IAAA,QAAKkH,SAAS,CAAC,2GAA2G,CAAAC,QAAA,CACvH/B,OAAO,CAAC5E,OAAO,CACb,CAAC,CACH,CACN,CAEA4E,OAAO,CAAC7E,IAAI,GAAK,aAAa,eAC7BL,KAAA,QAAKgH,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE/BjH,KAAA,QAAKgH,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjH,KAAA,QAAAiH,QAAA,eACEnH,IAAA,QAAKkH,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C/B,OAAO,CAACzE,MAAM,CACZ,CAAC,cACNX,IAAA,QAAKkH,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtC/B,OAAO,CAACrE,OAAO,CACb,CAAC,EACH,CAAC,cACNb,KAAA,QAAKgH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnH,IAAA,QAAKkH,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAC1C/B,OAAO,CAACxE,KAAK,CACX,CAAC,cACNV,KAAA,QAAKgH,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEnH,IAAA,CAACH,UAAU,EAACqH,SAAS,CAAC,cAAc,CAAE,CAAC,IACtC,CAAC9B,OAAO,CAACtE,aAAa,CAAC,GAC1B,EAAK,CAAC,EACH,CAAC,EACH,CAAC,cAGNd,IAAA,QAAKkH,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCnH,IAAA,QAAK+H,GAAG,CAAE1G,QAAS,CAAC6F,SAAS,CAAC,QAAQ,CAAE,CAAC,CACtC,CAAC,cAGNhH,KAAA,QAAKgH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnH,IAAA,WAAQkH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,eAE/B,CAAQ,CAAC,cACTnH,IAAA,WAAQkH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,eAE/B,CAAQ,CAAC,EACN,CAAC,EACH,CACN,GA1DI/B,OAAO,CAAC9E,EA2DH,CACb,CAAC,CACa,CAAC,cAGlBN,IAAA,CAACN,eAAe,EAAAyH,QAAA,CACbhG,QAAQ,eACPjB,KAAA,CAACT,MAAM,CAAC2H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BO,IAAI,CAAE,CAAER,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BL,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eAErDjH,KAAA,QAAKgH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnH,IAAA,QAAKkH,SAAS,CAAC,iDAAiD,CAAE,CAAC,cACnElH,IAAA,QAAKkH,SAAS,CAAC,iDAAiD,CAACc,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAE,CAAC,cACtGjI,IAAA,QAAKkH,SAAS,CAAC,iDAAiD,CAACc,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAE,CAAC,EACnG,CAAC,cACNjI,IAAA,SAAMkH,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,wBAAsB,CAAM,CAAC,EAC7C,CACb,CACc,CAAC,EACf,CAAC,cAGNnH,IAAA,QAAKkH,SAAS,CAAC,wEAAwE,CAAAC,QAAA,cACrFjH,KAAA,QAAKgH,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBnH,IAAA,UACEO,IAAI,CAAC,MAAM,CACX2H,KAAK,CAAEjH,YAAa,CACpBkH,QAAQ,CAAGrB,CAAC,EAAK5F,eAAe,CAAC4F,CAAC,CAAC3C,MAAM,CAAC+D,KAAK,CAAE,CACjDE,UAAU,CAAEvB,cAAe,CAC3BwB,WAAW,CAAC,mBAAmB,CAC/BnB,SAAS,CAAC,aAAa,CACxB,CAAC,cACFlH,IAAA,WACEsI,OAAO,CAAE/D,iBAAkB,CAC3B2C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAE1BnH,IAAA,CAACJ,IAAI,EAACsH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACjC,CAAC,EACN,CAAC,CACH,CAAC,EACI,CAAC,CACH,CAAC,CACV,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,QAAS,CAAAvF,qBAAqBA,CAAA,CAAG,CAC/B,KAAM,CAAAE,IAAI,CAAG,EAAE,CACf,GAAI,CAAA0G,SAAS,CAAG,GAAG,CACnB,KAAM,CAAAC,QAAQ,CAAG,MAAM,CACvB,KAAM,CAAAC,UAAU,CAAG,EAAE,CAErB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,UAAU,CAAEC,CAAC,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACnI,IAAI,CAACgE,GAAG,CAAC,CAAC,CAAG,IAAI,CAAC,CAAG,CAAC+D,UAAU,CAAGC,CAAC,EAAI,IAAI,CAAE;AACtE,KAAM,CAAAI,QAAQ,CAAGJ,CAAC,EAAID,UAAU,CAAG,CAAC,CAAC,CAErC;AACA,KAAM,CAAAM,UAAU,CAAGR,SAAS,CAAG,CAACC,QAAQ,CAAGD,SAAS,EAAIO,QAAQ,CAChE,KAAM,CAAAE,UAAU,CAAG,KAAK,CAAE;AAC1B,KAAM,CAAAC,YAAY,CAAG,CAACL,IAAI,CAACM,MAAM,CAAC,CAAC,CAAG,GAAG,EAAIF,UAAU,CAAGD,UAAU,CAEpE,KAAM,CAAAI,IAAI,CAAGT,CAAC,GAAK,CAAC,CAAGH,SAAS,CAAG1G,IAAI,CAAC6G,CAAC,CAAG,CAAC,CAAC,CAACU,KAAK,CACpD,KAAM,CAAAA,KAAK,CAAGL,UAAU,CAAGE,YAAY,CACvC,KAAM,CAAAI,IAAI,CAAGT,IAAI,CAACU,GAAG,CAACH,IAAI,CAAEC,KAAK,CAAC,CAAGR,IAAI,CAACM,MAAM,CAAC,CAAC,CAAG,GAAG,CACxD,KAAM,CAAAK,GAAG,CAAGX,IAAI,CAACY,GAAG,CAACL,IAAI,CAAEC,KAAK,CAAC,CAAGR,IAAI,CAACM,MAAM,CAAC,CAAC,CAAG,GAAG,CAEvDrH,IAAI,CAAC4H,IAAI,CAAC,CACRd,IAAI,CACJQ,IAAI,CAAEO,UAAU,CAACP,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC,CACjCN,IAAI,CAAEK,UAAU,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,CACjCJ,GAAG,CAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BP,KAAK,CAAEM,UAAU,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CACpC,CAAC,CAAC,CACJ,CAEA,MAAO,CAAA9H,IAAI,CACb,CAEA,cAAe,CAAA1B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}