{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m12.58 9.75-.87-.87.23-.66h.1zm-2.23-2.23.2-.52c.23-.6.8-1 1.45-1s1.22.4 1.45 1l2.17 5.79L22 19.17V4c0-1.1-.9-2-2-2H4.83zm10.84 15.09c-.39.39-1.02.39-1.41 0l-.61-.61H4c-1.1 0-2-.9-2-2V4.83l-.61-.61a.9959.9959 0 0 1 0-1.41c.39-.39 1.02-.39 1.41 0l18.38 18.38c.4.39.4 1.03.01 1.42m-9.09-7.68-3.3-3.3-1.9 5.07c-.23.63.23 1.3.9 1.3h.01c.41 0 .77-.26.9-.64l.86-2.43z\"\n}), 'FontDownloadOffRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/FontDownloadOffRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m12.58 9.75-.87-.87.23-.66h.1zm-2.23-2.23.2-.52c.23-.6.8-1 1.45-1s1.22.4 1.45 1l2.17 5.79L22 19.17V4c0-1.1-.9-2-2-2H4.83zm10.84 15.09c-.39.39-1.02.39-1.41 0l-.61-.61H4c-1.1 0-2-.9-2-2V4.83l-.61-.61a.9959.9959 0 0 1 0-1.41c.39-.39 1.02-.39 1.41 0l18.38 18.38c.4.39.4 1.03.01 1.42m-9.09-7.68-3.3-3.3-1.9 5.07c-.23.63.23 1.3.9 1.3h.01c.41 0 .77-.26.9-.64l.86-2.43z\"\n}), 'FontDownloadOffRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,wBAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}