#!/usr/bin/env python3
"""
Quick Response Test - See actual A.T.L.A.S. responses
"""

import requests
import json

def test_responses():
    base_url = "http://localhost:8080"
    
    # Key test prompts from your list
    prompts = [
        "What's Apple trading at right now?",
        "Are there any stocks that look like they might jump soon?", 
        "Why did Tesla move up today? What's the news behind it?",
        "Where do you think Microsoft will be in five days?",
        "Can you suggest an options trade on NVIDIA that could make me money this week?",
        "I want to make $100 by tomorrow—what trade should I place?",
        "Please buy 10 shares of Amazon for me now.",
        "Alert me when Google goes up more than 2% today.",
        "How can I protect my Tesla shares if they start falling?",
        "Can you optimize my portfolio to boost returns?",
        "How risky is buying 20 shares of Shopify right now?",
        "Give me a quick morning market briefing for today.",
        "Show me any unusual options activity in Netflix.",
        "Backtest a simple breakout strategy on the S&P 500 over the last month.",
        "Which forex pair could earn me $50 today?",
        "Alert me when the VIX index rises above 20.",
        "What's the best ETF for steady growth this month?",
        "Help me make $200 this week with minimal trades.",
        "Do any cryptocurrencies look good right now? If so, buy 1 ETH."
    ]
    
    print("🚀 A.T.L.A.S. RESPONSE TEST")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n[{i:2d}] PROMPT: {prompt}")
        print("-" * 80)
        
        try:
            response = requests.post(
                f"{base_url}/api/v1/chat",
                json={
                    "message": prompt,
                    "session_id": f"test_{i}",
                    "context": {"test_mode": True}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get("response", "")
                
                print("RESPONSE:")
                print(response_text)
                print()
                
                # Quick evaluation (no branding required)
                has_prices = "$" in response_text
                has_trading_terms = any(word in response_text.lower() for word in ["buy", "sell", "trade", "target", "entry", "stop", "confidence"])
                has_specific_data = any(word in response_text.lower() for word in ["target:", "entry:", "stop:", "confidence:"])
                is_substantial = len(response_text) > 50
                no_limitations = not any(phrase in response_text.lower() for phrase in ["i can't", "i don't have", "i'm unable", "as an ai", "i cannot"])
                
                score = sum([has_prices, has_trading_terms, has_specific_data, is_substantial, no_limitations])
                
                print("EVALUATION:")
                print(f"  💰 Has prices: {'✅' if has_prices else '❌'}")
                print(f"  📈 Trading terms: {'✅' if has_trading_terms else '❌'}")
                print(f"  🎯 Specific data: {'✅' if has_specific_data else '❌'}")
                print(f"  📝 Substantial response: {'✅' if is_substantial else '❌'}")
                print(f"  🚫 No AI limitations: {'✅' if no_limitations else '❌'}")
                print(f"  📊 Score: {score}/5")
                
                if score >= 3:
                    passed += 1
                    print("  ✅ PASSED")
                else:
                    failed += 1
                    print("  ❌ FAILED")
                    
            else:
                failed += 1
                print(f"❌ HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            failed += 1
            print(f"❌ Request failed: {e}")
        
        print("=" * 80)
    
    # Final summary
    total = len(prompts)
    pass_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"   Total Tests: {total}")
    print(f"   Passed: {passed} ✅")
    print(f"   Failed: {failed} ❌")
    print(f"   Pass Rate: {pass_rate:.1f}%")
    
    if pass_rate >= 80:
        print(f"\n🏆 EXCELLENT: A.T.L.A.S. is delivering strong trading responses!")
    elif pass_rate >= 60:
        print(f"\n✅ GOOD: A.T.L.A.S. is performing well with room for improvement")
    else:
        print(f"\n⚠️ NEEDS WORK: A.T.L.A.S. requires improvements")

if __name__ == "__main__":
    test_responses()
