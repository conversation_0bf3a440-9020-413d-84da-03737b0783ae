{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogContentUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogContent', slot);\n}\nconst dialogContentClasses = generateUtilityClasses('MuiDialogContent', ['root', 'dividers']);\nexport default dialogContentClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getDialogContentUtilityClass", "slot", "dialogContentClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/DialogContent/dialogContentClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogContentUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogContent', slot);\n}\nconst dialogContentClasses = generateUtilityClasses('MuiDialogContent', ['root', 'dividers']);\nexport default dialogContentClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGJ,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC7F,eAAeI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}