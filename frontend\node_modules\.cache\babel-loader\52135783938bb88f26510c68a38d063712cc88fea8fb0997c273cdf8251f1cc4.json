{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M15.64 11.02c.55-1.47 1.43-2.78 2.56-3.83.38-.36.04-1-.46-.85-3.32.98-5.75 4.05-5.74 7.69.95-1.28 2.2-2.31 3.64-3.01m-4.22-2.17c-.6-1.56-1.63-2.91-2.96-3.87-.42-.3-.96.19-.72.65C8.54 7.15 9 8.88 9 10.71c0 .21-.03.41-.04.61.43.24.83.52 1.22.82.21-1.18.65-2.29 1.24-3.29M12 20H3c-.55 0-1-.45-1-1s.45-1 1-1h4.75c-.57-2.19-2.04-4.02-4-5.06-.16-.08-.26-.25-.26-.44 0-.27.22-.49.49-.5H4c4.42 0 8 3.58 8 8m8.26-7.06c-1.96 1.04-3.44 2.87-4 5.06H21c.55 0 1 .45 1 1s-.45 1-1 1h-7c0-.68-.07-1.35-.2-2-.15-.72-.38-1.42-.67-2.07C14.52 13.58 17.07 12 20 12h.02c.27 0 .49.23.49.5.01.19-.1.35-.25.44\"\n}), 'GrassRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/GrassRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M15.64 11.02c.55-1.47 1.43-2.78 2.56-3.83.38-.36.04-1-.46-.85-3.32.98-5.75 4.05-5.74 7.69.95-1.28 2.2-2.31 3.64-3.01m-4.22-2.17c-.6-1.56-1.63-2.91-2.96-3.87-.42-.3-.96.19-.72.65C8.54 7.15 9 8.88 9 10.71c0 .21-.03.41-.04.61.43.24.83.52 1.22.82.21-1.18.65-2.29 1.24-3.29M12 20H3c-.55 0-1-.45-1-1s.45-1 1-1h4.75c-.57-2.19-2.04-4.02-4-5.06-.16-.08-.26-.25-.26-.44 0-.27.22-.49.49-.5H4c4.42 0 8 3.58 8 8m8.26-7.06c-1.96 1.04-3.44 2.87-4 5.06H21c.55 0 1 .45 1 1s-.45 1-1 1h-7c0-.68-.07-1.35-.2-2-.15-.72-.38-1.42-.67-2.07C14.52 13.58 17.07 12 20 12h.02c.27 0 .49.23.49.5.01.19-.1.35-.25.44\"\n}), 'GrassRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}