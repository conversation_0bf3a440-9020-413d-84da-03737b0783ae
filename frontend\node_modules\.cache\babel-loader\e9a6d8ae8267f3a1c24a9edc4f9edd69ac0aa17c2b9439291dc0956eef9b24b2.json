{"ast": null, "code": "'use client';\n\nexport { default } from './AlertTitle';\nexport { default as alertTitleClasses } from './alertTitleClasses';\nexport * from './alertTitleClasses';", "map": {"version": 3, "names": ["default", "alertTitleClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/AlertTitle/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './AlertTitle';\nexport { default as alertTitleClasses } from './alertTitleClasses';\nexport * from './alertTitleClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,qBAAqB;AAClE,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}