#!/usr/bin/env python3
"""
Manual A.T.L.A.S. Test - See actual responses
"""

import requests
import json

def test_atlas_manually():
    print("🔗 Testing A.T.L.A.S. server at: http://localhost:8080")
    print("=" * 70)
    
    # Test prompts from your list
    test_prompts = [
        "What's Apple trading at right now?",
        "Are there any stocks that look like they might jump soon?", 
        "I want to make $100 by tomorrow—what trade should I place?",
        "Please buy 10 shares of Amazon for me now.",
        "Can you optimize my portfolio to boost returns?"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n[{i}] PROMPT: \"{prompt}\"")
        print("-" * 70)
        
        try:
            response = requests.post(
                "http://localhost:8080/api/v1/chat",
                json={
                    "message": prompt,
                    "session_id": f"manual_test_{i}",
                    "context": {"test_mode": True}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get("response", "")
                
                print("RESPONSE:")
                print(response_text)
                print()
                
                # Quick analysis
                has_atlas = "a.t.l.a.s" in response_text.lower()
                has_predicto = "predicto" in response_text.lower()
                has_prices = "$" in response_text
                has_trading = any(word in response_text.lower() for word in ["buy", "sell", "trade", "target", "entry", "stop"])
                has_confidence = "confidence" in response_text.lower()
                
                print("ANALYSIS:")
                print(f"  ✅ A.T.L.A.S branding: {has_atlas}")
                print(f"  ✅ Predicto branding: {has_predicto}")
                print(f"  ✅ Price data: {has_prices}")
                print(f"  ✅ Trading terms: {has_trading}")
                print(f"  ✅ Confidence levels: {has_confidence}")
                
                score = sum([has_atlas, has_predicto, has_prices, has_trading, has_confidence])
                print(f"  📊 Score: {score}/5")
                
            else:
                print(f"❌ HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
        
        print("=" * 70)

if __name__ == "__main__":
    test_atlas_manually()
