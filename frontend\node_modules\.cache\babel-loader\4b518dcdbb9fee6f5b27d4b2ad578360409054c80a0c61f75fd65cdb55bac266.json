{"ast": null, "code": "import capitalize from '@mui/utils/capitalize';\nimport merge from '../merge';\nimport { getPath, getStyleValue as getValue } from '../style';\nimport { handleBreakpoints, createEmptyBreakpointObject, removeUnusedBreakpoints } from '../breakpoints';\nimport defaultSxConfig from './defaultSxConfig';\nfunction objectsHaveSameKeys(...objects) {\n  const allKeys = objects.reduce((keys, object) => keys.concat(Object.keys(object)), []);\n  const union = new Set(allKeys);\n  return objects.every(object => union.size === Object.keys(object).length);\n}\nfunction callIfFn(maybeFn, arg) {\n  return typeof maybeFn === 'function' ? maybeFn(arg) : maybeFn;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createStyleFunctionSx() {\n  function getThemeValue(prop, val, theme, config) {\n    const props = {\n      [prop]: val,\n      theme\n    };\n    const options = config[prop];\n    if (!options) {\n      return {\n        [prop]: val\n      };\n    }\n    const {\n      cssProperty = prop,\n      themeKey,\n      transform,\n      style\n    } = options;\n    if (val == null) {\n      return null;\n    }\n\n    // TODO v6: remove, see https://github.com/mui/material-ui/pull/38123\n    if (themeKey === 'typography' && val === 'inherit') {\n      return {\n        [prop]: val\n      };\n    }\n    const themeMapping = getPath(theme, themeKey) || {};\n    if (style) {\n      return style(props);\n    }\n    const styleFromPropValue = propValueFinal => {\n      let value = getValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, val, styleFromPropValue);\n  }\n  function styleFunctionSx(props) {\n    var _theme$unstable_sxCon;\n    const {\n      sx,\n      theme = {}\n    } = props || {};\n    if (!sx) {\n      return null; // Emotion & styled-components will neglect null\n    }\n    const config = (_theme$unstable_sxCon = theme.unstable_sxConfig) != null ? _theme$unstable_sxCon : defaultSxConfig;\n\n    /*\n     * Receive `sxInput` as object or callback\n     * and then recursively check keys & values to create media query object styles.\n     * (the result will be used in `styled`)\n     */\n    function traverse(sxInput) {\n      let sxObject = sxInput;\n      if (typeof sxInput === 'function') {\n        sxObject = sxInput(theme);\n      } else if (typeof sxInput !== 'object') {\n        // value\n        return sxInput;\n      }\n      if (!sxObject) {\n        return null;\n      }\n      const emptyBreakpoints = createEmptyBreakpointObject(theme.breakpoints);\n      const breakpointsKeys = Object.keys(emptyBreakpoints);\n      let css = emptyBreakpoints;\n      Object.keys(sxObject).forEach(styleKey => {\n        const value = callIfFn(sxObject[styleKey], theme);\n        if (value !== null && value !== undefined) {\n          if (typeof value === 'object') {\n            if (config[styleKey]) {\n              css = merge(css, getThemeValue(styleKey, value, theme, config));\n            } else {\n              const breakpointsValues = handleBreakpoints({\n                theme\n              }, value, x => ({\n                [styleKey]: x\n              }));\n              if (objectsHaveSameKeys(breakpointsValues, value)) {\n                css[styleKey] = styleFunctionSx({\n                  sx: value,\n                  theme\n                });\n              } else {\n                css = merge(css, breakpointsValues);\n              }\n            }\n          } else {\n            css = merge(css, getThemeValue(styleKey, value, theme, config));\n          }\n        }\n      });\n      return removeUnusedBreakpoints(breakpointsKeys, css);\n    }\n    return Array.isArray(sx) ? sx.map(traverse) : traverse(sx);\n  }\n  return styleFunctionSx;\n}\nconst styleFunctionSx = unstable_createStyleFunctionSx();\nstyleFunctionSx.filterProps = ['sx'];\nexport default styleFunctionSx;", "map": {"version": 3, "names": ["capitalize", "merge", "<PERSON><PERSON><PERSON>", "getStyleValue", "getValue", "handleBreakpoints", "createEmptyBreakpointObject", "removeUnusedBreakpoints", "defaultSxConfig", "objectsHaveSameKeys", "objects", "allKeys", "reduce", "keys", "object", "concat", "Object", "union", "Set", "every", "size", "length", "callIfFn", "maybeFn", "arg", "unstable_createStyleFunctionSx", "getThemeValue", "prop", "val", "theme", "config", "props", "options", "cssProperty", "<PERSON><PERSON><PERSON>", "transform", "style", "themeMapping", "styleFromPropValue", "propValueFinal", "value", "styleFunctionSx", "_theme$unstable_sxCon", "sx", "unstable_sxConfig", "traverse", "sxInput", "sxObject", "emptyBreakpoints", "breakpoints", "breakpointsKeys", "css", "for<PERSON>ach", "styleKey", "undefined", "breakpointsValues", "x", "Array", "isArray", "map", "filterProps"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/system/esm/styleFunctionSx/styleFunctionSx.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nimport merge from '../merge';\nimport { getPath, getStyleValue as getValue } from '../style';\nimport { handleBreakpoints, createEmptyBreakpointObject, removeUnusedBreakpoints } from '../breakpoints';\nimport defaultSxConfig from './defaultSxConfig';\nfunction objectsHaveSameKeys(...objects) {\n  const allKeys = objects.reduce((keys, object) => keys.concat(Object.keys(object)), []);\n  const union = new Set(allKeys);\n  return objects.every(object => union.size === Object.keys(object).length);\n}\nfunction callIfFn(maybeFn, arg) {\n  return typeof maybeFn === 'function' ? maybeFn(arg) : maybeFn;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createStyleFunctionSx() {\n  function getThemeValue(prop, val, theme, config) {\n    const props = {\n      [prop]: val,\n      theme\n    };\n    const options = config[prop];\n    if (!options) {\n      return {\n        [prop]: val\n      };\n    }\n    const {\n      cssProperty = prop,\n      themeKey,\n      transform,\n      style\n    } = options;\n    if (val == null) {\n      return null;\n    }\n\n    // TODO v6: remove, see https://github.com/mui/material-ui/pull/38123\n    if (themeKey === 'typography' && val === 'inherit') {\n      return {\n        [prop]: val\n      };\n    }\n    const themeMapping = getPath(theme, themeKey) || {};\n    if (style) {\n      return style(props);\n    }\n    const styleFromPropValue = propValueFinal => {\n      let value = getValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, val, styleFromPropValue);\n  }\n  function styleFunctionSx(props) {\n    var _theme$unstable_sxCon;\n    const {\n      sx,\n      theme = {}\n    } = props || {};\n    if (!sx) {\n      return null; // Emotion & styled-components will neglect null\n    }\n    const config = (_theme$unstable_sxCon = theme.unstable_sxConfig) != null ? _theme$unstable_sxCon : defaultSxConfig;\n\n    /*\n     * Receive `sxInput` as object or callback\n     * and then recursively check keys & values to create media query object styles.\n     * (the result will be used in `styled`)\n     */\n    function traverse(sxInput) {\n      let sxObject = sxInput;\n      if (typeof sxInput === 'function') {\n        sxObject = sxInput(theme);\n      } else if (typeof sxInput !== 'object') {\n        // value\n        return sxInput;\n      }\n      if (!sxObject) {\n        return null;\n      }\n      const emptyBreakpoints = createEmptyBreakpointObject(theme.breakpoints);\n      const breakpointsKeys = Object.keys(emptyBreakpoints);\n      let css = emptyBreakpoints;\n      Object.keys(sxObject).forEach(styleKey => {\n        const value = callIfFn(sxObject[styleKey], theme);\n        if (value !== null && value !== undefined) {\n          if (typeof value === 'object') {\n            if (config[styleKey]) {\n              css = merge(css, getThemeValue(styleKey, value, theme, config));\n            } else {\n              const breakpointsValues = handleBreakpoints({\n                theme\n              }, value, x => ({\n                [styleKey]: x\n              }));\n              if (objectsHaveSameKeys(breakpointsValues, value)) {\n                css[styleKey] = styleFunctionSx({\n                  sx: value,\n                  theme\n                });\n              } else {\n                css = merge(css, breakpointsValues);\n              }\n            }\n          } else {\n            css = merge(css, getThemeValue(styleKey, value, theme, config));\n          }\n        }\n      });\n      return removeUnusedBreakpoints(breakpointsKeys, css);\n    }\n    return Array.isArray(sx) ? sx.map(traverse) : traverse(sx);\n  }\n  return styleFunctionSx;\n}\nconst styleFunctionSx = unstable_createStyleFunctionSx();\nstyleFunctionSx.filterProps = ['sx'];\nexport default styleFunctionSx;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,OAAO,EAAEC,aAAa,IAAIC,QAAQ,QAAQ,UAAU;AAC7D,SAASC,iBAAiB,EAAEC,2BAA2B,EAAEC,uBAAuB,QAAQ,gBAAgB;AACxG,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,mBAAmBA,CAAC,GAAGC,OAAO,EAAE;EACvC,MAAMC,OAAO,GAAGD,OAAO,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,MAAM,KAAKD,IAAI,CAACE,MAAM,CAACC,MAAM,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF,MAAMG,KAAK,GAAG,IAAIC,GAAG,CAACP,OAAO,CAAC;EAC9B,OAAOD,OAAO,CAACS,KAAK,CAACL,MAAM,IAAIG,KAAK,CAACG,IAAI,KAAKJ,MAAM,CAACH,IAAI,CAACC,MAAM,CAAC,CAACO,MAAM,CAAC;AAC3E;AACA,SAASC,QAAQA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC9B,OAAO,OAAOD,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACC,GAAG,CAAC,GAAGD,OAAO;AAC/D;;AAEA;AACA,OAAO,SAASE,8BAA8BA,CAAA,EAAG;EAC/C,SAASC,aAAaA,CAACC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC/C,MAAMC,KAAK,GAAG;MACZ,CAACJ,IAAI,GAAGC,GAAG;MACXC;IACF,CAAC;IACD,MAAMG,OAAO,GAAGF,MAAM,CAACH,IAAI,CAAC;IAC5B,IAAI,CAACK,OAAO,EAAE;MACZ,OAAO;QACL,CAACL,IAAI,GAAGC;MACV,CAAC;IACH;IACA,MAAM;MACJK,WAAW,GAAGN,IAAI;MAClBO,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,GAAGJ,OAAO;IACX,IAAIJ,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI;IACb;;IAEA;IACA,IAAIM,QAAQ,KAAK,YAAY,IAAIN,GAAG,KAAK,SAAS,EAAE;MAClD,OAAO;QACL,CAACD,IAAI,GAAGC;MACV,CAAC;IACH;IACA,MAAMS,YAAY,GAAGnC,OAAO,CAAC2B,KAAK,EAAEK,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnD,IAAIE,KAAK,EAAE;MACT,OAAOA,KAAK,CAACL,KAAK,CAAC;IACrB;IACA,MAAMO,kBAAkB,GAAGC,cAAc,IAAI;MAC3C,IAAIC,KAAK,GAAGpC,QAAQ,CAACiC,YAAY,EAAEF,SAAS,EAAEI,cAAc,CAAC;MAC7D,IAAIA,cAAc,KAAKC,KAAK,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;QAClE;QACAC,KAAK,GAAGpC,QAAQ,CAACiC,YAAY,EAAEF,SAAS,EAAE,GAAGR,IAAI,GAAGY,cAAc,KAAK,SAAS,GAAG,EAAE,GAAGvC,UAAU,CAACuC,cAAc,CAAC,EAAE,EAAEA,cAAc,CAAC;MACvI;MACA,IAAIN,WAAW,KAAK,KAAK,EAAE;QACzB,OAAOO,KAAK;MACd;MACA,OAAO;QACL,CAACP,WAAW,GAAGO;MACjB,CAAC;IACH,CAAC;IACD,OAAOnC,iBAAiB,CAAC0B,KAAK,EAAEH,GAAG,EAAEU,kBAAkB,CAAC;EAC1D;EACA,SAASG,eAAeA,CAACV,KAAK,EAAE;IAC9B,IAAIW,qBAAqB;IACzB,MAAM;MACJC,EAAE;MACFd,KAAK,GAAG,CAAC;IACX,CAAC,GAAGE,KAAK,IAAI,CAAC,CAAC;IACf,IAAI,CAACY,EAAE,EAAE;MACP,OAAO,IAAI,CAAC,CAAC;IACf;IACA,MAAMb,MAAM,GAAG,CAACY,qBAAqB,GAAGb,KAAK,CAACe,iBAAiB,KAAK,IAAI,GAAGF,qBAAqB,GAAGlC,eAAe;;IAElH;AACJ;AACA;AACA;AACA;IACI,SAASqC,QAAQA,CAACC,OAAO,EAAE;MACzB,IAAIC,QAAQ,GAAGD,OAAO;MACtB,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;QACjCC,QAAQ,GAAGD,OAAO,CAACjB,KAAK,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOiB,OAAO,KAAK,QAAQ,EAAE;QACtC;QACA,OAAOA,OAAO;MAChB;MACA,IAAI,CAACC,QAAQ,EAAE;QACb,OAAO,IAAI;MACb;MACA,MAAMC,gBAAgB,GAAG1C,2BAA2B,CAACuB,KAAK,CAACoB,WAAW,CAAC;MACvE,MAAMC,eAAe,GAAGlC,MAAM,CAACH,IAAI,CAACmC,gBAAgB,CAAC;MACrD,IAAIG,GAAG,GAAGH,gBAAgB;MAC1BhC,MAAM,CAACH,IAAI,CAACkC,QAAQ,CAAC,CAACK,OAAO,CAACC,QAAQ,IAAI;QACxC,MAAMb,KAAK,GAAGlB,QAAQ,CAACyB,QAAQ,CAACM,QAAQ,CAAC,EAAExB,KAAK,CAAC;QACjD,IAAIW,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKc,SAAS,EAAE;UACzC,IAAI,OAAOd,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAIV,MAAM,CAACuB,QAAQ,CAAC,EAAE;cACpBF,GAAG,GAAGlD,KAAK,CAACkD,GAAG,EAAEzB,aAAa,CAAC2B,QAAQ,EAAEb,KAAK,EAAEX,KAAK,EAAEC,MAAM,CAAC,CAAC;YACjE,CAAC,MAAM;cACL,MAAMyB,iBAAiB,GAAGlD,iBAAiB,CAAC;gBAC1CwB;cACF,CAAC,EAAEW,KAAK,EAAEgB,CAAC,KAAK;gBACd,CAACH,QAAQ,GAAGG;cACd,CAAC,CAAC,CAAC;cACH,IAAI/C,mBAAmB,CAAC8C,iBAAiB,EAAEf,KAAK,CAAC,EAAE;gBACjDW,GAAG,CAACE,QAAQ,CAAC,GAAGZ,eAAe,CAAC;kBAC9BE,EAAE,EAAEH,KAAK;kBACTX;gBACF,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLsB,GAAG,GAAGlD,KAAK,CAACkD,GAAG,EAAEI,iBAAiB,CAAC;cACrC;YACF;UACF,CAAC,MAAM;YACLJ,GAAG,GAAGlD,KAAK,CAACkD,GAAG,EAAEzB,aAAa,CAAC2B,QAAQ,EAAEb,KAAK,EAAEX,KAAK,EAAEC,MAAM,CAAC,CAAC;UACjE;QACF;MACF,CAAC,CAAC;MACF,OAAOvB,uBAAuB,CAAC2C,eAAe,EAAEC,GAAG,CAAC;IACtD;IACA,OAAOM,KAAK,CAACC,OAAO,CAACf,EAAE,CAAC,GAAGA,EAAE,CAACgB,GAAG,CAACd,QAAQ,CAAC,GAAGA,QAAQ,CAACF,EAAE,CAAC;EAC5D;EACA,OAAOF,eAAe;AACxB;AACA,MAAMA,eAAe,GAAGhB,8BAA8B,CAAC,CAAC;AACxDgB,eAAe,CAACmB,WAAW,GAAG,CAAC,IAAI,CAAC;AACpC,eAAenB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}