{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M17 4h-3V2h-4v2H7v5h4.93L13 7v2h4V4z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    d: \"M13 12.5h2L11 20v-5.5H9L11.93 9H7v13h10V9h-4v3.5z\"\n  })]\n}), 'BatteryCharging80Sharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "children", "fillOpacity", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/BatteryCharging80Sharp.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M17 4h-3V2h-4v2H7v5h4.93L13 7v2h4V4z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    d: \"M13 12.5h2L11 20v-5.5H9L11.93 9H7v13h10V9h-4v3.5z\"\n  })]\n}), 'BatteryCharging80Sharp');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,eAAeJ,aAAa,CAAE,aAAaI,KAAK,CAACL,KAAK,CAACM,QAAQ,EAAE;EAC/DC,QAAQ,EAAE,CAAC,aAAaJ,IAAI,CAAC,MAAM,EAAE;IACnCK,WAAW,EAAE,IAAI;IACjBC,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAaN,IAAI,CAAC,MAAM,EAAE;IAC5BM,CAAC,EAAE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC,EAAE,wBAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}