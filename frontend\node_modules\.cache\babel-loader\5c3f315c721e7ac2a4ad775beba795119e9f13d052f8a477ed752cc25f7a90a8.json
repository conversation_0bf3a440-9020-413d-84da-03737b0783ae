{"ast": null, "code": "'use client';\n\nimport { usePreviousProps } from '@mui/utils';\n/**\n *\n * Demos:\n *\n * - [Badge](https://next.mui.com/base-ui/react-badge/#hook)\n *\n * API:\n *\n * - [useBadge API](https://next.mui.com/base-ui/react-badge/hooks-api/#use-badge)\n */\nfunction useBadge(parameters) {\n  const {\n    badgeContent: badgeContentProp,\n    invisible: invisibleProp = false,\n    max: maxProp = 99,\n    showZero = false\n  } = parameters;\n  const prevProps = usePreviousProps({\n    badgeContent: badgeContentProp,\n    max: maxProp\n  });\n  let invisible = invisibleProp;\n  if (invisibleProp === false && badgeContentProp === 0 && !showZero) {\n    invisible = true;\n  }\n  const {\n    badgeContent,\n    max = maxProp\n  } = invisible ? prevProps : parameters;\n  const displayValue = badgeContent && Number(badgeContent) > max ? `${max}+` : badgeContent;\n  return {\n    badgeContent,\n    invisible,\n    max,\n    displayValue\n  };\n}\nexport default useBadge;", "map": {"version": 3, "names": ["usePreviousProps", "useBadge", "parameters", "badgeContent", "badgeContentProp", "invisible", "invisibleProp", "max", "maxProp", "showZero", "prevProps", "displayValue", "Number"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Badge/useBadge.js"], "sourcesContent": ["'use client';\n\nimport { usePreviousProps } from '@mui/utils';\n/**\n *\n * Demos:\n *\n * - [Badge](https://next.mui.com/base-ui/react-badge/#hook)\n *\n * API:\n *\n * - [useBadge API](https://next.mui.com/base-ui/react-badge/hooks-api/#use-badge)\n */\nfunction useBadge(parameters) {\n  const {\n    badgeContent: badgeContentProp,\n    invisible: invisibleProp = false,\n    max: maxProp = 99,\n    showZero = false\n  } = parameters;\n  const prevProps = usePreviousProps({\n    badgeContent: badgeContentProp,\n    max: maxProp\n  });\n  let invisible = invisibleProp;\n  if (invisibleProp === false && badgeContentProp === 0 && !showZero) {\n    invisible = true;\n  }\n  const {\n    badgeContent,\n    max = maxProp\n  } = invisible ? prevProps : parameters;\n  const displayValue = badgeContent && Number(badgeContent) > max ? `${max}+` : badgeContent;\n  return {\n    badgeContent,\n    invisible,\n    max,\n    displayValue\n  };\n}\nexport default useBadge;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgB,QAAQ,YAAY;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,UAAU,EAAE;EAC5B,MAAM;IACJC,YAAY,EAAEC,gBAAgB;IAC9BC,SAAS,EAAEC,aAAa,GAAG,KAAK;IAChCC,GAAG,EAAEC,OAAO,GAAG,EAAE;IACjBC,QAAQ,GAAG;EACb,CAAC,GAAGP,UAAU;EACd,MAAMQ,SAAS,GAAGV,gBAAgB,CAAC;IACjCG,YAAY,EAAEC,gBAAgB;IAC9BG,GAAG,EAAEC;EACP,CAAC,CAAC;EACF,IAAIH,SAAS,GAAGC,aAAa;EAC7B,IAAIA,aAAa,KAAK,KAAK,IAAIF,gBAAgB,KAAK,CAAC,IAAI,CAACK,QAAQ,EAAE;IAClEJ,SAAS,GAAG,IAAI;EAClB;EACA,MAAM;IACJF,YAAY;IACZI,GAAG,GAAGC;EACR,CAAC,GAAGH,SAAS,GAAGK,SAAS,GAAGR,UAAU;EACtC,MAAMS,YAAY,GAAGR,YAAY,IAAIS,MAAM,CAACT,YAAY,CAAC,GAAGI,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAGJ,YAAY;EAC1F,OAAO;IACLA,YAAY;IACZE,SAAS;IACTE,GAAG;IACHI;EACF,CAAC;AACH;AACA,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}