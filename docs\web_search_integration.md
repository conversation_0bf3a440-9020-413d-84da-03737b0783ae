# Holly AI Web Search Integration

Holly AI now includes powerful web search capabilities that allow it to find current information when internal trading data is insufficient. This enables <PERSON> to provide more comprehensive and up-to-date market analysis.

## 🌟 What Web Search Adds to Holly AI

### Enhanced Information Gathering
- **Current News**: Find breaking news, earnings announcements, and market events
- **Real-time Context**: Get information about unusual market movements or volatility spikes
- **Company Updates**: Research recent developments, management changes, or strategic announcements
- **Economic Events**: Stay informed about Fed decisions, economic data releases, and policy changes
- **Analyst Coverage**: Find recent analyst reports and price target changes

### Intelligent Fallback System
Holly automatically uses web search when:
- Internal data doesn't contain the information needed
- User asks about recent events or news
- Market data shows unusual patterns that need context
- Specific company events or announcements are mentioned

## 🔧 Setup and Configuration

### Supported Search Providers

#### 1. Google Custom Search (Recommended)
**Best results with comprehensive coverage**

```bash
# Get API credentials:
# 1. Visit: https://developers.google.com/custom-search/v1/introduction
# 2. Create a Custom Search Engine: https://cse.google.com/cse/
# 3. Add to your .env file:

GOOGLE_SEARCH_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
```

#### 2. Bing Search API (Alternative)
**Good alternative with easier setup**

```bash
# Get API key:
# 1. Visit: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api
# 2. Add to your .env file:

BING_SEARCH_API_KEY=your_bing_api_key_here
```

#### 3. DuckDuckGo (Automatic Fallback)
**No API key required, but limited results**

DuckDuckGo is automatically available as a fallback option. While it doesn't require an API key, it provides limited search results compared to Google or Bing.

### Quick Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Add your search API keys to `.env`:**
   ```bash
   # Choose one or more providers
   GOOGLE_SEARCH_API_KEY=your_key_here
   GOOGLE_SEARCH_ENGINE_ID=your_engine_id_here
   # OR
   BING_SEARCH_API_KEY=your_key_here
   ```

3. **Test the integration:**
   ```bash
   python test_web_search.py
   ```

## 🚀 How It Works

### Automatic Integration
Holly AI automatically determines when to use web search based on:

1. **User Intent**: Questions about news, recent events, or current information
2. **Data Gaps**: When internal APIs don't have the needed information
3. **Context Clues**: Keywords like "recent", "latest", "news", "earnings", etc.

### Example Interactions

```
User: "What's the latest news about Apple earnings?"
Holly: [Automatically searches web] → [Analyzes results] → [Provides comprehensive response]

User: "Why is Tesla stock moving so much today?"
Holly: [Searches for Tesla news] → [Combines with technical analysis] → [Explains movement]

User: "Find information about the Fed's latest interest rate decision"
Holly: [Searches for Fed news] → [Analyzes market impact] → [Provides trading context]
```

### Smart Result Processing
Holly doesn't just return raw search results. It:

- **Filters for relevance** to trading and financial markets
- **Summarizes key information** in trading context
- **Combines web data** with technical analysis
- **Provides actionable insights** for trading decisions

## 🎯 Use Cases

### 1. Earnings and Company News
```
"Search for NVIDIA's latest earnings report"
"What's the recent news about Microsoft's AI initiatives?"
"Find information about Amazon's latest quarterly results"
```

### 2. Market Events and Economic Data
```
"What happened at the latest Fed meeting?"
"Search for recent inflation data and market impact"
"Find news about the latest jobs report"
```

### 3. Unusual Market Activity
```
"Why is the VIX spiking today?"
"Search for news about today's market volatility"
"What's causing the tech selloff this week?"
```

### 4. Sector and Industry Analysis
```
"Find recent news about the semiconductor industry"
"Search for information about renewable energy stocks"
"What's the latest on the banking sector?"
```

## 🔒 Privacy and Security

### Data Handling
- **No Personal Data**: Only search queries are sent to external APIs
- **No Trading Data**: Your positions, account info, and trading history stay private
- **Configurable**: You control which search providers to use
- **Optional**: Web search is completely optional - Holly works without it

### API Key Security
- Store API keys in `.env` file (never commit to version control)
- Use environment variables for production deployments
- Rotate keys regularly following provider recommendations

## 🛠️ Advanced Configuration

### Custom Search Scope
For Google Custom Search, you can configure:

```javascript
// In your Google Custom Search Engine settings:
{
  "sites": [
    "finance.yahoo.com",
    "marketwatch.com", 
    "bloomberg.com",
    "reuters.com",
    "sec.gov"
  ],
  "safeSearch": "active",
  "language": "en"
}
```

### Rate Limiting and Costs
- **Google**: 100 free searches/day, then $5 per 1000 queries
- **Bing**: 1000 free searches/month, then $3 per 1000 queries
- **DuckDuckGo**: Free but limited functionality

### Error Handling
Holly gracefully handles:
- API rate limits (automatic fallback to other providers)
- Network timeouts (returns cached results if available)
- Invalid API keys (falls back to DuckDuckGo)
- No search results (uses internal data only)

## 🧪 Testing

Run the test script to verify your setup:

```bash
python test_web_search.py
```

This will test:
- ✅ Search provider configuration
- ✅ API connectivity
- ✅ Holly AI integration
- ✅ Error handling scenarios

## 🚨 Troubleshooting

### Common Issues

**"Web search is not configured"**
- Add API keys to your `.env` file
- Restart Holly AI after adding keys
- Check API key format and permissions

**"Search failed with HTTP 403"**
- Verify API key is correct and active
- Check if you've exceeded rate limits
- Ensure API key has proper permissions

**"No results found"**
- Try rephrasing your search query
- Check if the topic is too specific or recent
- Verify internet connectivity

### Getting Help

1. **Check the logs**: Holly logs all search attempts and errors
2. **Run the test script**: `python test_web_search.py`
3. **Verify API keys**: Test them directly with the provider's documentation
4. **Check rate limits**: Most providers have daily/monthly limits

## 🎉 Benefits for Trading

With web search integration, Holly AI becomes significantly more powerful:

- **Better Context**: Understand why stocks are moving
- **Timely Information**: Get breaking news that affects your positions
- **Comprehensive Analysis**: Combine technical analysis with fundamental news
- **Risk Management**: Stay informed about events that could impact your trades
- **Learning**: Understand market dynamics and news impact on prices

The web search integration makes Holly AI a truly comprehensive trading assistant that combines the best of technical analysis, market data, and real-time information gathering.
