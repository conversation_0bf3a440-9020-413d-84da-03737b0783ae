{"ast": null, "code": "'use client';\n\nexport { default } from './CircularProgress';\nexport { default as circularProgressClasses } from './circularProgressClasses';\nexport * from './circularProgressClasses';", "map": {"version": 3, "names": ["default", "circularProgressClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/CircularProgress/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './CircularProgress';\nexport { default as circularProgressClasses } from './circularProgressClasses';\nexport * from './circularProgressClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASA,OAAO,IAAIC,uBAAuB,QAAQ,2BAA2B;AAC9E,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}