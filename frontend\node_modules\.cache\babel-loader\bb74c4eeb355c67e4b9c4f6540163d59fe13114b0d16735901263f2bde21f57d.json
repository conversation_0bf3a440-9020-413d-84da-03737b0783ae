{"ast": null, "code": "import PropTypes from 'prop-types';\nconst refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);\nexport default refType;", "map": {"version": 3, "names": ["PropTypes", "refType", "oneOfType", "func", "object"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/utils/esm/refType/refType.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nconst refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);\nexport default refType;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,MAAMC,OAAO,GAAGD,SAAS,CAACE,SAAS,CAAC,CAACF,SAAS,CAACG,IAAI,EAAEH,SAAS,CAACI,MAAM,CAAC,CAAC;AACvE,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}