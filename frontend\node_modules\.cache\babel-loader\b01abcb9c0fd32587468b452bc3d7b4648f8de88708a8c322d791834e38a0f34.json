{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst AlignEndHorizontal = createLucideIcon(\"AlignEndHorizontal\", [[\"rect\", {\n  width: \"6\",\n  height: \"16\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"z5wdxg\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"9\",\n  x: \"14\",\n  y: \"9\",\n  rx: \"2\",\n  key: \"um7a8w\"\n}], [\"path\", {\n  d: \"M22 22H2\",\n  key: \"19qnx5\"\n}]]);\nexport { AlignEndHorizontal as default };", "map": {"version": 3, "names": ["AlignEndHorizontal", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\align-end-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignEndHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNiIgeD0iNCIgeT0iMiIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iOSIgeD0iMTQiIHk9IjkiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0yMiAyMkgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/align-end-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignEndHorizontal = createLucideIcon('AlignEndHorizontal', [\n  ['rect', { width: '6', height: '16', x: '4', y: '2', rx: '2', key: 'z5wdxg' }],\n  ['rect', { width: '6', height: '9', x: '14', y: '9', rx: '2', key: 'um7a8w' }],\n  ['path', { d: 'M22 22H2', key: '19qnx5' }],\n]);\n\nexport default AlignEndHorizontal;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,kBAAA,GAAqBC,gBAAA,CAAiB,oBAAsB,GAChE,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}