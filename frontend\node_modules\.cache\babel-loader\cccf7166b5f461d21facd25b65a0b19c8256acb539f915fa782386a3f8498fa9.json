{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Carrot = createLucideIcon(\"Carrot\", [[\"path\", {\n  d: \"M2.27 21.7s9.87-3.5 12.73-6.36a4.5 4.5 0 0 0-6.36-6.37C5.77 11.84 2.27 21.7 2.27 21.7zM8.64 14l-2.05-2.04M15.34 15l-2.46-2.46\",\n  key: \"rfqxbe\"\n}], [\"path\", {\n  d: \"M22 9s-1.33-2-3.5-2C16.86 7 15 9 15 9s1.33 2 3.5 2S22 9 22 9z\",\n  key: \"6b25w4\"\n}], [\"path\", {\n  d: \"M15 2s-2 1.33-2 3.5S15 9 15 9s2-1.84 2-3.5C17 3.33 15 2 15 2z\",\n  key: \"fn65lo\"\n}]]);\nexport { Carrot as default };", "map": {"version": 3, "names": ["Carrot", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\carrot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Carrot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4yNyAyMS43czkuODctMy41IDEyLjczLTYuMzZhNC41IDQuNSAwIDAgMC02LjM2LTYuMzdDNS43NyAxMS44NCAyLjI3IDIxLjcgMi4yNyAyMS43ek04LjY0IDE0bC0yLjA1LTIuMDRNMTUuMzQgMTVsLTIuNDYtMi40NiIgLz4KICA8cGF0aCBkPSJNMjIgOXMtMS4zMy0yLTMuNS0yQzE2Ljg2IDcgMTUgOSAxNSA5czEuMzMgMiAzLjUgMlMyMiA5IDIyIDl6IiAvPgogIDxwYXRoIGQ9Ik0xNSAycy0yIDEuMzMtMiAzLjVTMTUgOSAxNSA5czItMS44NCAyLTMuNUMxNyAzLjMzIDE1IDIgMTUgMnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/carrot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Carrot = createLucideIcon('Carrot', [\n  [\n    'path',\n    {\n      d: 'M2.27 21.7s9.87-3.5 12.73-6.36a4.5 4.5 0 0 0-6.36-6.37C5.77 11.84 2.27 21.7 2.27 21.7zM8.64 14l-2.05-2.04M15.34 15l-2.46-2.46',\n      key: 'rfqxbe',\n    },\n  ],\n  ['path', { d: 'M22 9s-1.33-2-3.5-2C16.86 7 15 9 15 9s1.33 2 3.5 2S22 9 22 9z', key: '6b25w4' }],\n  ['path', { d: 'M15 2s-2 1.33-2 3.5S15 9 15 9s2-1.84 2-3.5C17 3.33 15 2 15 2z', key: 'fn65lo' }],\n]);\n\nexport default Carrot;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,+DAAiE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9F,CAAC,MAAQ;EAAED,CAAA,EAAG,+DAAiE;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}