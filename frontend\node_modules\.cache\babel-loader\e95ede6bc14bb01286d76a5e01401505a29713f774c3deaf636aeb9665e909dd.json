{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Megaphone = createLucideIcon(\"Megaphone\", [[\"path\", {\n  d: \"m3 11 18-5v12L3 14v-3z\",\n  key: \"n962bs\"\n}], [\"path\", {\n  d: \"M11.6 16.8a3 3 0 1 1-5.8-1.6\",\n  key: \"1yl0tm\"\n}]]);\nexport { Megaphone as default };", "map": {"version": 3, "names": ["Megaphone", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\megaphone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Megaphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAxMSAxOC01djEyTDMgMTR2LTN6IiAvPgogIDxwYXRoIGQ9Ik0xMS42IDE2LjhhMyAzIDAgMSAxLTUuOC0xLjYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/megaphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Megaphone = createLucideIcon('Megaphone', [\n  ['path', { d: 'm3 11 18-5v12L3 14v-3z', key: 'n962bs' }],\n  ['path', { d: 'M11.6 16.8a3 3 0 1 1-5.8-1.6', key: '1yl0tm' }],\n]);\n\nexport default Megaphone;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}