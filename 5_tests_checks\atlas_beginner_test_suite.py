#!/usr/bin/env python3
"""
A.T.L.A.S. Beginner-Style Test Suite
Tests all system capabilities using natural beginner questions
"""

import requests
import time
from datetime import datetime
from typing import Dict, List, Any

class ATLASBeginnerTestSuite:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.test_results = []
        self.failed_tests = []
        self.passed_tests = []
        
    def send_request(self, message: str, panel: str = "left") -> Dict[str, Any]:
        """Send request to A.T.L.A.S. system"""
        data = {
            'message': message,
            'session_id': f'test_{int(time.time())}',
            'context': {
                'panel': panel,
                'interface_type': 'general_trading'
            }
        }
        
        try:
            response = requests.post(f'{self.base_url}/api/v1/chat', json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}", "response": response.text}
        except Exception as e:
            return {"error": str(e), "response": ""}
    
    def analyze_response(self, question: str, response: Dict[str, Any], expected_features: List[str]) -> Dict[str, Any]:
        """Analyze response for A.T.L.A.S. capabilities and limitations"""
        response_text = response.get('response', '').lower()
        
        # Check for limitation indicators (should be minimal for A.T.L.A.S.)
        limitation_phrases = [
            "i can't", "i don't have", "i'm not able", "i cannot",
            "as an ai", "i'm just a", "i don't actually",
            "i'm unable to", "not possible", "can't access",
            "don't have access", "not available", "cannot provide",
            "i'm a language model", "i don't have the ability"
        ]
        
        limitations = [phrase for phrase in limitation_phrases if phrase in response_text]
        
        # Check for expected features
        features_found = []
        for feature in expected_features:
            if feature.lower() in response_text:
                features_found.append(feature)
        
        # Check for A.T.L.A.S. branding
        has_atlas_branding = any(brand in response_text for brand in [
            'a.t.l.a.s', 'atlas', 'predicto'
        ])
        
        # Check for specific trading data
        has_trading_data = any(indicator in response_text for indicator in [
            '$', '%', 'price:', 'target:', 'stop:', 'entry:', 'shares', 'buy', 'sell'
        ])
        
        # Check for confident trading system response
        is_confident = not limitations and len(response.get('response', '')) > 50
        
        return {
            'question': question,
            'response': response.get('response', ''),
            'limitations': limitations,
            'features_found': features_found,
            'has_atlas_branding': has_atlas_branding,
            'has_trading_data': has_trading_data,
            'is_confident': is_confident,
            'response_length': len(response.get('response', '')),
            'error': response.get('error'),
            'passed': is_confident and has_trading_data and not limitations
        }
    
    def run_beginner_tests(self):
        """Run all beginner-style tests covering A.T.L.A.S. capabilities"""
        print("🚀 Starting A.T.L.A.S. Beginner-Style Test Suite")
        print("=" * 60)

        # Define all test cases with expected features
        test_cases = [
            {
                'question': "What's Apple trading at right now?",
                'features': ['real-time', 'price', 'apple', 'aapl', 'current'],
                'capability': 'Real-Time Market Data'
            },
            {
                'question': "Are there any stocks that look like they might jump soon?",
                'features': ['scan', 'stocks', 'momentum', 'breakout', 'opportunities'],
                'capability': 'Pattern Detection & Scanning'
            },
            {
                'question': "Why did Tesla move up today? What's the news behind it?",
                'features': ['tesla', 'tsla', 'news', 'sentiment', 'analysis'],
                'capability': 'News & Sentiment Analysis'
            },
            {
                'question': "Where do you think Microsoft will be in five days?",
                'features': ['microsoft', 'msft', 'prediction', 'forecast', '5 days'],
                'capability': 'ML Price Predictions'
            },
            {
                'question': "Can you suggest an options trade on NVIDIA that could make me money this week?",
                'features': ['options', 'nvidia', 'nvda', 'trade', 'strategy'],
                'capability': 'Options Analysis'
            },
            {
                'question': "I want to make $100 by tomorrow—what trade should I place?",
                'features': ['$100', 'tomorrow', 'trade', 'strategy', 'goal'],
                'capability': 'Goal-Based Strategy Generation'
            },
            {
                'question': "Please buy 10 shares of Amazon for me now.",
                'features': ['buy', '10 shares', 'amazon', 'amzn', 'execute'],
                'capability': 'Trade Execution'
            },
            {
                'question': "Alert me when Google goes up more than 2% today.",
                'features': ['alert', 'google', 'googl', '2%', 'notification'],
                'capability': 'Real-Time Alerts'
            },
            {
                'question': "How can I protect my Tesla shares if they start falling?",
                'features': ['protect', 'tesla', 'hedging', 'stop loss', 'risk'],
                'capability': 'Risk Management & Hedging'
            },
            {
                'question': "Can you optimize my portfolio to boost returns?",
                'features': ['optimize', 'portfolio', 'returns', 'allocation', 'sharpe'],
                'capability': 'Portfolio Optimization'
            },
            {
                'question': "How risky is buying 20 shares of Shopify right now?",
                'features': ['risk', '20 shares', 'shopify', 'shop', 'assessment'],
                'capability': 'Risk Assessment'
            },
            {
                'question': "Give me a quick morning market briefing for today.",
                'features': ['morning', 'briefing', 'market', 'today', 'overview'],
                'capability': 'Morning Briefing'
            },
            {
                'question': "Show me any unusual options activity in Netflix.",
                'features': ['unusual', 'options', 'activity', 'netflix', 'nflx'],
                'capability': 'Options Flow Analysis'
            },
            {
                'question': "Backtest a simple breakout strategy on the S&P 500 over the last month.",
                'features': ['backtest', 'breakout', 'strategy', 's&p 500', 'spy'],
                'capability': 'Backtesting'
            },
            {
                'question': "Which forex pair could earn me $50 today?",
                'features': ['forex', 'pair', '$50', 'today', 'currency'],
                'capability': 'Multi-Asset Support'
            },
            {
                'question': "Alert me when the VIX index rises above 20.",
                'features': ['alert', 'vix', 'index', 'above 20', 'volatility'],
                'capability': 'Market Indicators'
            },
            {
                'question': "What's the best ETF for steady growth this month?",
                'features': ['etf', 'steady growth', 'month', 'recommendation'],
                'capability': 'Investment Recommendations'
            },
            {
                'question': "Help me make $200 this week with minimal trades.",
                'features': ['$200', 'week', 'minimal trades', 'strategy', 'goal'],
                'capability': 'Goal-Based Trading'
            },
            {
                'question': "Do any cryptocurrencies look good right now? If so, buy 1 ETH.",
                'features': ['cryptocurrency', 'crypto', 'eth', 'ethereum', 'buy'],
                'capability': 'Cryptocurrency Support'
            },
            {
                'question': "Show my last trades and how much profit or loss I've made.",
                'features': ['trades', 'profit', 'loss', 'p&l', 'history'],
                'capability': 'Trade History & P&L'
            }
        ]
        
        start_time = datetime.now()

        # Run all tests
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i:2d}. Testing: {test_case['capability']}")
            print(f"    Question: {test_case['question']}")
            
            response = self.send_request(test_case['question'])
            analysis = self.analyze_response(
                test_case['question'], 
                response, 
                test_case['features']
            )
            analysis['capability'] = test_case['capability']
            
            self.test_results.append(analysis)
            
            if analysis['passed']:
                self.passed_tests.append(analysis)
                print(f"    ✅ PASSED - Confident response with trading data")
            else:
                self.failed_tests.append(analysis)
                issues = []
                if analysis['limitations']:
                    issues.append(f"Limitations: {analysis['limitations']}")
                if not analysis['has_trading_data']:
                    issues.append("No specific trading data")
                if not analysis['is_confident']:
                    issues.append("Not confident response")
                print(f"    ❌ FAILED - {'; '.join(issues)}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Generate comprehensive report
        self.generate_report(duration)
    
    def generate_report(self, duration):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 A.T.L.A.S. BEGINNER TEST RESULTS")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_count = len(self.passed_tests)
        failed_count = len(self.failed_tests)
        success_rate = (passed_count / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"⏱️  Test Duration: {duration}")
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_count}")
        print(f"❌ Failed: {failed_count}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        # Show capability breakdown
        print(f"\n🎯 CAPABILITY BREAKDOWN:")
        print("-" * 40)
        
        capabilities = {}
        for result in self.test_results:
            cap = result['capability']
            if cap not in capabilities:
                capabilities[cap] = {'passed': 0, 'total': 0}
            capabilities[cap]['total'] += 1
            if result['passed']:
                capabilities[cap]['passed'] += 1
        
        for capability, stats in capabilities.items():
            rate = (stats['passed'] / stats['total']) * 100
            status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
            print(f"{status} {capability}: {stats['passed']}/{stats['total']} ({rate:.0f}%)")
        
        # Show failed tests details
        if self.failed_tests:
            print(f"\n🔍 FAILED TESTS ANALYSIS:")
            print("-" * 40)
            
            for i, failure in enumerate(self.failed_tests, 1):
                print(f"\n{i}. {failure['capability']}")
                print(f"   Question: {failure['question']}")
                print(f"   Issues: ", end="")
                issues = []
                if failure['limitations']:
                    issues.append(f"Limitations ({len(failure['limitations'])})")
                if not failure['has_trading_data']:
                    issues.append("No trading data")
                if not failure['is_confident']:
                    issues.append("Not confident")
                print(", ".join(issues))
                
                # Show response preview
                preview = failure['response'][:150] + "..." if len(failure['response']) > 150 else failure['response']
                print(f"   Response: {preview}")
        
        # Generate recommendations
        self.generate_recommendations(success_rate)
    
    def generate_recommendations(self, success_rate: float):
        """Generate improvement recommendations"""
        print(f"\n🔧 RECOMMENDATIONS:")
        print("-" * 25)
        
        if success_rate >= 90:
            print("🎉 Excellent! A.T.L.A.S. is performing at institutional level!")
        elif success_rate >= 75:
            print("✅ Good performance. Minor improvements needed:")
            print("   • Enhance responses for failed capabilities")
            print("   • Add more specific trading data")
        elif success_rate >= 50:
            print("⚠️  Moderate performance. Key improvements needed:")
            print("   • Eliminate generic AI disclaimers")
            print("   • Add confident trading system responses")
            print("   • Include specific prices and trading data")
        else:
            print("❌ Poor performance. Major improvements required:")
            print("   • Complete system response overhaul needed")
            print("   • Implement Trading God persona")
            print("   • Add real market data integration")
            print("   • Remove all AI limitations language")

def main():
    """Main execution function"""
    print("🚀 A.T.L.A.S. Beginner-Style Test Suite")
    print("Testing all capabilities with natural beginner questions")
    print("=" * 60)

    try:
        test_suite = ATLASBeginnerTestSuite()
        print("✅ Test suite initialized")
        test_suite.run_beginner_tests()
        print("✅ Test suite completed")
    except Exception as e:
        print(f"❌ Error running test suite: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
