{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Truck = createLucideIcon(\"Truck\", [[\"path\", {\n  d: \"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11\",\n  key: \"hs4xqm\"\n}], [\"path\", {\n  d: \"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2\",\n  key: \"11fp61\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"19iecd\"\n}], [\"path\", {\n  d: \"M15 18H9\",\n  key: \"1lyqi6\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"332jqn\"\n}]]);\nexport { Truck as default };", "map": {"version": 3, "names": ["Truck", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\truck.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Truck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxOEgzYy0uNiAwLTEtLjQtMS0xVjdjMC0uNi40LTEgMS0xaDEwYy42IDAgMSAuNCAxIDF2MTEiIC8+CiAgPHBhdGggZD0iTTE0IDloNGw0IDR2NGMwIC42LS40IDEtMSAxaC0yIiAvPgogIDxjaXJjbGUgY3g9IjciIGN5PSIxOCIgcj0iMiIgLz4KICA8cGF0aCBkPSJNMTUgMThIOSIgLz4KICA8Y2lyY2xlIGN4PSIxNyIgY3k9IjE4IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/truck\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Truck = createLucideIcon('Truck', [\n  ['path', { d: 'M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11', key: 'hs4xqm' }],\n  ['path', { d: 'M14 9h4l4 4v4c0 .6-.4 1-1 1h-2', key: '11fp61' }],\n  ['circle', { cx: '7', cy: '18', r: '2', key: '19iecd' }],\n  ['path', { d: 'M15 18H9', key: '1lyqi6' }],\n  ['circle', { cx: '17', cy: '18', r: '2', key: '332jqn' }],\n]);\n\nexport default Truck;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}