{"ast": null, "code": "import { useContext, useState, useEffect } from 'react';\nimport { motionValue } from './index.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n  const value = useConstant(() => motionValue(initial));\n  /**\n   * If this motion value is being used in static mode, like on\n   * the Framer canvas, force components to rerender when the motion\n   * value is updated.\n   */\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  if (isStatic) {\n    const [, setLatest] = useState(initial);\n    useEffect(() => value.on(\"change\", setLatest), []);\n  }\n  return value;\n}\nexport { useMotionValue };", "map": {"version": 3, "names": ["useContext", "useState", "useEffect", "motionValue", "MotionConfigContext", "useConstant", "useMotionValue", "initial", "value", "isStatic", "setLatest", "on"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/value/use-motion-value.mjs"], "sourcesContent": ["import { useContext, useState, useEffect } from 'react';\nimport { motionValue } from './index.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = useConstant(() => motionValue(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = useContext(MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = useState(initial);\n        useEffect(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\nexport { useMotionValue };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,WAAW,QAAQ,2BAA2B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC7B,MAAMC,KAAK,GAAGH,WAAW,CAAC,MAAMF,WAAW,CAACI,OAAO,CAAC,CAAC;EACrD;AACJ;AACA;AACA;AACA;EACI,MAAM;IAAEE;EAAS,CAAC,GAAGT,UAAU,CAACI,mBAAmB,CAAC;EACpD,IAAIK,QAAQ,EAAE;IACV,MAAM,GAAGC,SAAS,CAAC,GAAGT,QAAQ,CAACM,OAAO,CAAC;IACvCL,SAAS,CAAC,MAAMM,KAAK,CAACG,EAAE,CAAC,QAAQ,EAAED,SAAS,CAAC,EAAE,EAAE,CAAC;EACtD;EACA,OAAOF,KAAK;AAChB;AAEA,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}