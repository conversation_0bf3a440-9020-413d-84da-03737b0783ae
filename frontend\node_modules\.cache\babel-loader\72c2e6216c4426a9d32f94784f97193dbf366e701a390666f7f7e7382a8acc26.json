{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FileKey2 = createLucideIcon(\"FileKey2\", [[\"path\", {\n  d: \"M4 10V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H4\",\n  key: \"1nw5t3\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"circle\", {\n  cx: \"4\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1ehqvc\"\n}], [\"path\", {\n  d: \"m10 10-4.5 4.5\",\n  key: \"7fwrp6\"\n}], [\"path\", {\n  d: \"m9 11 1 1\",\n  key: \"wa6s5q\"\n}]]);\nexport { FileKey2 as default };", "map": {"version": 3, "names": ["FileKey2", "createLucideIcon", "d", "key", "points", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\file-key-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileKey2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMFY0YTIgMiAwIDAgMSAyLTJoOC41TDIwIDcuNVYyMGEyIDIgMCAwIDEtMiAySDQiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPGNpcmNsZSBjeD0iNCIgY3k9IjE2IiByPSIyIiAvPgogIDxwYXRoIGQ9Im0xMCAxMC00LjUgNC41IiAvPgogIDxwYXRoIGQ9Im05IDExIDEgMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/file-key-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileKey2 = createLucideIcon('FileKey2', [\n  ['path', { d: 'M4 10V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H4', key: '1nw5t3' }],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['circle', { cx: '4', cy: '16', r: '2', key: '1ehqvc' }],\n  ['path', { d: 'm10 10-4.5 4.5', key: '7fwrp6' }],\n  ['path', { d: 'm9 11 1 1', key: 'wa6s5q' }],\n]);\n\nexport default FileKey2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,qDAAuD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpF,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEE,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}