{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PackageOpen = createLucideIcon(\"PackageOpen\", [[\"path\", {\n  d: \"M20.91 8.84 8.56 2.23a1.93 1.93 0 0 0-1.81 0L3.1 4.13a2.12 2.12 0 0 0-.05 3.69l12.22 6.93a2 2 0 0 0 1.94 0L21 12.51a2.12 2.12 0 0 0-.09-3.67Z\",\n  key: \"1vy178\"\n}], [\"path\", {\n  d: \"m3.09 8.84 12.35-6.61a1.93 1.93 0 0 1 1.81 0l3.65 1.9a2.12 2.12 0 0 1 .1 3.69L8.73 14.75a2 2 0 0 1-1.94 0L3 12.51a2.12 2.12 0 0 1 .09-3.67Z\",\n  key: \"s3bv25\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"13\",\n  key: \"1o4xyi\"\n}], [\"path\", {\n  d: \"M20 13.5v3.37a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13.5\",\n  key: \"1na2nq\"\n}]]);\nexport { PackageOpen as default };", "map": {"version": 3, "names": ["PackageOpen", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\package-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PackageOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAuOTEgOC44NCA4LjU2IDIuMjNhMS45MyAxLjkzIDAgMCAwLTEuODEgMEwzLjEgNC4xM2EyLjEyIDIuMTIgMCAwIDAtLjA1IDMuNjlsMTIuMjIgNi45M2EyIDIgMCAwIDAgMS45NCAwTDIxIDEyLjUxYTIuMTIgMi4xMiAwIDAgMC0uMDktMy42N1oiIC8+CiAgPHBhdGggZD0ibTMuMDkgOC44NCAxMi4zNS02LjYxYTEuOTMgMS45MyAwIDAgMSAxLjgxIDBsMy42NSAxLjlhMi4xMiAyLjEyIDAgMCAxIC4xIDMuNjlMOC43MyAxNC43NWEyIDIgMCAwIDEtMS45NCAwTDMgMTIuNTFhMi4xMiAyLjEyIDAgMCAxIC4wOS0zLjY3WiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIyIiB5Mj0iMTMiIC8+CiAgPHBhdGggZD0iTTIwIDEzLjV2My4zN2EyLjA2IDIuMDYgMCAwIDEtMS4xMSAxLjgzbC02IDMuMDhhMS45MyAxLjkzIDAgMCAxLTEuNzggMGwtNi0zLjA4QTIuMDYgMi4wNiAwIDAgMSA0IDE2Ljg3VjEzLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/package-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PackageOpen = createLucideIcon('PackageOpen', [\n  [\n    'path',\n    {\n      d: 'M20.91 8.84 8.56 2.23a1.93 1.93 0 0 0-1.81 0L3.1 4.13a2.12 2.12 0 0 0-.05 3.69l12.22 6.93a2 2 0 0 0 1.94 0L21 12.51a2.12 2.12 0 0 0-.09-3.67Z',\n      key: '1vy178',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm3.09 8.84 12.35-6.61a1.93 1.93 0 0 1 1.81 0l3.65 1.9a2.12 2.12 0 0 1 .1 3.69L8.73 14.75a2 2 0 0 1-1.94 0L3 12.51a2.12 2.12 0 0 1 .09-3.67Z',\n      key: 's3bv25',\n    },\n  ],\n  ['line', { x1: '12', x2: '12', y1: '22', y2: '13', key: '1o4xyi' }],\n  [\n    'path',\n    {\n      d: 'M20 13.5v3.37a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13.5',\n      key: '1na2nq',\n    },\n  ],\n]);\n\nexport default PackageOpen;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}