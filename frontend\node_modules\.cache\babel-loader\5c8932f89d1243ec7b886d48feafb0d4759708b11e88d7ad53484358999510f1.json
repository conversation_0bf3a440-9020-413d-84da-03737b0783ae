{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport default function requirePropFactory(componentNameInError, Component) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n\n  // eslint-disable-next-line react/forbid-foreign-prop-types\n  const prevPropTypes = Component ? _extends({}, Component.propTypes) : null;\n  const requireProp = requiredProp => function (props, propName, componentName, location, propFullName) {\n    const propFullNameSafe = propFullName || propName;\n    const defaultTypeChecker = prevPropTypes == null ? void 0 : prevPropTypes[propFullNameSafe];\n    if (defaultTypeChecker) {\n      for (var _len = arguments.length, args = new Array(_len > 5 ? _len - 5 : 0), _key = 5; _key < _len; _key++) {\n        args[_key - 5] = arguments[_key];\n      }\n      const typeCheckerResult = defaultTypeChecker(props, propName, componentName, location, propFullName, ...args);\n      if (typeCheckerResult) {\n        return typeCheckerResult;\n      }\n    }\n    if (typeof props[propName] !== 'undefined' && !props[requiredProp]) {\n      return new Error(\"The prop `\".concat(propFullNameSafe, \"` of \") + \"`\".concat(componentNameInError, \"` can only be used together with the `\").concat(requiredProp, \"` prop.\"));\n    }\n    return null;\n  };\n  return requireProp;\n}", "map": {"version": 3, "names": ["_extends", "requirePropFactory", "componentNameInError", "Component", "process", "env", "NODE_ENV", "prevPropTypes", "propTypes", "requireProp", "requiredProp", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "propFullNameSafe", "defaultTypeChecker", "_len", "arguments", "length", "args", "Array", "_key", "typeCheckerResult", "Error", "concat"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/utils/esm/requirePropFactory/requirePropFactory.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport default function requirePropFactory(componentNameInError, Component) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n\n  // eslint-disable-next-line react/forbid-foreign-prop-types\n  const prevPropTypes = Component ? _extends({}, Component.propTypes) : null;\n  const requireProp = requiredProp => (props, propName, componentName, location, propFullName, ...args) => {\n    const propFullNameSafe = propFullName || propName;\n    const defaultTypeChecker = prevPropTypes == null ? void 0 : prevPropTypes[propFullNameSafe];\n    if (defaultTypeChecker) {\n      const typeCheckerResult = defaultTypeChecker(props, propName, componentName, location, propFullName, ...args);\n      if (typeCheckerResult) {\n        return typeCheckerResult;\n      }\n    }\n    if (typeof props[propName] !== 'undefined' && !props[requiredProp]) {\n      return new Error(`The prop \\`${propFullNameSafe}\\` of ` + `\\`${componentNameInError}\\` can only be used together with the \\`${requiredProp}\\` prop.`);\n    }\n    return null;\n  };\n  return requireProp;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,eAAe,SAASC,kBAAkBA,CAACC,oBAAoB,EAAEC,SAAS,EAAE;EAC1E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,MAAM,IAAI;EACnB;;EAEA;EACA,MAAMC,aAAa,GAAGJ,SAAS,GAAGH,QAAQ,CAAC,CAAC,CAAC,EAAEG,SAAS,CAACK,SAAS,CAAC,GAAG,IAAI;EAC1E,MAAMC,WAAW,GAAGC,YAAY,IAAI,UAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAc;IACvG,MAAMC,gBAAgB,GAAGD,YAAY,IAAIH,QAAQ;IACjD,MAAMK,kBAAkB,GAAGV,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACS,gBAAgB,CAAC;IAC3F,IAAIC,kBAAkB,EAAE;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAHsEC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAIhG,MAAMC,iBAAiB,GAAGP,kBAAkB,CAACN,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAGM,IAAI,CAAC;MAC7G,IAAIG,iBAAiB,EAAE;QACrB,OAAOA,iBAAiB;MAC1B;IACF;IACA,IAAI,OAAOb,KAAK,CAACC,QAAQ,CAAC,KAAK,WAAW,IAAI,CAACD,KAAK,CAACD,YAAY,CAAC,EAAE;MAClE,OAAO,IAAIe,KAAK,CAAC,aAAAC,MAAA,CAAcV,gBAAgB,iBAAAU,MAAA,CAAgBxB,oBAAoB,4CAAAwB,MAAA,CAA2ChB,YAAY,YAAU,CAAC;IACvJ;IACA,OAAO,IAAI;EACb,CAAC;EACD,OAAOD,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}