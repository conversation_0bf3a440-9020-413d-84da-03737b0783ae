{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst TrafficCone = createLucideIcon(\"TrafficCone\", [[\"path\", {\n  d: \"M9.3 6.2a4.55 4.55 0 0 0 5.4 0\",\n  key: \"flyxqv\"\n}], [\"path\", {\n  d: \"M7.9 10.7c.9.8 2.4 1.3 4.1 1.3s3.2-.5 4.1-1.3\",\n  key: \"1nlxxg\"\n}], [\"path\", {\n  d: \"M13.9 3.5a1.93 1.93 0 0 0-3.8-.1l-3 10c-.1.2-.1.4-.1.6 0 1.7 2.2 3 5 3s5-1.3 5-3c0-.2 0-.4-.1-.5Z\",\n  key: \"vz7x1l\"\n}], [\"path\", {\n  d: \"m7.5 12.2-4.7 2.7c-.5.3-.8.7-.8 1.1s.3.8.8 1.1l7.6 4.5c.9.5 2.1.5 3 0l7.6-4.5c.7-.3 1-.7 1-1.1s-.3-.8-.8-1.1l-4.7-2.8\",\n  key: \"1xfzlw\"\n}]]);\nexport { TrafficCone as default };", "map": {"version": 3, "names": ["TrafficCone", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\traffic-cone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TrafficCone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOS4zIDYuMmE0LjU1IDQuNTUgMCAwIDAgNS40IDAiIC8+CiAgPHBhdGggZD0iTTcuOSAxMC43Yy45LjggMi40IDEuMyA0LjEgMS4zczMuMi0uNSA0LjEtMS4zIiAvPgogIDxwYXRoIGQ9Ik0xMy45IDMuNWExLjkzIDEuOTMgMCAwIDAtMy44LS4xbC0zIDEwYy0uMS4yLS4xLjQtLjEuNiAwIDEuNyAyLjIgMyA1IDNzNS0xLjMgNS0zYzAtLjIgMC0uNC0uMS0uNVoiIC8+CiAgPHBhdGggZD0ibTcuNSAxMi4yLTQuNyAyLjdjLS41LjMtLjguNy0uOCAxLjFzLjMuOC44IDEuMWw3LjYgNC41Yy45LjUgMi4xLjUgMyAwbDcuNi00LjVjLjctLjMgMS0uNyAxLTEuMXMtLjMtLjgtLjgtMS4xbC00LjctMi44IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/traffic-cone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrafficCone = createLucideIcon('TrafficCone', [\n  ['path', { d: 'M9.3 6.2a4.55 4.55 0 0 0 5.4 0', key: 'flyxqv' }],\n  ['path', { d: 'M7.9 10.7c.9.8 2.4 1.3 4.1 1.3s3.2-.5 4.1-1.3', key: '1nlxxg' }],\n  [\n    'path',\n    {\n      d: 'M13.9 3.5a1.93 1.93 0 0 0-3.8-.1l-3 10c-.1.2-.1.4-.1.6 0 1.7 2.2 3 5 3s5-1.3 5-3c0-.2 0-.4-.1-.5Z',\n      key: 'vz7x1l',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm7.5 12.2-4.7 2.7c-.5.3-.8.7-.8 1.1s.3.8.8 1.1l7.6 4.5c.9.5 2.1.5 3 0l7.6-4.5c.7-.3 1-.7 1-1.1s-.3-.8-.8-1.1l-4.7-2.8',\n      key: '1xfzlw',\n    },\n  ],\n]);\n\nexport default TrafficCone;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}