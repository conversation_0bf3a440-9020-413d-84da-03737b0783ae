{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Files = createLucideIcon(\"Files\", [[\"path\", {\n  d: \"M15.5 2H8.6c-.4 0-.8.2-1.1.5-.3.3-.5.7-.5 1.1v12.8c0 .******* *******.7.5 1.1.5h9.8c.4 0 .8-.2 1.1-.5.3-.3.5-.7.5-1.1V6.5L15.5 2z\",\n  key: \"cennsq\"\n}], [\"path\", {\n  d: \"M3 7.6v12.8c0 .******* *******.7.5 1.1.5h9.8\",\n  key: \"ms809a\"\n}], [\"path\", {\n  d: \"M15 2v5h5\",\n  key: \"qq6kwv\"\n}]]);\nexport { Files as default };", "map": {"version": 3, "names": ["Files", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\files.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Files\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuNSAySDguNmMtLjQgMC0uOC4yLTEuMS41LS4zLjMtLjUuNy0uNSAxLjF2MTIuOGMwIC40LjIuOC41IDEuMS4zLjMuNy41IDEuMS41aDkuOGMuNCAwIC44LS4yIDEuMS0uNS4zLS4zLjUtLjcuNS0xLjFWNi41TDE1LjUgMnoiIC8+CiAgPHBhdGggZD0iTTMgNy42djEyLjhjMCAuNC4yLjguNSAxLjEuMy4zLjcuNSAxLjEuNWg5LjgiIC8+CiAgPHBhdGggZD0iTTE1IDJ2NWg1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/files\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Files = createLucideIcon('Files', [\n  [\n    'path',\n    {\n      d: 'M15.5 2H8.6c-.4 0-.8.2-1.1.5-.3.3-.5.7-.5 1.1v12.8c0 .******* *******.7.5 1.1.5h9.8c.4 0 .8-.2 1.1-.5.3-.3.5-.7.5-1.1V6.5L15.5 2z',\n      key: 'cennsq',\n    },\n  ],\n  ['path', { d: 'M3 7.6v12.8c0 .******* *******.7.5 1.1.5h9.8', key: 'ms809a' }],\n  ['path', { d: 'M15 2v5h5', key: 'qq6kwv' }],\n]);\n\nexport default Files;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}