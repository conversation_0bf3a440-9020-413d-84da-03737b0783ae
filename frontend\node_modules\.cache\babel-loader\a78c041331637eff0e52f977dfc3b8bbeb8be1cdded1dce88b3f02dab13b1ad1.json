{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BellElectric = createLucideIcon(\"BellElectric\", [[\"path\", {\n  d: \"M18.8 4A6.3 8.7 0 0 1 20 9\",\n  key: \"xve1fh\"\n}], [\"path\", {\n  d: \"M9 9h.01\",\n  key: \"1q5me6\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"7\",\n  key: \"p2h5vp\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"4\",\n  y: \"16\",\n  rx: \"2\",\n  key: \"17f3te\"\n}], [\"path\", {\n  d: \"M14 19c3 0 4.6-1.6 4.6-1.6\",\n  key: \"n7odp6\"\n}], [\"circle\", {\n  cx: \"20\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1v9bxh\"\n}]]);\nexport { BellElectric as default };", "map": {"version": 3, "names": ["BellElectric", "createLucideIcon", "d", "key", "cx", "cy", "r", "width", "height", "x", "y", "rx"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\bell-electric.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BellElectric\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTguOCA0QTYuMyA4LjcgMCAwIDEgMjAgOSIgLz4KICA8cGF0aCBkPSJNOSA5aC4wMSIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iOSIgcj0iNyIgLz4KICA8cmVjdCB3aWR0aD0iMTAiIGhlaWdodD0iNiIgeD0iNCIgeT0iMTYiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xNCAxOWMzIDAgNC42LTEuNiA0LjYtMS42IiAvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTYiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bell-electric\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BellElectric = createLucideIcon('BellElectric', [\n  ['path', { d: 'M18.8 4A6.3 8.7 0 0 1 20 9', key: 'xve1fh' }],\n  ['path', { d: 'M9 9h.01', key: '1q5me6' }],\n  ['circle', { cx: '9', cy: '9', r: '7', key: 'p2h5vp' }],\n  ['rect', { width: '10', height: '6', x: '4', y: '16', rx: '2', key: '17f3te' }],\n  ['path', { d: 'M14 19c3 0 4.6-1.6 4.6-1.6', key: 'n7odp6' }],\n  ['circle', { cx: '20', cy: '16', r: '2', key: '1v9bxh' }],\n]);\n\nexport default BellElectric;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,QAAQ;EAAEI,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAR,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}