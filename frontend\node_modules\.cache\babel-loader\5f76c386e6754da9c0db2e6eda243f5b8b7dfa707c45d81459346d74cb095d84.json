{"ast": null, "code": "/**\n * @experimental\n */\nvar CanvasRenderingTarget2D = /** @class */function () {\n  function CanvasRenderingTarget2D(context, mediaSize, bitmapSize) {\n    if (mediaSize.width === 0 || mediaSize.height === 0) {\n      throw new TypeError('Rendering target could only be created on a media with positive width and height');\n    }\n    this._mediaSize = mediaSize;\n    // !Number.isInteger(bitmapSize.width) || !Number.isInteger(bitmapSize.height)\n    if (bitmapSize.width === 0 || bitmapSize.height === 0) {\n      throw new TypeError('Rendering target could only be created using a bitmap with positive integer width and height');\n    }\n    this._bitmapSize = bitmapSize;\n    this._context = context;\n  }\n  CanvasRenderingTarget2D.prototype.useMediaCoordinateSpace = function (f) {\n    try {\n      this._context.save();\n      // do not use resetTransform to support old versions of Edge\n      this._context.setTransform(1, 0, 0, 1, 0, 0);\n      this._context.scale(this._horizontalPixelRatio, this._verticalPixelRatio);\n      return f({\n        context: this._context,\n        mediaSize: this._mediaSize\n      });\n    } finally {\n      this._context.restore();\n    }\n  };\n  CanvasRenderingTarget2D.prototype.useBitmapCoordinateSpace = function (f) {\n    try {\n      this._context.save();\n      // do not use resetTransform to support old versions of Edge\n      this._context.setTransform(1, 0, 0, 1, 0, 0);\n      return f({\n        context: this._context,\n        mediaSize: this._mediaSize,\n        bitmapSize: this._bitmapSize,\n        horizontalPixelRatio: this._horizontalPixelRatio,\n        verticalPixelRatio: this._verticalPixelRatio\n      });\n    } finally {\n      this._context.restore();\n    }\n  };\n  Object.defineProperty(CanvasRenderingTarget2D.prototype, \"_horizontalPixelRatio\", {\n    get: function () {\n      return this._bitmapSize.width / this._mediaSize.width;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(CanvasRenderingTarget2D.prototype, \"_verticalPixelRatio\", {\n    get: function () {\n      return this._bitmapSize.height / this._mediaSize.height;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  return CanvasRenderingTarget2D;\n}();\nexport { CanvasRenderingTarget2D };\n/**\n * @experimental\n */\nexport function createCanvasRenderingTarget2D(binding, contextOptions) {\n  var mediaSize = binding.canvasElementClientSize;\n  var bitmapSize = binding.bitmapSize;\n  var context = binding.canvasElement.getContext('2d', contextOptions);\n  if (context === null) {\n    throw new Error('Could not get 2d drawing context from bound canvas element. Has the canvas already been set to a different context mode?');\n  }\n  return new CanvasRenderingTarget2D(context, mediaSize, bitmapSize);\n}\n/**\n * @experimental\n */\nexport function tryCreateCanvasRenderingTarget2D(binding, contextOptions) {\n  var mediaSize = binding.canvasElementClientSize;\n  if (mediaSize.width === 0 || mediaSize.height === 0) {\n    return null;\n  }\n  var bitmapSize = binding.bitmapSize;\n  if (bitmapSize.width === 0 || bitmapSize.height === 0) {\n    return null;\n  }\n  var context = binding.canvasElement.getContext('2d', contextOptions);\n  if (context === null) {\n    return null;\n  }\n  return new CanvasRenderingTarget2D(context, mediaSize, bitmapSize);\n}", "map": {"version": 3, "names": ["CanvasRenderingTarget2D", "context", "mediaSize", "bitmapSize", "width", "height", "TypeError", "_mediaSize", "_bitmapSize", "_context", "prototype", "useMediaCoordinateSpace", "f", "save", "setTransform", "scale", "_horizontalPixelRatio", "_verticalPixelRatio", "restore", "useBitmapCoordinateSpace", "horizontalPixelRatio", "verticalPixelRatio", "Object", "defineProperty", "get", "enumerable", "configurable", "createCanvasRenderingTarget2D", "binding", "contextOptions", "canvasElementClientSize", "canvasElement", "getContext", "Error", "tryCreateCanvasRenderingTarget2D"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/fancy-canvas/canvas-rendering-target.mjs"], "sourcesContent": ["/**\n * @experimental\n */\nvar CanvasRenderingTarget2D = /** @class */ (function () {\n    function CanvasRenderingTarget2D(context, mediaSize, bitmapSize) {\n        if (mediaSize.width === 0 || mediaSize.height === 0) {\n            throw new TypeError('Rendering target could only be created on a media with positive width and height');\n        }\n        this._mediaSize = mediaSize;\n        // !Number.isInteger(bitmapSize.width) || !Number.isInteger(bitmapSize.height)\n        if (bitmapSize.width === 0 || bitmapSize.height === 0) {\n            throw new TypeError('Rendering target could only be created using a bitmap with positive integer width and height');\n        }\n        this._bitmapSize = bitmapSize;\n        this._context = context;\n    }\n    CanvasRenderingTarget2D.prototype.useMediaCoordinateSpace = function (f) {\n        try {\n            this._context.save();\n            // do not use resetTransform to support old versions of Edge\n            this._context.setTransform(1, 0, 0, 1, 0, 0);\n            this._context.scale(this._horizontalPixelRatio, this._verticalPixelRatio);\n            return f({\n                context: this._context,\n                mediaSize: this._mediaSize,\n            });\n        }\n        finally {\n            this._context.restore();\n        }\n    };\n    CanvasRenderingTarget2D.prototype.useBitmapCoordinateSpace = function (f) {\n        try {\n            this._context.save();\n            // do not use resetTransform to support old versions of Edge\n            this._context.setTransform(1, 0, 0, 1, 0, 0);\n            return f({\n                context: this._context,\n                mediaSize: this._mediaSize,\n                bitmapSize: this._bitmapSize,\n                horizontalPixelRatio: this._horizontalPixelRatio,\n                verticalPixelRatio: this._verticalPixelRatio,\n            });\n        }\n        finally {\n            this._context.restore();\n        }\n    };\n    Object.defineProperty(CanvasRenderingTarget2D.prototype, \"_horizontalPixelRatio\", {\n        get: function () {\n            return this._bitmapSize.width / this._mediaSize.width;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CanvasRenderingTarget2D.prototype, \"_verticalPixelRatio\", {\n        get: function () {\n            return this._bitmapSize.height / this._mediaSize.height;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CanvasRenderingTarget2D;\n}());\nexport { CanvasRenderingTarget2D };\n/**\n * @experimental\n */\nexport function createCanvasRenderingTarget2D(binding, contextOptions) {\n    var mediaSize = binding.canvasElementClientSize;\n    var bitmapSize = binding.bitmapSize;\n    var context = binding.canvasElement.getContext('2d', contextOptions);\n    if (context === null) {\n        throw new Error('Could not get 2d drawing context from bound canvas element. Has the canvas already been set to a different context mode?');\n    }\n    return new CanvasRenderingTarget2D(context, mediaSize, bitmapSize);\n}\n/**\n * @experimental\n */\nexport function tryCreateCanvasRenderingTarget2D(binding, contextOptions) {\n    var mediaSize = binding.canvasElementClientSize;\n    if (mediaSize.width === 0 || mediaSize.height === 0) {\n        return null;\n    }\n    var bitmapSize = binding.bitmapSize;\n    if (bitmapSize.width === 0 || bitmapSize.height === 0) {\n        return null;\n    }\n    var context = binding.canvasElement.getContext('2d', contextOptions);\n    if (context === null) {\n        return null;\n    }\n    return new CanvasRenderingTarget2D(context, mediaSize, bitmapSize);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,uBAAuB,GAAG,aAAe,YAAY;EACrD,SAASA,uBAAuBA,CAACC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAE;IAC7D,IAAID,SAAS,CAACE,KAAK,KAAK,CAAC,IAAIF,SAAS,CAACG,MAAM,KAAK,CAAC,EAAE;MACjD,MAAM,IAAIC,SAAS,CAAC,kFAAkF,CAAC;IAC3G;IACA,IAAI,CAACC,UAAU,GAAGL,SAAS;IAC3B;IACA,IAAIC,UAAU,CAACC,KAAK,KAAK,CAAC,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;MACnD,MAAM,IAAIC,SAAS,CAAC,8FAA8F,CAAC;IACvH;IACA,IAAI,CAACE,WAAW,GAAGL,UAAU;IAC7B,IAAI,CAACM,QAAQ,GAAGR,OAAO;EAC3B;EACAD,uBAAuB,CAACU,SAAS,CAACC,uBAAuB,GAAG,UAAUC,CAAC,EAAE;IACrE,IAAI;MACA,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,CAAC;MACpB;MACA,IAAI,CAACJ,QAAQ,CAACK,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5C,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC,IAAI,CAACC,qBAAqB,EAAE,IAAI,CAACC,mBAAmB,CAAC;MACzE,OAAOL,CAAC,CAAC;QACLX,OAAO,EAAE,IAAI,CAACQ,QAAQ;QACtBP,SAAS,EAAE,IAAI,CAACK;MACpB,CAAC,CAAC;IACN,CAAC,SACO;MACJ,IAAI,CAACE,QAAQ,CAACS,OAAO,CAAC,CAAC;IAC3B;EACJ,CAAC;EACDlB,uBAAuB,CAACU,SAAS,CAACS,wBAAwB,GAAG,UAAUP,CAAC,EAAE;IACtE,IAAI;MACA,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,CAAC;MACpB;MACA,IAAI,CAACJ,QAAQ,CAACK,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5C,OAAOF,CAAC,CAAC;QACLX,OAAO,EAAE,IAAI,CAACQ,QAAQ;QACtBP,SAAS,EAAE,IAAI,CAACK,UAAU;QAC1BJ,UAAU,EAAE,IAAI,CAACK,WAAW;QAC5BY,oBAAoB,EAAE,IAAI,CAACJ,qBAAqB;QAChDK,kBAAkB,EAAE,IAAI,CAACJ;MAC7B,CAAC,CAAC;IACN,CAAC,SACO;MACJ,IAAI,CAACR,QAAQ,CAACS,OAAO,CAAC,CAAC;IAC3B;EACJ,CAAC;EACDI,MAAM,CAACC,cAAc,CAACvB,uBAAuB,CAACU,SAAS,EAAE,uBAAuB,EAAE;IAC9Ec,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAAChB,WAAW,CAACJ,KAAK,GAAG,IAAI,CAACG,UAAU,CAACH,KAAK;IACzD,CAAC;IACDqB,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFJ,MAAM,CAACC,cAAc,CAACvB,uBAAuB,CAACU,SAAS,EAAE,qBAAqB,EAAE;IAC5Ec,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAAChB,WAAW,CAACH,MAAM,GAAG,IAAI,CAACE,UAAU,CAACF,MAAM;IAC3D,CAAC;IACDoB,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,OAAO1B,uBAAuB;AAClC,CAAC,CAAC,CAAE;AACJ,SAASA,uBAAuB;AAChC;AACA;AACA;AACA,OAAO,SAAS2B,6BAA6BA,CAACC,OAAO,EAAEC,cAAc,EAAE;EACnE,IAAI3B,SAAS,GAAG0B,OAAO,CAACE,uBAAuB;EAC/C,IAAI3B,UAAU,GAAGyB,OAAO,CAACzB,UAAU;EACnC,IAAIF,OAAO,GAAG2B,OAAO,CAACG,aAAa,CAACC,UAAU,CAAC,IAAI,EAAEH,cAAc,CAAC;EACpE,IAAI5B,OAAO,KAAK,IAAI,EAAE;IAClB,MAAM,IAAIgC,KAAK,CAAC,0HAA0H,CAAC;EAC/I;EACA,OAAO,IAAIjC,uBAAuB,CAACC,OAAO,EAAEC,SAAS,EAAEC,UAAU,CAAC;AACtE;AACA;AACA;AACA;AACA,OAAO,SAAS+B,gCAAgCA,CAACN,OAAO,EAAEC,cAAc,EAAE;EACtE,IAAI3B,SAAS,GAAG0B,OAAO,CAACE,uBAAuB;EAC/C,IAAI5B,SAAS,CAACE,KAAK,KAAK,CAAC,IAAIF,SAAS,CAACG,MAAM,KAAK,CAAC,EAAE;IACjD,OAAO,IAAI;EACf;EACA,IAAIF,UAAU,GAAGyB,OAAO,CAACzB,UAAU;EACnC,IAAIA,UAAU,CAACC,KAAK,KAAK,CAAC,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;IACnD,OAAO,IAAI;EACf;EACA,IAAIJ,OAAO,GAAG2B,OAAO,CAACG,aAAa,CAACC,UAAU,CAAC,IAAI,EAAEH,cAAc,CAAC;EACpE,IAAI5B,OAAO,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACf;EACA,OAAO,IAAID,uBAAuB,CAACC,OAAO,EAAEC,SAAS,EAAEC,UAAU,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}