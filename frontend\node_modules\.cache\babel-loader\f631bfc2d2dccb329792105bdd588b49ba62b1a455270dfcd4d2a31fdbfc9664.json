{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"disableSpacing\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getCardActionsUtilityClass } from './cardActionsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getCardActionsUtilityClass, classes);\n};\nconst CardActionsRoot = styled('div', {\n  name: 'MuiCardActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8\n}, !ownerState.disableSpacing && {\n  '& > :not(style) ~ :not(style)': {\n    marginLeft: 8\n  }\n}));\nconst CardActions = /*#__PURE__*/React.forwardRef(function CardActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActions'\n  });\n  const {\n      disableSpacing = false,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableSpacing\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardActionsRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActions;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getCardActionsUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "disableSpacing", "slots", "root", "CardActionsRoot", "name", "slot", "overridesResolver", "props", "styles", "spacing", "display", "alignItems", "padding", "marginLeft", "CardActions", "forwardRef", "inProps", "ref", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/CardActions/CardActions.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"disableSpacing\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getCardActionsUtilityClass } from './cardActionsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getCardActionsUtilityClass, classes);\n};\nconst CardActionsRoot = styled('div', {\n  name: 'MuiCardActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8\n}, !ownerState.disableSpacing && {\n  '& > :not(style) ~ :not(style)': {\n    marginLeft: 8\n  }\n}));\nconst CardActions = /*#__PURE__*/React.forwardRef(function CardActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActions'\n  });\n  const {\n      disableSpacing = false,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableSpacing\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardActionsRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActions;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,gBAAgB,EAAE,WAAW,CAAC;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACF,cAAc,IAAI,SAAS;EAC7C,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAEP,0BAA0B,EAAEK,OAAO,CAAC;AACnE,CAAC;AACD,MAAMI,eAAe,GAAGX,MAAM,CAAC,KAAK,EAAE;EACpCY,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAE,CAACJ,UAAU,CAACE,cAAc,IAAIQ,MAAM,CAACC,OAAO,CAAC;EACpE;AACF,CAAC,CAAC,CAAC,CAAC;EACFX;AACF,CAAC,KAAKZ,QAAQ,CAAC;EACbwB,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE;AACX,CAAC,EAAE,CAACd,UAAU,CAACE,cAAc,IAAI;EAC/B,+BAA+B,EAAE;IAC/Ba,UAAU,EAAE;EACd;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,WAAW,GAAG,aAAa1B,KAAK,CAAC2B,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMV,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFJ,cAAc,GAAG,KAAK;MACtBkB;IACF,CAAC,GAAGX,KAAK;IACTY,KAAK,GAAGlC,6BAA6B,CAACsB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAMW,UAAU,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCP;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,eAAe,EAAEjB,QAAQ,CAAC;IACjDgC,SAAS,EAAE5B,IAAI,CAACS,OAAO,CAACG,IAAI,EAAEgB,SAAS,CAAC;IACxCpB,UAAU,EAAEA,UAAU;IACtBmB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,WAAW,CAACS,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEnC,SAAS,CAACoC,IAAI;EACxB;AACF;AACA;EACE1B,OAAO,EAAEV,SAAS,CAACqC,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAE7B,SAAS,CAACsC,MAAM;EAC3B;AACF;AACA;AACA;EACE3B,cAAc,EAAEX,SAAS,CAACuC,IAAI;EAC9B;AACF;AACA;EACEC,EAAE,EAAExC,SAAS,CAACyC,SAAS,CAAC,CAACzC,SAAS,CAAC0C,OAAO,CAAC1C,SAAS,CAACyC,SAAS,CAAC,CAACzC,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAACqC,MAAM,EAAErC,SAAS,CAACuC,IAAI,CAAC,CAAC,CAAC,EAAEvC,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAACqC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}