{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"dividers\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDialogContentUtilityClass } from './dialogContentClasses';\nimport dialogTitleClasses from '../DialogTitle/dialogTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    dividers\n  } = ownerState;\n  const slots = {\n    root: ['root', dividers && 'dividers']\n  };\n  return composeClasses(slots, getDialogContentUtilityClass, classes);\n};\nconst DialogContentRoot = styled('div', {\n  name: 'MuiDialogContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.dividers && styles.dividers];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    flex: '1 1 auto',\n    // Add iOS momentum scrolling for iOS < 13.0\n    WebkitOverflowScrolling: 'touch',\n    overflowY: 'auto',\n    padding: '20px 24px'\n  }, ownerState.dividers ? {\n    padding: '16px 24px',\n    borderTop: \"1px solid \".concat((theme.vars || theme).palette.divider),\n    borderBottom: \"1px solid \".concat((theme.vars || theme).palette.divider)\n  } : {\n    [\".\".concat(dialogTitleClasses.root, \" + &\")]: {\n      paddingTop: 0\n    }\n  });\n});\nconst DialogContent = /*#__PURE__*/React.forwardRef(function DialogContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogContent'\n  });\n  const {\n      className,\n      dividers = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    dividers\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogContentRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Display the top and bottom dividers.\n   * @default false\n   */\n  dividers: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContent;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getDialogContentUtilityClass", "dialogTitleClasses", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "dividers", "slots", "root", "DialogContentRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "flex", "WebkitOverflowScrolling", "overflowY", "padding", "borderTop", "concat", "vars", "palette", "divider", "borderBottom", "paddingTop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "inProps", "ref", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/DialogContent/DialogContent.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"dividers\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDialogContentUtilityClass } from './dialogContentClasses';\nimport dialogTitleClasses from '../DialogTitle/dialogTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    dividers\n  } = ownerState;\n  const slots = {\n    root: ['root', dividers && 'dividers']\n  };\n  return composeClasses(slots, getDialogContentUtilityClass, classes);\n};\nconst DialogContentRoot = styled('div', {\n  name: 'MuiDialogContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.dividers && styles.dividers];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  flex: '1 1 auto',\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  overflowY: 'auto',\n  padding: '20px 24px'\n}, ownerState.dividers ? {\n  padding: '16px 24px',\n  borderTop: `1px solid ${(theme.vars || theme).palette.divider}`,\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n} : {\n  [`.${dialogTitleClasses.root} + &`]: {\n    paddingTop: 0\n  }\n}));\nconst DialogContent = /*#__PURE__*/React.forwardRef(function DialogContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogContent'\n  });\n  const {\n      className,\n      dividers = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    dividers\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogContentRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Display the top and bottom dividers.\n   * @default false\n   */\n  dividers: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,QAAQ,IAAI,UAAU;EACvC,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAER,4BAA4B,EAAEM,OAAO,CAAC;AACrE,CAAC;AACD,MAAMI,iBAAiB,GAAGZ,MAAM,CAAC,KAAK,EAAE;EACtCa,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACE,QAAQ,IAAIQ,MAAM,CAACR,QAAQ,CAAC;EAC9D;AACF,CAAC,CAAC,CAACS,IAAA;EAAA,IAAC;IACFC,KAAK;IACLZ;EACF,CAAC,GAAAW,IAAA;EAAA,OAAKxB,QAAQ,CAAC;IACb0B,IAAI,EAAE,UAAU;IAChB;IACAC,uBAAuB,EAAE,OAAO;IAChCC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE;EACX,CAAC,EAAEhB,UAAU,CAACE,QAAQ,GAAG;IACvBc,OAAO,EAAE,WAAW;IACpBC,SAAS,eAAAC,MAAA,CAAe,CAACN,KAAK,CAACO,IAAI,IAAIP,KAAK,EAAEQ,OAAO,CAACC,OAAO,CAAE;IAC/DC,YAAY,eAAAJ,MAAA,CAAe,CAACN,KAAK,CAACO,IAAI,IAAIP,KAAK,EAAEQ,OAAO,CAACC,OAAO;EAClE,CAAC,GAAG;IACF,KAAAH,MAAA,CAAKtB,kBAAkB,CAACQ,IAAI,YAAS;MACnCmB,UAAU,EAAE;IACd;EACF,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,aAAa,GAAG,aAAanC,KAAK,CAACoC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMlB,KAAK,GAAGf,eAAe,CAAC;IAC5Be,KAAK,EAAEiB,OAAO;IACdpB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFsB,SAAS;MACT1B,QAAQ,GAAG;IACb,CAAC,GAAGO,KAAK;IACToB,KAAK,GAAG3C,6BAA6B,CAACuB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMY,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCP;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,iBAAiB,EAAElB,QAAQ,CAAC;IACnDyC,SAAS,EAAErC,IAAI,CAACU,OAAO,CAACG,IAAI,EAAEwB,SAAS,CAAC;IACxC5B,UAAU,EAAEA,UAAU;IACtB2B,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,aAAa,CAACS,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAE5C,SAAS,CAAC6C,IAAI;EACxB;AACF;AACA;EACElC,OAAO,EAAEX,SAAS,CAAC8C,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAEtC,SAAS,CAAC+C,MAAM;EAC3B;AACF;AACA;AACA;EACEnC,QAAQ,EAAEZ,SAAS,CAACgD,IAAI;EACxB;AACF;AACA;EACEC,EAAE,EAAEjD,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACmD,OAAO,CAACnD,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACoD,IAAI,EAAEpD,SAAS,CAAC8C,MAAM,EAAE9C,SAAS,CAACgD,IAAI,CAAC,CAAC,CAAC,EAAEhD,SAAS,CAACoD,IAAI,EAAEpD,SAAS,CAAC8C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}