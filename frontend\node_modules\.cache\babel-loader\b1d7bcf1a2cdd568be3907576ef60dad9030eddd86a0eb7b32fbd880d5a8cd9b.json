{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"transitionEnd\", \"transition\"];\nimport { isNumericalString } from '../../utils/is-numerical-string.mjs';\nimport { isZeroValueString } from '../../utils/is-zero-value-string.mjs';\nimport { resolveFinalValueInKeyframes } from '../../utils/resolve-value.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../dom/value-types/animatable-none.mjs';\nimport { findValueType } from '../dom/value-types/find.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n  if (visualElement.hasValue(key)) {\n    visualElement.getValue(key).set(value);\n  } else {\n    visualElement.addValue(key, motionValue(value));\n  }\n}\nfunction setTarget(visualElement, definition) {\n  const resolved = resolveVariant(visualElement, definition);\n  let _ref = resolved ? visualElement.makeTargetAnimatable(resolved, false) : {},\n    {\n      transitionEnd = {},\n      transition = {}\n    } = _ref,\n    target = _objectWithoutProperties(_ref, _excluded);\n  target = _objectSpread(_objectSpread({}, target), transitionEnd);\n  for (const key in target) {\n    const value = resolveFinalValueInKeyframes(target[key]);\n    setMotionValue(visualElement, key, value);\n  }\n}\nfunction setVariants(visualElement, variantLabels) {\n  const reversedLabels = [...variantLabels].reverse();\n  reversedLabels.forEach(key => {\n    const variant = visualElement.getVariant(key);\n    variant && setTarget(visualElement, variant);\n    if (visualElement.variantChildren) {\n      visualElement.variantChildren.forEach(child => {\n        setVariants(child, variantLabels);\n      });\n    }\n  });\n}\nfunction setValues(visualElement, definition) {\n  if (Array.isArray(definition)) {\n    return setVariants(visualElement, definition);\n  } else if (typeof definition === \"string\") {\n    return setVariants(visualElement, [definition]);\n  } else {\n    setTarget(visualElement, definition);\n  }\n}\nfunction checkTargetForNewValues(visualElement, target, origin) {\n  var _a, _b;\n  const newValueKeys = Object.keys(target).filter(key => !visualElement.hasValue(key));\n  const numNewValues = newValueKeys.length;\n  if (!numNewValues) return;\n  for (let i = 0; i < numNewValues; i++) {\n    const key = newValueKeys[i];\n    const targetValue = target[key];\n    let value = null;\n    /**\n     * If the target is a series of keyframes, we can use the first value\n     * in the array. If this first value is null, we'll still need to read from the DOM.\n     */\n    if (Array.isArray(targetValue)) {\n      value = targetValue[0];\n    }\n    /**\n     * If the target isn't keyframes, or the first keyframe was null, we need to\n     * first check if an origin value was explicitly defined in the transition as \"from\",\n     * if not read the value from the DOM. As an absolute fallback, take the defined target value.\n     */\n    if (value === null) {\n      value = (_b = (_a = origin[key]) !== null && _a !== void 0 ? _a : visualElement.readValue(key)) !== null && _b !== void 0 ? _b : target[key];\n    }\n    /**\n     * If value is still undefined or null, ignore it. Preferably this would throw,\n     * but this was causing issues in Framer.\n     */\n    if (value === undefined || value === null) continue;\n    if (typeof value === \"string\" && (isNumericalString(value) || isZeroValueString(value))) {\n      // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n      value = parseFloat(value);\n    } else if (!findValueType(value) && complex.test(targetValue)) {\n      value = getAnimatableNone(key, targetValue);\n    }\n    visualElement.addValue(key, motionValue(value, {\n      owner: visualElement\n    }));\n    if (origin[key] === undefined) {\n      origin[key] = value;\n    }\n    if (value !== null) visualElement.setBaseTarget(key, value);\n  }\n}\nfunction getOriginFromTransition(key, transition) {\n  if (!transition) return;\n  const valueTransition = transition[key] || transition[\"default\"] || transition;\n  return valueTransition.from;\n}\nfunction getOrigin(target, transition, visualElement) {\n  const origin = {};\n  for (const key in target) {\n    const transitionOrigin = getOriginFromTransition(key, transition);\n    if (transitionOrigin !== undefined) {\n      origin[key] = transitionOrigin;\n    } else {\n      const value = visualElement.getValue(key);\n      if (value) {\n        origin[key] = value.get();\n      }\n    }\n  }\n  return origin;\n}\nexport { checkTargetForNewValues, getOrigin, getOriginFromTransition, setTarget, setValues };", "map": {"version": 3, "names": ["isNumericalString", "isZeroValueString", "resolveFinalValueInKeyframes", "motionValue", "complex", "getAnimatableNone", "findValueType", "resolveV<PERSON>t", "setMotionValue", "visualElement", "key", "value", "hasValue", "getValue", "set", "addValue", "<PERSON><PERSON><PERSON><PERSON>", "definition", "resolved", "_ref", "makeTargetAnimatable", "transitionEnd", "transition", "target", "_objectWithoutProperties", "_excluded", "_objectSpread", "setVariants", "variantLabels", "<PERSON><PERSON><PERSON><PERSON>", "reverse", "for<PERSON>ach", "variant", "getVariant", "variant<PERSON><PERSON><PERSON>n", "child", "set<PERSON><PERSON><PERSON>", "Array", "isArray", "checkTargetForNewValues", "origin", "_a", "_b", "newValueKeys", "Object", "keys", "filter", "numNewV<PERSON>ues", "length", "i", "targetValue", "readValue", "undefined", "parseFloat", "test", "owner", "set<PERSON><PERSON><PERSON><PERSON>get", "getOriginFromTransition", "valueTransition", "from", "<PERSON><PERSON><PERSON><PERSON>", "transitionOrigin", "get"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/render/utils/setters.mjs"], "sourcesContent": ["import { isNumericalString } from '../../utils/is-numerical-string.mjs';\nimport { isZeroValueString } from '../../utils/is-zero-value-string.mjs';\nimport { resolveFinalValueInKeyframes } from '../../utils/resolve-value.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../dom/value-types/animatable-none.mjs';\nimport { findValueType } from '../dom/value-types/find.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved ? visualElement.makeTargetAnimatable(resolved, false) : {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\nfunction setVariants(visualElement, variantLabels) {\n    const reversedLabels = [...variantLabels].reverse();\n    reversedLabels.forEach((key) => {\n        const variant = visualElement.getVariant(key);\n        variant && setTarget(visualElement, variant);\n        if (visualElement.variantChildren) {\n            visualElement.variantChildren.forEach((child) => {\n                setVariants(child, variantLabels);\n            });\n        }\n    });\n}\nfunction setValues(visualElement, definition) {\n    if (Array.isArray(definition)) {\n        return setVariants(visualElement, definition);\n    }\n    else if (typeof definition === \"string\") {\n        return setVariants(visualElement, [definition]);\n    }\n    else {\n        setTarget(visualElement, definition);\n    }\n}\nfunction checkTargetForNewValues(visualElement, target, origin) {\n    var _a, _b;\n    const newValueKeys = Object.keys(target).filter((key) => !visualElement.hasValue(key));\n    const numNewValues = newValueKeys.length;\n    if (!numNewValues)\n        return;\n    for (let i = 0; i < numNewValues; i++) {\n        const key = newValueKeys[i];\n        const targetValue = target[key];\n        let value = null;\n        /**\n         * If the target is a series of keyframes, we can use the first value\n         * in the array. If this first value is null, we'll still need to read from the DOM.\n         */\n        if (Array.isArray(targetValue)) {\n            value = targetValue[0];\n        }\n        /**\n         * If the target isn't keyframes, or the first keyframe was null, we need to\n         * first check if an origin value was explicitly defined in the transition as \"from\",\n         * if not read the value from the DOM. As an absolute fallback, take the defined target value.\n         */\n        if (value === null) {\n            value = (_b = (_a = origin[key]) !== null && _a !== void 0 ? _a : visualElement.readValue(key)) !== null && _b !== void 0 ? _b : target[key];\n        }\n        /**\n         * If value is still undefined or null, ignore it. Preferably this would throw,\n         * but this was causing issues in Framer.\n         */\n        if (value === undefined || value === null)\n            continue;\n        if (typeof value === \"string\" &&\n            (isNumericalString(value) || isZeroValueString(value))) {\n            // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n            value = parseFloat(value);\n        }\n        else if (!findValueType(value) && complex.test(targetValue)) {\n            value = getAnimatableNone(key, targetValue);\n        }\n        visualElement.addValue(key, motionValue(value, { owner: visualElement }));\n        if (origin[key] === undefined) {\n            origin[key] = value;\n        }\n        if (value !== null)\n            visualElement.setBaseTarget(key, value);\n    }\n}\nfunction getOriginFromTransition(key, transition) {\n    if (!transition)\n        return;\n    const valueTransition = transition[key] || transition[\"default\"] || transition;\n    return valueTransition.from;\n}\nfunction getOrigin(target, transition, visualElement) {\n    const origin = {};\n    for (const key in target) {\n        const transitionOrigin = getOriginFromTransition(key, transition);\n        if (transitionOrigin !== undefined) {\n            origin[key] = transitionOrigin;\n        }\n        else {\n            const value = visualElement.getValue(key);\n            if (value) {\n                origin[key] = value.get();\n            }\n        }\n    }\n    return origin;\n}\n\nexport { checkTargetForNewValues, getOrigin, getOriginFromTransition, setTarget, setValues };\n"], "mappings": ";;;AAAA,SAASA,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,4BAA4B,QAAQ,+BAA+B;AAC5E,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,gCAAgC;;AAE/D;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC/C,IAAIF,aAAa,CAACG,QAAQ,CAACF,GAAG,CAAC,EAAE;IAC7BD,aAAa,CAACI,QAAQ,CAACH,GAAG,CAAC,CAACI,GAAG,CAACH,KAAK,CAAC;EAC1C,CAAC,MACI;IACDF,aAAa,CAACM,QAAQ,CAACL,GAAG,EAAEP,WAAW,CAACQ,KAAK,CAAC,CAAC;EACnD;AACJ;AACA,SAASK,SAASA,CAACP,aAAa,EAAEQ,UAAU,EAAE;EAC1C,MAAMC,QAAQ,GAAGX,cAAc,CAACE,aAAa,EAAEQ,UAAU,CAAC;EAC1D,IAAAE,IAAA,GAAyDD,QAAQ,GAAGT,aAAa,CAACW,oBAAoB,CAACF,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IAAxH;MAAEG,aAAa,GAAG,CAAC,CAAC;MAAEC,UAAU,GAAG,CAAC;IAAa,CAAC,GAAAH,IAAA;IAARI,MAAM,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EACpDF,MAAM,GAAAG,aAAA,CAAAA,aAAA,KAAQH,MAAM,GAAKF,aAAa,CAAE;EACxC,KAAK,MAAMX,GAAG,IAAIa,MAAM,EAAE;IACtB,MAAMZ,KAAK,GAAGT,4BAA4B,CAACqB,MAAM,CAACb,GAAG,CAAC,CAAC;IACvDF,cAAc,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,CAAC;EAC7C;AACJ;AACA,SAASgB,WAAWA,CAAClB,aAAa,EAAEmB,aAAa,EAAE;EAC/C,MAAMC,cAAc,GAAG,CAAC,GAAGD,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC;EACnDD,cAAc,CAACE,OAAO,CAAErB,GAAG,IAAK;IAC5B,MAAMsB,OAAO,GAAGvB,aAAa,CAACwB,UAAU,CAACvB,GAAG,CAAC;IAC7CsB,OAAO,IAAIhB,SAAS,CAACP,aAAa,EAAEuB,OAAO,CAAC;IAC5C,IAAIvB,aAAa,CAACyB,eAAe,EAAE;MAC/BzB,aAAa,CAACyB,eAAe,CAACH,OAAO,CAAEI,KAAK,IAAK;QAC7CR,WAAW,CAACQ,KAAK,EAAEP,aAAa,CAAC;MACrC,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN;AACA,SAASQ,SAASA,CAAC3B,aAAa,EAAEQ,UAAU,EAAE;EAC1C,IAAIoB,KAAK,CAACC,OAAO,CAACrB,UAAU,CAAC,EAAE;IAC3B,OAAOU,WAAW,CAAClB,aAAa,EAAEQ,UAAU,CAAC;EACjD,CAAC,MACI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACrC,OAAOU,WAAW,CAAClB,aAAa,EAAE,CAACQ,UAAU,CAAC,CAAC;EACnD,CAAC,MACI;IACDD,SAAS,CAACP,aAAa,EAAEQ,UAAU,CAAC;EACxC;AACJ;AACA,SAASsB,uBAAuBA,CAAC9B,aAAa,EAAEc,MAAM,EAAEiB,MAAM,EAAE;EAC5D,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACtB,MAAM,CAAC,CAACuB,MAAM,CAAEpC,GAAG,IAAK,CAACD,aAAa,CAACG,QAAQ,CAACF,GAAG,CAAC,CAAC;EACtF,MAAMqC,YAAY,GAAGJ,YAAY,CAACK,MAAM;EACxC,IAAI,CAACD,YAAY,EACb;EACJ,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAE;IACnC,MAAMvC,GAAG,GAAGiC,YAAY,CAACM,CAAC,CAAC;IAC3B,MAAMC,WAAW,GAAG3B,MAAM,CAACb,GAAG,CAAC;IAC/B,IAAIC,KAAK,GAAG,IAAI;IAChB;AACR;AACA;AACA;IACQ,IAAI0B,KAAK,CAACC,OAAO,CAACY,WAAW,CAAC,EAAE;MAC5BvC,KAAK,GAAGuC,WAAW,CAAC,CAAC,CAAC;IAC1B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIvC,KAAK,KAAK,IAAI,EAAE;MAChBA,KAAK,GAAG,CAAC+B,EAAE,GAAG,CAACD,EAAE,GAAGD,MAAM,CAAC9B,GAAG,CAAC,MAAM,IAAI,IAAI+B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGhC,aAAa,CAAC0C,SAAS,CAACzC,GAAG,CAAC,MAAM,IAAI,IAAIgC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGnB,MAAM,CAACb,GAAG,CAAC;IAChJ;IACA;AACR;AACA;AACA;IACQ,IAAIC,KAAK,KAAKyC,SAAS,IAAIzC,KAAK,KAAK,IAAI,EACrC;IACJ,IAAI,OAAOA,KAAK,KAAK,QAAQ,KACxBX,iBAAiB,CAACW,KAAK,CAAC,IAAIV,iBAAiB,CAACU,KAAK,CAAC,CAAC,EAAE;MACxD;MACAA,KAAK,GAAG0C,UAAU,CAAC1C,KAAK,CAAC;IAC7B,CAAC,MACI,IAAI,CAACL,aAAa,CAACK,KAAK,CAAC,IAAIP,OAAO,CAACkD,IAAI,CAACJ,WAAW,CAAC,EAAE;MACzDvC,KAAK,GAAGN,iBAAiB,CAACK,GAAG,EAAEwC,WAAW,CAAC;IAC/C;IACAzC,aAAa,CAACM,QAAQ,CAACL,GAAG,EAAEP,WAAW,CAACQ,KAAK,EAAE;MAAE4C,KAAK,EAAE9C;IAAc,CAAC,CAAC,CAAC;IACzE,IAAI+B,MAAM,CAAC9B,GAAG,CAAC,KAAK0C,SAAS,EAAE;MAC3BZ,MAAM,CAAC9B,GAAG,CAAC,GAAGC,KAAK;IACvB;IACA,IAAIA,KAAK,KAAK,IAAI,EACdF,aAAa,CAAC+C,aAAa,CAAC9C,GAAG,EAAEC,KAAK,CAAC;EAC/C;AACJ;AACA,SAAS8C,uBAAuBA,CAAC/C,GAAG,EAAEY,UAAU,EAAE;EAC9C,IAAI,CAACA,UAAU,EACX;EACJ,MAAMoC,eAAe,GAAGpC,UAAU,CAACZ,GAAG,CAAC,IAAIY,UAAU,CAAC,SAAS,CAAC,IAAIA,UAAU;EAC9E,OAAOoC,eAAe,CAACC,IAAI;AAC/B;AACA,SAASC,SAASA,CAACrC,MAAM,EAAED,UAAU,EAAEb,aAAa,EAAE;EAClD,MAAM+B,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAM9B,GAAG,IAAIa,MAAM,EAAE;IACtB,MAAMsC,gBAAgB,GAAGJ,uBAAuB,CAAC/C,GAAG,EAAEY,UAAU,CAAC;IACjE,IAAIuC,gBAAgB,KAAKT,SAAS,EAAE;MAChCZ,MAAM,CAAC9B,GAAG,CAAC,GAAGmD,gBAAgB;IAClC,CAAC,MACI;MACD,MAAMlD,KAAK,GAAGF,aAAa,CAACI,QAAQ,CAACH,GAAG,CAAC;MACzC,IAAIC,KAAK,EAAE;QACP6B,MAAM,CAAC9B,GAAG,CAAC,GAAGC,KAAK,CAACmD,GAAG,CAAC,CAAC;MAC7B;IACJ;EACJ;EACA,OAAOtB,MAAM;AACjB;AAEA,SAASD,uBAAuB,EAAEqB,SAAS,EAAEH,uBAAuB,EAAEzC,SAAS,EAAEoB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}