{"ast": null, "code": "/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({\n  when,\n  delay: _delay,\n  delayChildren,\n  staggerChildren,\n  staggerDirection,\n  repeat,\n  repeatType,\n  repeatDelay,\n  from,\n  elapsed,\n  ...transition\n}) {\n  return !!Object.keys(transition).length;\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] || transition[\"default\"] || transition;\n}\nexport { getValueTransition, isTransitionDefined };", "map": {"version": 3, "names": ["isTransitionDefined", "when", "delay", "_delay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "repeat", "repeatType", "repeatDelay", "from", "elapsed", "transition", "Object", "keys", "length", "getValueTransition", "key"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/animation/utils/transitions.mjs"], "sourcesContent": ["/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, elapsed, ...transition }) {\n    return !!Object.keys(transition).length;\n}\nfunction getValueTransition(transition, key) {\n    return transition[key] || transition[\"default\"] || transition;\n}\n\nexport { getValueTransition, isTransitionDefined };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmBA,CAAC;EAAEC,IAAI;EAAEC,KAAK,EAAEC,MAAM;EAAEC,aAAa;EAAEC,eAAe;EAAEC,gBAAgB;EAAEC,MAAM;EAAEC,UAAU;EAAEC,WAAW;EAAEC,IAAI;EAAEC,OAAO;EAAE,GAAGC;AAAW,CAAC,EAAE;EACnK,OAAO,CAAC,CAACC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,MAAM;AAC3C;AACA,SAASC,kBAAkBA,CAACJ,UAAU,EAAEK,GAAG,EAAE;EACzC,OAAOL,UAAU,CAACK,GAAG,CAAC,IAAIL,UAAU,CAAC,SAAS,CAAC,IAAIA,UAAU;AACjE;AAEA,SAASI,kBAAkB,EAAEhB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}