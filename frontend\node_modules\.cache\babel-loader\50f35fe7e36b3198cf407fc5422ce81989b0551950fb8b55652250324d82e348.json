{"ast": null, "code": "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n  var _generatorOptions = generatorOptions,\n    _generatorOptions$def = _generatorOptions.defaultModifiers,\n    defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n    _generatorOptions$def2 = _generatorOptions.defaultOptions,\n    defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n        var _state$elements = state.elements,\n          reference = _state$elements.reference,\n          popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n          var _state$orderedModifie = state.orderedModifiers[index],\n            fn = _state$orderedModifie.fn,\n            _state$orderedModifie2 = _state$orderedModifie.options,\n            _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n            name = _state$orderedModifie.name;\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n          _ref$options = _ref.options,\n          options = _ref$options === void 0 ? {} : _ref$options,\n          effect = _ref.effect;\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n          var noopFn = function noopFn() {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "map": {"version": 3, "names": ["getCompositeRect", "getLayoutRect", "listScrollParents", "getOffsetParent", "orderModifiers", "debounce", "mergeByName", "detectOverflow", "isElement", "DEFAULT_OPTIONS", "placement", "modifiers", "strategy", "areValidElements", "_len", "arguments", "length", "args", "Array", "_key", "some", "element", "getBoundingClientRect", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "createPopper", "reference", "popper", "options", "state", "orderedModifiers", "Object", "assign", "modifiersData", "elements", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "contextElement", "concat", "filter", "m", "enabled", "runModifierEffects", "update", "forceUpdate", "_state$elements", "rects", "reset", "for<PERSON>ach", "modifier", "name", "data", "index", "_state$orderedModifie", "fn", "_state$orderedModifie2", "_options", "Promise", "resolve", "destroy", "then", "onFirstUpdate", "_ref", "_ref$options", "effect", "cleanupFn", "noopFn", "push"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@popperjs/core/lib/createPopper.js"], "sourcesContent": ["import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,IAAIC,eAAe,GAAG;EACpBC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE;AACZ,CAAC;AAED,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EAEA,OAAO,CAACF,IAAI,CAACG,IAAI,CAAC,UAAUC,OAAO,EAAE;IACnC,OAAO,EAAEA,OAAO,IAAI,OAAOA,OAAO,CAACC,qBAAqB,KAAK,UAAU,CAAC;EAC1E,CAAC,CAAC;AACJ;AAEA,OAAO,SAASC,eAAeA,CAACC,gBAAgB,EAAE;EAChD,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAC/BA,gBAAgB,GAAG,CAAC,CAAC;EACvB;EAEA,IAAIC,iBAAiB,GAAGD,gBAAgB;IACpCE,qBAAqB,GAAGD,iBAAiB,CAACE,gBAAgB;IAC1DA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAChFE,sBAAsB,GAAGH,iBAAiB,CAACI,cAAc;IACzDA,cAAc,GAAGD,sBAAsB,KAAK,KAAK,CAAC,GAAGnB,eAAe,GAAGmB,sBAAsB;EACjG,OAAO,SAASE,YAAYA,CAACC,SAAS,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACvD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MACtBA,OAAO,GAAGJ,cAAc;IAC1B;IAEA,IAAIK,KAAK,GAAG;MACVxB,SAAS,EAAE,QAAQ;MACnByB,gBAAgB,EAAE,EAAE;MACpBF,OAAO,EAAEG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5B,eAAe,EAAEoB,cAAc,CAAC;MAC3DS,aAAa,EAAE,CAAC,CAAC;MACjBC,QAAQ,EAAE;QACRR,SAAS,EAAEA,SAAS;QACpBC,MAAM,EAAEA;MACV,CAAC;MACDQ,UAAU,EAAE,CAAC,CAAC;MACdC,MAAM,EAAE,CAAC;IACX,CAAC;IACD,IAAIC,gBAAgB,GAAG,EAAE;IACzB,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAIC,QAAQ,GAAG;MACbV,KAAK,EAAEA,KAAK;MACZW,UAAU,EAAE,SAASA,UAAUA,CAACC,gBAAgB,EAAE;QAChD,IAAIb,OAAO,GAAG,OAAOa,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACZ,KAAK,CAACD,OAAO,CAAC,GAAGa,gBAAgB;QACzGC,sBAAsB,CAAC,CAAC;QACxBb,KAAK,CAACD,OAAO,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,cAAc,EAAEK,KAAK,CAACD,OAAO,EAAEA,OAAO,CAAC;QACzEC,KAAK,CAACc,aAAa,GAAG;UACpBjB,SAAS,EAAEvB,SAAS,CAACuB,SAAS,CAAC,GAAG7B,iBAAiB,CAAC6B,SAAS,CAAC,GAAGA,SAAS,CAACkB,cAAc,GAAG/C,iBAAiB,CAAC6B,SAAS,CAACkB,cAAc,CAAC,GAAG,EAAE;UAC5IjB,MAAM,EAAE9B,iBAAiB,CAAC8B,MAAM;QAClC,CAAC,CAAC,CAAC;QACH;;QAEA,IAAIG,gBAAgB,GAAG/B,cAAc,CAACE,WAAW,CAAC,EAAE,CAAC4C,MAAM,CAACvB,gBAAgB,EAAEO,KAAK,CAACD,OAAO,CAACtB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE1GuB,KAAK,CAACC,gBAAgB,GAAGA,gBAAgB,CAACgB,MAAM,CAAC,UAAUC,CAAC,EAAE;UAC5D,OAAOA,CAAC,CAACC,OAAO;QAClB,CAAC,CAAC;QACFC,kBAAkB,CAAC,CAAC;QACpB,OAAOV,QAAQ,CAACW,MAAM,CAAC,CAAC;MAC1B,CAAC;MACD;MACA;MACA;MACA;MACA;MACAC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC,IAAIb,WAAW,EAAE;UACf;QACF;QAEA,IAAIc,eAAe,GAAGvB,KAAK,CAACK,QAAQ;UAChCR,SAAS,GAAG0B,eAAe,CAAC1B,SAAS;UACrCC,MAAM,GAAGyB,eAAe,CAACzB,MAAM,CAAC,CAAC;QACrC;;QAEA,IAAI,CAACnB,gBAAgB,CAACkB,SAAS,EAAEC,MAAM,CAAC,EAAE;UACxC;QACF,CAAC,CAAC;;QAGFE,KAAK,CAACwB,KAAK,GAAG;UACZ3B,SAAS,EAAE/B,gBAAgB,CAAC+B,SAAS,EAAE5B,eAAe,CAAC6B,MAAM,CAAC,EAAEE,KAAK,CAACD,OAAO,CAACrB,QAAQ,KAAK,OAAO,CAAC;UACnGoB,MAAM,EAAE/B,aAAa,CAAC+B,MAAM;QAC9B,CAAC,CAAC,CAAC;QACH;QACA;QACA;QACA;;QAEAE,KAAK,CAACyB,KAAK,GAAG,KAAK;QACnBzB,KAAK,CAACxB,SAAS,GAAGwB,KAAK,CAACD,OAAO,CAACvB,SAAS,CAAC,CAAC;QAC3C;QACA;QACA;;QAEAwB,KAAK,CAACC,gBAAgB,CAACyB,OAAO,CAAC,UAAUC,QAAQ,EAAE;UACjD,OAAO3B,KAAK,CAACI,aAAa,CAACuB,QAAQ,CAACC,IAAI,CAAC,GAAG1B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwB,QAAQ,CAACE,IAAI,CAAC;QAC9E,CAAC,CAAC;QAEF,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG9B,KAAK,CAACC,gBAAgB,CAACnB,MAAM,EAAEgD,KAAK,EAAE,EAAE;UAClE,IAAI9B,KAAK,CAACyB,KAAK,KAAK,IAAI,EAAE;YACxBzB,KAAK,CAACyB,KAAK,GAAG,KAAK;YACnBK,KAAK,GAAG,CAAC,CAAC;YACV;UACF;UAEA,IAAIC,qBAAqB,GAAG/B,KAAK,CAACC,gBAAgB,CAAC6B,KAAK,CAAC;YACrDE,EAAE,GAAGD,qBAAqB,CAACC,EAAE;YAC7BC,sBAAsB,GAAGF,qBAAqB,CAAChC,OAAO;YACtDmC,QAAQ,GAAGD,sBAAsB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,sBAAsB;YAC1EL,IAAI,GAAGG,qBAAqB,CAACH,IAAI;UAErC,IAAI,OAAOI,EAAE,KAAK,UAAU,EAAE;YAC5BhC,KAAK,GAAGgC,EAAE,CAAC;cACThC,KAAK,EAAEA,KAAK;cACZD,OAAO,EAAEmC,QAAQ;cACjBN,IAAI,EAAEA,IAAI;cACVlB,QAAQ,EAAEA;YACZ,CAAC,CAAC,IAAIV,KAAK;UACb;QACF;MACF,CAAC;MACD;MACA;MACAqB,MAAM,EAAElD,QAAQ,CAAC,YAAY;QAC3B,OAAO,IAAIgE,OAAO,CAAC,UAAUC,OAAO,EAAE;UACpC1B,QAAQ,CAACY,WAAW,CAAC,CAAC;UACtBc,OAAO,CAACpC,KAAK,CAAC;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFqC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1BxB,sBAAsB,CAAC,CAAC;QACxBJ,WAAW,GAAG,IAAI;MACpB;IACF,CAAC;IAED,IAAI,CAAC9B,gBAAgB,CAACkB,SAAS,EAAEC,MAAM,CAAC,EAAE;MACxC,OAAOY,QAAQ;IACjB;IAEAA,QAAQ,CAACC,UAAU,CAACZ,OAAO,CAAC,CAACuC,IAAI,CAAC,UAAUtC,KAAK,EAAE;MACjD,IAAI,CAACS,WAAW,IAAIV,OAAO,CAACwC,aAAa,EAAE;QACzCxC,OAAO,CAACwC,aAAa,CAACvC,KAAK,CAAC;MAC9B;IACF,CAAC,CAAC,CAAC,CAAC;IACJ;IACA;IACA;IACA;;IAEA,SAASoB,kBAAkBA,CAAA,EAAG;MAC5BpB,KAAK,CAACC,gBAAgB,CAACyB,OAAO,CAAC,UAAUc,IAAI,EAAE;QAC7C,IAAIZ,IAAI,GAAGY,IAAI,CAACZ,IAAI;UAChBa,YAAY,GAAGD,IAAI,CAACzC,OAAO;UAC3BA,OAAO,GAAG0C,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;UACrDC,MAAM,GAAGF,IAAI,CAACE,MAAM;QAExB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;UAChC,IAAIC,SAAS,GAAGD,MAAM,CAAC;YACrB1C,KAAK,EAAEA,KAAK;YACZ4B,IAAI,EAAEA,IAAI;YACVlB,QAAQ,EAAEA,QAAQ;YAClBX,OAAO,EAAEA;UACX,CAAC,CAAC;UAEF,IAAI6C,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG,CAAC,CAAC;UAEjCpC,gBAAgB,CAACqC,IAAI,CAACF,SAAS,IAAIC,MAAM,CAAC;QAC5C;MACF,CAAC,CAAC;IACJ;IAEA,SAAS/B,sBAAsBA,CAAA,EAAG;MAChCL,gBAAgB,CAACkB,OAAO,CAAC,UAAUM,EAAE,EAAE;QACrC,OAAOA,EAAE,CAAC,CAAC;MACb,CAAC,CAAC;MACFxB,gBAAgB,GAAG,EAAE;IACvB;IAEA,OAAOE,QAAQ;EACjB,CAAC;AACH;AACA,OAAO,IAAId,YAAY,GAAG,aAAaP,eAAe,CAAC,CAAC,CAAC,CAAC;;AAE1D,SAAShB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}