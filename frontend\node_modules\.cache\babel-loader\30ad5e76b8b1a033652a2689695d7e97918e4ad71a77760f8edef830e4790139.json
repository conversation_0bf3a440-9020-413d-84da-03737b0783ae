{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ChefHat = createLucideIcon(\"ChefHat\", [[\"path\", {\n  d: \"M6 13.87A4 4 0 0 1 7.41 6a5.11 5.11 0 0 1 1.05-1.54 5 5 0 0 1 7.08 0A5.11 5.11 0 0 1 16.59 6 4 4 0 0 1 18 13.87V21H6Z\",\n  key: \"z3ra2g\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"18\",\n  y1: \"17\",\n  y2: \"17\",\n  key: \"12q60k\"\n}]]);\nexport { ChefHat as default };", "map": {"version": 3, "names": ["ChefHat", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\chef-hat.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChefHat\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAxMy44N0E0IDQgMCAwIDEgNy40MSA2YTUuMTEgNS4xMSAwIDAgMSAxLjA1LTEuNTQgNSA1IDAgMCAxIDcuMDggMEE1LjExIDUuMTEgMCAwIDEgMTYuNTkgNiA0IDQgMCAwIDEgMTggMTMuODdWMjFINloiIC8+CiAgPGxpbmUgeDE9IjYiIHgyPSIxOCIgeTE9IjE3IiB5Mj0iMTciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chef-hat\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChefHat = createLucideIcon('ChefHat', [\n  [\n    'path',\n    {\n      d: 'M6 13.87A4 4 0 0 1 7.41 6a5.11 5.11 0 0 1 1.05-1.54 5 5 0 0 1 7.08 0A5.11 5.11 0 0 1 16.59 6 4 4 0 0 1 18 13.87V21H6Z',\n      key: 'z3ra2g',\n    },\n  ],\n  ['line', { x1: '6', x2: '18', y1: '17', y2: '17', key: '12q60k' }],\n]);\n\nexport default ChefHat;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}