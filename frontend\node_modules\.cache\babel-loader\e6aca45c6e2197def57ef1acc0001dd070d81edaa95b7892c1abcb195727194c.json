{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BatteryWarning = createLucideIcon(\"BatteryWarning\", [[\"path\", {\n  d: \"M14 7h2a2 2 0 0 1 2 2v6c0 1-1 2-2 2h-2\",\n  key: \"1if82c\"\n}], [\"path\", {\n  d: \"M6 7H4a2 2 0 0 0-2 2v6c0 1 1 2 2 2h2\",\n  key: \"2pdlyl\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"22\",\n  y1: \"11\",\n  y2: \"13\",\n  key: \"4dh1rd\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"10\",\n  y1: \"7\",\n  y2: \"13\",\n  key: \"1uzyus\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"10\",\n  y1: \"17\",\n  y2: \"17.01\",\n  key: \"1y8k4g\"\n}]]);\nexport { BatteryWarning as default };", "map": {"version": 3, "names": ["BatteryWarning", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\battery-warning.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BatteryWarning\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgN2gyYTIgMiAwIDAgMSAyIDJ2NmMwIDEtMSAyLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNNiA3SDRhMiAyIDAgMCAwLTIgMnY2YzAgMSAxIDIgMiAyaDIiIC8+CiAgPGxpbmUgeDE9IjIyIiB4Mj0iMjIiIHkxPSIxMSIgeTI9IjEzIiAvPgogIDxsaW5lIHgxPSIxMCIgeDI9IjEwIiB5MT0iNyIgeTI9IjEzIiAvPgogIDxsaW5lIHgxPSIxMCIgeDI9IjEwIiB5MT0iMTciIHkyPSIxNy4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/battery-warning\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BatteryWarning = createLucideIcon('BatteryWarning', [\n  ['path', { d: 'M14 7h2a2 2 0 0 1 2 2v6c0 1-1 2-2 2h-2', key: '1if82c' }],\n  ['path', { d: 'M6 7H4a2 2 0 0 0-2 2v6c0 1 1 2 2 2h2', key: '2pdlyl' }],\n  ['line', { x1: '22', x2: '22', y1: '11', y2: '13', key: '4dh1rd' }],\n  ['line', { x1: '10', x2: '10', y1: '7', y2: '13', key: '1uzyus' }],\n  ['line', { x1: '10', x2: '10', y1: '17', y2: '17.01', key: '1y8k4g' }],\n]);\n\nexport default BatteryWarning;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,OAAS;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}