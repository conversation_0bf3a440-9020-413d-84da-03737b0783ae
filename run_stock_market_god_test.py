#!/usr/bin/env python3
"""
Simple launcher for Stock Market God Test Suite
Handles import paths and runs the comprehensive test
"""

import sys
import os
import asyncio
from datetime import datetime

# Add all necessary paths
sys.path.append(os.path.join(os.path.dirname(__file__), '1_main_chat_engine'))
sys.path.append(os.path.join(os.path.dirname(__file__), '2_trading_logic'))
sys.path.append(os.path.join(os.path.dirname(__file__), '3_market_news_data'))
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))
sys.path.append(os.path.join(os.path.dirname(__file__), '5_tests_checks'))

# Import the test questions
from stock_market_god_test_suite import STOCK_MARKET_GOD_TEST_QUESTIONS, validate_6_point_format

async def run_simple_test():
    """Run a simplified version of the Stock Market God test"""
    print("🔮 A.T.L.A.S. Stock Market God Test Suite")
    print("=" * 50)
    print(f"Testing {len(STOCK_MARKET_GOD_TEST_QUESTIONS)} capabilities")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Try to import and initialize engines
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        
        # Initialize engine
        predicto = PredictoConversationalEngine()
        await predicto.initialize()
        
        # Switch to guru mode for Stock Market God responses
        predicto.switch_persona("guru")
        
        print("✅ A.T.L.A.S. Predicto engine initialized successfully")
        print()
        
        # Test a few key questions
        test_questions = [
            "What's Apple's price right now?",
            "Give me a trade idea for Tesla", 
            "Help me make $200 this week"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"🧪 Test {i}: {question}")
            
            try:
                # Get response from A.T.L.A.S.
                response = await predicto.process_conversation(
                    question, 
                    session_id=f"test_{i}", 
                    orchestrator=None  # Simplified test without full orchestrator
                )
                
                # Validate 6-point format
                format_validation = validate_6_point_format(response.response)
                
                print(f"📊 Format Score: {format_validation['score']}/6 ({format_validation['percentage']:.1f}%)")
                
                if format_validation['is_valid']:
                    print("✅ PASSED - Valid Stock Market God format")
                else:
                    print("❌ FAILED - Invalid format")
                    if format_validation['missing_sections']:
                        print(f"⚠️  Missing: {', '.join(format_validation['missing_sections'])}")
                
                print(f"🎯 Response Preview:")
                print(response.response[:300] + "..." if len(response.response) > 300 else response.response)
                print("-" * 50)
                
            except Exception as e:
                print(f"❌ ERROR: {e}")
                print("-" * 50)
        
        print("\n🎉 Basic test completed!")
        print("For full testing, run the complete test suite in 5_tests_checks/")
        
    except Exception as e:
        print(f"❌ Failed to initialize A.T.L.A.S.: {e}")
        print("\nTo run tests manually:")
        print("1. Start A.T.L.A.S. system")
        print("2. Use the questions from 5_tests_checks/STOCK_MARKET_GOD_CHECKLIST.md")
        print("3. Verify each response has all 6 sections")

if __name__ == "__main__":
    asyncio.run(run_simple_test())
