{"ast": null, "code": "const lightBlue = {\n  50: '#e1f5fe',\n  100: '#b3e5fc',\n  200: '#81d4fa',\n  300: '#4fc3f7',\n  400: '#29b6f6',\n  500: '#03a9f4',\n  600: '#039be5',\n  700: '#0288d1',\n  800: '#0277bd',\n  900: '#01579b',\n  A100: '#80d8ff',\n  A200: '#40c4ff',\n  A400: '#00b0ff',\n  A700: '#0091ea'\n};\nexport default lightBlue;", "map": {"version": 3, "names": ["lightBlue", "A100", "A200", "A400", "A700"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/colors/lightBlue.js"], "sourcesContent": ["const lightBlue = {\n  50: '#e1f5fe',\n  100: '#b3e5fc',\n  200: '#81d4fa',\n  300: '#4fc3f7',\n  400: '#29b6f6',\n  500: '#03a9f4',\n  600: '#039be5',\n  700: '#0288d1',\n  800: '#0277bd',\n  900: '#01579b',\n  A100: '#80d8ff',\n  A200: '#40c4ff',\n  A400: '#00b0ff',\n  A700: '#0091ea'\n};\nexport default lightBlue;"], "mappings": "AAAA,MAAMA,SAAS,GAAG;EAChB,EAAE,EAAE,SAAS;EACb,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;AACD,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}