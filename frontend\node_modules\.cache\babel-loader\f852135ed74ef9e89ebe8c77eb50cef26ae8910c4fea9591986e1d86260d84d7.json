{"ast": null, "code": "'use client';\n\nexport { default } from './InputLabel';\nexport { default as inputLabelClasses } from './inputLabelClasses';\nexport * from './inputLabelClasses';", "map": {"version": 3, "names": ["default", "inputLabelClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/InputLabel/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './InputLabel';\nexport { default as inputLabelClasses } from './inputLabelClasses';\nexport * from './inputLabelClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,qBAAqB;AAClE,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}