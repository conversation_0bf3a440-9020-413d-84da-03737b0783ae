{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst MousePointer2 = createLucideIcon(\"MousePointer2\", [[\"path\", {\n  d: \"m4 4 7.07 17 2.51-7.39L21 11.07z\",\n  key: \"1vqm48\"\n}]]);\nexport { MousePointer2 as default };", "map": {"version": 3, "names": ["MousePointer2", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\mouse-pointer-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MousePointer2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNCA0IDcuMDcgMTcgMi41MS03LjM5TDIxIDExLjA3eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mouse-pointer-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MousePointer2 = createLucideIcon('MousePointer2', [\n  ['path', { d: 'm4 4 7.07 17 2.51-7.39L21 11.07z', key: '1vqm48' }],\n]);\n\nexport default MousePointer2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}