{"ast": null, "code": "'use client';\n\nexport { default } from './Tabs';\nexport { default as tabsClasses } from './tabsClasses';\nexport * from './tabsClasses';", "map": {"version": 3, "names": ["default", "tabsClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Tabs/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Tabs';\nexport { default as tabsClasses } from './tabsClasses';\nexport * from './tabsClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,QAAQ;AAChC,SAASA,OAAO,IAAIC,WAAW,QAAQ,eAAe;AACtD,cAAc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}