{"ast": null, "code": "export { size, equalSizes } from \"./size.mjs\";\nexport { bindTo as bindCanvasElementBitmapSizeTo } from \"./canvas-element-bitmap-size.mjs\";\nexport { CanvasRenderingTarget2D, createCanvasRenderingTarget2D, tryCreateCanvasRenderingTarget2D } from \"./canvas-rendering-target.mjs\";", "map": {"version": 3, "names": ["size", "equalSizes", "bindTo", "bindCanvasElementBitmapSizeTo", "CanvasRenderingTarget2D", "createCanvasRenderingTarget2D", "tryCreateCanvasRenderingTarget2D"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/fancy-canvas/index.mjs"], "sourcesContent": ["export { size, equalSizes } from \"./size.mjs\";\nexport { bindTo as bindCanvasElementBitmapSizeTo, } from \"./canvas-element-bitmap-size.mjs\";\nexport { CanvasRenderingTarget2D, createCanvasRenderingTarget2D, tryCreateCanvasRenderingTarget2D, } from \"./canvas-rendering-target.mjs\";\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,UAAU,QAAQ,YAAY;AAC7C,SAASC,MAAM,IAAIC,6BAA6B,QAAS,kCAAkC;AAC3F,SAASC,uBAAuB,EAAEC,6BAA6B,EAAEC,gCAAgC,QAAS,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}