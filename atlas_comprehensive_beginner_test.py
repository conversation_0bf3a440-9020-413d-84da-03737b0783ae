#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive Beginner Test Suite
Tests all system capabilities using the exact beginner-style prompts provided
"""

import requests
import time
import json
from datetime import datetime
from typing import Dict, List, Any

class ATLASComprehensiveBeginnerTest:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
        
        # Exact beginner-style test prompts as provided
        self.test_prompts = [
            "What's Apple trading at right now?",
            "Are there any stocks that look like they might jump soon?",
            "Why did Tesla move up today? What's the news behind it?",
            "Where do you think Microsoft will be in five days?",
            "Can you suggest an options trade on NVIDIA that could make me money this week?",
            "I want to make $100 by tomorrow—what trade should I place?",
            "Please buy 10 shares of Amazon for me now.",
            "Alert me when Google goes up more than 2% today.",
            "How can I protect my Tesla shares if they start falling?",
            "Can you optimize my portfolio to boost returns?",
            "How risky is buying 20 shares of Shopify right now?",
            "Give me a quick morning market briefing for today.",
            "Show me any unusual options activity in Netflix.",
            "Backtest a simple breakout strategy on the S&P 500 over the last month.",
            "Which forex pair could earn me $50 today?",
            "Alert me when the VIX index rises above 20.",
            "What's the best ETF for steady growth this month?",
            "Help me make $200 this week with minimal trades.",
            "Do any cryptocurrencies look good right now? If so, buy 1 ETH."
        ]
        
    def send_request(self, message: str) -> Dict[str, Any]:
        """Send request to A.T.L.A.S. system"""
        data = {
            'message': message,
            'session_id': f'test_{int(time.time())}',
            'context': {'test_mode': True}
        }

        try:
            response = requests.post(f'{self.base_url}/api/v1/chat', json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}", "response": response.text}
        except Exception as e:
            return {"error": str(e), "response": ""}
    
    def check_server_health(self) -> bool:
        """Check if A.T.L.A.S. server is running"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def evaluate_response(self, prompt: str, response: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate response for A.T.L.A.S. Trading God capabilities"""
        response_text = response.get('response', '').lower()
        
        # Initialize scoring
        score = 0.0
        issues = []
        
        # 1. A.T.L.A.S. Branding (2 points)
        if 'a.t.l.a.s' in response_text and 'predicto' in response_text:
            score += 2.0
        elif 'a.t.l.a.s' in response_text:
            score += 1.0
            issues.append("Missing Predicto branding")
        else:
            issues.append("Missing A.T.L.A.S. branding")
        
        # 2. Trading God Persona - No AI Limitations (2 points)
        limitation_phrases = ["i can't", "i don't have", "i'm unable", "as an ai", "i cannot", "not possible"]
        if not any(phrase in response_text for phrase in limitation_phrases):
            score += 2.0
        else:
            issues.append("Contains AI limitation language")
        
        # 3. Specific Trading Data (2 points)
        import re
        has_prices = bool(re.search(r'\$\d+\.?\d*', response_text))
        has_percentages = bool(re.search(r'\d+\.?\d*%', response_text))
        has_trading_terms = any(term in response_text for term in ['buy', 'sell', 'target', 'entry', 'stop', 'shares'])
        
        if has_prices and (has_percentages or has_trading_terms):
            score += 2.0
        elif has_prices or has_trading_terms:
            score += 1.0
            issues.append("Limited specific trading data")
        else:
            issues.append("No specific trading data")
        
        # 4. Confident Trading Recommendations (2 points)
        confidence_indicators = ['confidence:', 'target:', 'entry:', 'stop:', 'buy', 'sell', 'execute', 'trade']
        if sum(1 for indicator in confidence_indicators if indicator in response_text) >= 3:
            score += 2.0
        elif sum(1 for indicator in confidence_indicators if indicator in response_text) >= 1:
            score += 1.0
            issues.append("Partial trading recommendations")
        else:
            issues.append("No trading recommendations")
        
        # 5. Response Quality (2 points)
        response_length = len(response.get('response', ''))
        if response_length > 200:
            score += 2.0
        elif response_length > 100:
            score += 1.0
            issues.append("Short response")
        else:
            issues.append("Very short response")
        
        return {
            'prompt': prompt,
            'response': response.get('response', ''),
            'score': score,
            'max_score': 10.0,
            'passed': score >= 7.0,  # 70% threshold
            'issues': issues,
            'response_length': response_length,
            'error': response.get('error')
        }
    
    def run_all_tests(self):
        """Run all beginner-style tests"""
        print("🚀 Starting A.T.L.A.S. Comprehensive Beginner Test Suite")
        print("=" * 70)
        print(f"📊 Total Tests: {len(self.test_prompts)}")
        
        # Check server health
        if not self.check_server_health():
            print("❌ A.T.L.A.S. server is not available!")
            print("   Make sure the server is running on", self.base_url)
            return
        
        print("✅ A.T.L.A.S. server is healthy")
        print("\n🧪 Running Tests...")
        
        start_time = time.time()
        
        for i, prompt in enumerate(self.test_prompts, 1):
            print(f"\n[{i:2d}/{len(self.test_prompts)}] Testing: \"{prompt[:60]}{'...' if len(prompt) > 60 else ''}\"")
            
            # Send request
            response = self.send_request(prompt)
            
            # Evaluate response
            evaluation = self.evaluate_response(prompt, response)
            self.test_results.append(evaluation)
            
            if evaluation['passed']:
                self.passed_tests += 1
                print(f"      ✅ PASSED - Score: {evaluation['score']:.1f}/10")
            else:
                self.failed_tests += 1
                print(f"      ❌ FAILED - Score: {evaluation['score']:.1f}/10")
                if evaluation['issues']:
                    print(f"         Issues: {', '.join(evaluation['issues'])}")
            
            # Brief pause between tests
            time.sleep(0.5)
        
        duration = time.time() - start_time
        
        # Generate and display results
        self.display_results(duration)
        self.save_detailed_report()
    
    def display_results(self, duration: float):
        """Display comprehensive test results"""
        total_tests = len(self.test_results)
        pass_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        avg_score = sum(r['score'] for r in self.test_results) / total_tests if total_tests > 0 else 0
        
        print("\n" + "=" * 70)
        print("🎯 A.T.L.A.S. COMPREHENSIVE TEST RESULTS")
        print("=" * 70)
        
        print(f"\n📊 OVERALL PERFORMANCE:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {self.passed_tests} ✅")
        print(f"   Failed: {self.failed_tests} ❌")
        print(f"   Pass Rate: {pass_rate:.1f}%")
        print(f"   Average Score: {avg_score:.1f}/10")
        print(f"   Duration: {duration:.1f} seconds")
        
        # Show failed tests
        failed_tests = [r for r in self.test_results if not r['passed']]
        if failed_tests:
            print(f"\n🚨 FAILED TESTS ({len(failed_tests)}):")
            for i, test in enumerate(failed_tests, 1):
                print(f"   {i}. \"{test['prompt'][:50]}{'...' if len(test['prompt']) > 50 else ''}\"")
                print(f"      Score: {test['score']:.1f}/10")
                print(f"      Issues: {', '.join(test['issues'])}")
        else:
            print("\n🎉 ALL TESTS PASSED!")
        
        # Overall assessment
        if pass_rate >= 90:
            print(f"\n🏆 EXCELLENT: A.T.L.A.S. is performing at institutional level!")
        elif pass_rate >= 80:
            print(f"\n✅ GOOD: A.T.L.A.S. meets Trading God standards!")
        elif pass_rate >= 70:
            print(f"\n⚠️  ACCEPTABLE: A.T.L.A.S. needs minor improvements")
        else:
            print(f"\n❌ NEEDS WORK: A.T.L.A.S. requires significant improvements")
        
        print("=" * 70)
    
    def save_detailed_report(self):
        """Save detailed test report to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"atlas_beginner_test_report_{timestamp}.json"
        
        report = {
            "test_summary": {
                "total_tests": len(self.test_results),
                "passed_tests": self.passed_tests,
                "failed_tests": self.failed_tests,
                "pass_rate": (self.passed_tests / len(self.test_results) * 100) if self.test_results else 0,
                "average_score": sum(r['score'] for r in self.test_results) / len(self.test_results) if self.test_results else 0,
                "timestamp": datetime.now().isoformat()
            },
            "detailed_results": self.test_results
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {filename}")

def main():
    """Main execution function"""
    import sys
    
    # Allow custom server URL
    server_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    
    print(f"🔗 Testing A.T.L.A.S. server at: {server_url}")
    
    # Create and run test suite
    test_suite = ATLASComprehensiveBeginnerTest(server_url)
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
