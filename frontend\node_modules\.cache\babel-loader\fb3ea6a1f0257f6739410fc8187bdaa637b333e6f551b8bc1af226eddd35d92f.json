{"ast": null, "code": "import React from'react';import{Card,CardContent,Typography}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Dashboard=_ref=>{let{accountData,positions,signals}=_ref;return/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Dashboard\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Account Value: $\",(accountData===null||accountData===void 0?void 0:accountData.portfolio_value)||'0.00']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Positions: \",(positions===null||positions===void 0?void 0:positions.length)||0]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Signals: \",(signals===null||signals===void 0?void 0:signals.length)||0]})]})});};export default Dashboard;", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "jsx", "_jsx", "jsxs", "_jsxs", "Dashboard", "_ref", "accountData", "positions", "signals", "children", "variant", "portfolio_value", "length"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography } from '@mui/material';\n\nconst Dashboard = ({ accountData, positions, signals }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\">Dashboard</Typography>\n        <Typography variant=\"body2\">\n          Account Value: ${accountData?.portfolio_value || '0.00'}\n        </Typography>\n        <Typography variant=\"body2\">\n          Positions: {positions?.length || 0}\n        </Typography>\n        <Typography variant=\"body2\">\n          Signals: {signals?.length || 0}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,CAAEC,UAAU,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9D,KAAM,CAAAC,SAAS,CAAGC,IAAA,EAAyC,IAAxC,CAAEC,WAAW,CAAEC,SAAS,CAAEC,OAAQ,CAAC,CAAAH,IAAA,CACpD,mBACEJ,IAAA,CAACJ,IAAI,EAAAY,QAAA,cACHN,KAAA,CAACL,WAAW,EAAAW,QAAA,eACVR,IAAA,CAACF,UAAU,EAACW,OAAO,CAAC,IAAI,CAAAD,QAAA,CAAC,WAAS,CAAY,CAAC,cAC/CN,KAAA,CAACJ,UAAU,EAACW,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,kBACV,CAAC,CAAAH,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEK,eAAe,GAAI,MAAM,EAC7C,CAAC,cACbR,KAAA,CAACJ,UAAU,EAACW,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,aACf,CAAC,CAAAF,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEK,MAAM,GAAI,CAAC,EACxB,CAAC,cACbT,KAAA,CAACJ,UAAU,EAACW,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,WACjB,CAAC,CAAAD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEI,MAAM,GAAI,CAAC,EACpB,CAAC,EACF,CAAC,CACV,CAAC,CAEX,CAAC,CAED,cAAe,CAAAR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}