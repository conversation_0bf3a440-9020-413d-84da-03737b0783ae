#!/usr/bin/env python3
"""
A.T.L.A.S. Server Startup Script
"""

import sys
import os

# Add all necessary paths
sys.path.append('4_helper_tools')
sys.path.append('1_main_chat_engine')
sys.path.append('2_trading_logic')
sys.path.append('3_market_news_data')

print("Starting A.T.L.A.S. Stock Market God Server...")
print("=" * 50)

try:
    # Import uvicorn and the server
    import uvicorn
    print("SUCCESS: Starting A.T.L.A.S. with uvicorn")
    print("Server will be available at: http://localhost:8080")

    # Start the server using uvicorn
    uvicorn.run(
        "1_main_chat_engine.atlas_server:app",
        host="0.0.0.0",
        port=8080,
        reload=False,
        log_level="info"
    )

except Exception as e:
    print(f"ERROR: Server startup failed: {e}")
    import traceback
    traceback.print_exc()
