{"ast": null, "code": "import { unstable_ownerWindow as ownerWindow, unstable_ownerDocument as ownerDocument, unstable_getScrollbarSize as getScrollbarSize } from '@mui/utils';\n// Is a vertical scrollbar displayed?\nfunction isOverflowing(container) {\n  const doc = ownerDocument(container);\n  if (doc.body === container) {\n    return ownerWindow(container).innerWidth > doc.documentElement.clientWidth;\n  }\n  return container.scrollHeight > container.clientHeight;\n}\nexport function ariaHidden(element, show) {\n  if (show) {\n    element.setAttribute('aria-hidden', 'true');\n  } else {\n    element.removeAttribute('aria-hidden');\n  }\n}\nfunction getPaddingRight(element) {\n  return parseInt(ownerWindow(element).getComputedStyle(element).paddingRight, 10) || 0;\n}\nfunction isAriaHiddenForbiddenOnElement(element) {\n  // The forbidden HTML tags are the ones from ARIA specification that\n  // can be children of body and can't have aria-hidden attribute.\n  // cf. https://www.w3.org/TR/html-aria/#docconformance\n  const forbiddenTagNames = ['TEMPLATE', 'SCRIPT', 'STYLE', 'LINK', 'MAP', 'META', 'NOSCRIPT', 'PICTURE', 'COL', 'COLGROUP', 'PARAM', 'SLOT', 'SOURCE', 'TRACK'];\n  const isForbiddenTagName = forbiddenTagNames.indexOf(element.tagName) !== -1;\n  const isInputHidden = element.tagName === 'INPUT' && element.getAttribute('type') === 'hidden';\n  return isForbiddenTagName || isInputHidden;\n}\nfunction ariaHiddenSiblings(container, mountElement, currentElement, elementsToExclude, show) {\n  const blacklist = [mountElement, currentElement, ...elementsToExclude];\n  [].forEach.call(container.children, element => {\n    const isNotExcludedElement = blacklist.indexOf(element) === -1;\n    const isNotForbiddenElement = !isAriaHiddenForbiddenOnElement(element);\n    if (isNotExcludedElement && isNotForbiddenElement) {\n      ariaHidden(element, show);\n    }\n  });\n}\nfunction findIndexOf(items, callback) {\n  let idx = -1;\n  items.some((item, index) => {\n    if (callback(item)) {\n      idx = index;\n      return true;\n    }\n    return false;\n  });\n  return idx;\n}\nfunction handleContainer(containerInfo, props) {\n  const restoreStyle = [];\n  const container = containerInfo.container;\n  if (!props.disableScrollLock) {\n    if (isOverflowing(container)) {\n      // Compute the size before applying overflow hidden to avoid any scroll jumps.\n      const scrollbarSize = getScrollbarSize(ownerDocument(container));\n      restoreStyle.push({\n        value: container.style.paddingRight,\n        property: 'padding-right',\n        el: container\n      });\n      // Use computed style, here to get the real padding to add our scrollbar width.\n      container.style.paddingRight = `${getPaddingRight(container) + scrollbarSize}px`;\n\n      // .mui-fixed is a global helper.\n      const fixedElements = ownerDocument(container).querySelectorAll('.mui-fixed');\n      [].forEach.call(fixedElements, element => {\n        restoreStyle.push({\n          value: element.style.paddingRight,\n          property: 'padding-right',\n          el: element\n        });\n        element.style.paddingRight = `${getPaddingRight(element) + scrollbarSize}px`;\n      });\n    }\n    let scrollContainer;\n    if (container.parentNode instanceof DocumentFragment) {\n      scrollContainer = ownerDocument(container).body;\n    } else {\n      // Support html overflow-y: auto for scroll stability between pages\n      // https://css-tricks.com/snippets/css/force-vertical-scrollbar/\n      const parent = container.parentElement;\n      const containerWindow = ownerWindow(container);\n      scrollContainer = (parent == null ? void 0 : parent.nodeName) === 'HTML' && containerWindow.getComputedStyle(parent).overflowY === 'scroll' ? parent : container;\n    }\n\n    // Block the scroll even if no scrollbar is visible to account for mobile keyboard\n    // screensize shrink.\n    restoreStyle.push({\n      value: scrollContainer.style.overflow,\n      property: 'overflow',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowX,\n      property: 'overflow-x',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowY,\n      property: 'overflow-y',\n      el: scrollContainer\n    });\n    scrollContainer.style.overflow = 'hidden';\n  }\n  const restore = () => {\n    restoreStyle.forEach(({\n      value,\n      el,\n      property\n    }) => {\n      if (value) {\n        el.style.setProperty(property, value);\n      } else {\n        el.style.removeProperty(property);\n      }\n    });\n  };\n  return restore;\n}\nfunction getHiddenSiblings(container) {\n  const hiddenSiblings = [];\n  [].forEach.call(container.children, element => {\n    if (element.getAttribute('aria-hidden') === 'true') {\n      hiddenSiblings.push(element);\n    }\n  });\n  return hiddenSiblings;\n}\n/**\n * @ignore - do not document.\n *\n * Proper state management for containers and the modals in those containers.\n * Simplified, but inspired by react-overlay's ModalManager class.\n * Used by the Modal to ensure proper styling of containers.\n */\nexport class ModalManager {\n  constructor() {\n    this.containers = void 0;\n    this.modals = void 0;\n    this.modals = [];\n    this.containers = [];\n  }\n  add(modal, container) {\n    let modalIndex = this.modals.indexOf(modal);\n    if (modalIndex !== -1) {\n      return modalIndex;\n    }\n    modalIndex = this.modals.length;\n    this.modals.push(modal);\n\n    // If the modal we are adding is already in the DOM.\n    if (modal.modalRef) {\n      ariaHidden(modal.modalRef, false);\n    }\n    const hiddenSiblings = getHiddenSiblings(container);\n    ariaHiddenSiblings(container, modal.mount, modal.modalRef, hiddenSiblings, true);\n    const containerIndex = findIndexOf(this.containers, item => item.container === container);\n    if (containerIndex !== -1) {\n      this.containers[containerIndex].modals.push(modal);\n      return modalIndex;\n    }\n    this.containers.push({\n      modals: [modal],\n      container,\n      restore: null,\n      hiddenSiblings\n    });\n    return modalIndex;\n  }\n  mount(modal, props) {\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    if (!containerInfo.restore) {\n      containerInfo.restore = handleContainer(containerInfo, props);\n    }\n  }\n  remove(modal, ariaHiddenState = true) {\n    const modalIndex = this.modals.indexOf(modal);\n    if (modalIndex === -1) {\n      return modalIndex;\n    }\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    containerInfo.modals.splice(containerInfo.modals.indexOf(modal), 1);\n    this.modals.splice(modalIndex, 1);\n\n    // If that was the last modal in a container, clean up the container.\n    if (containerInfo.modals.length === 0) {\n      // The modal might be closed before it had the chance to be mounted in the DOM.\n      if (containerInfo.restore) {\n        containerInfo.restore();\n      }\n      if (modal.modalRef) {\n        // In case the modal wasn't in the DOM yet.\n        ariaHidden(modal.modalRef, ariaHiddenState);\n      }\n      ariaHiddenSiblings(containerInfo.container, modal.mount, modal.modalRef, containerInfo.hiddenSiblings, false);\n      this.containers.splice(containerIndex, 1);\n    } else {\n      // Otherwise make sure the next top modal is visible to a screen reader.\n      const nextTop = containerInfo.modals[containerInfo.modals.length - 1];\n      // as soon as a modal is adding its modalRef is undefined. it can't set\n      // aria-hidden because the dom element doesn't exist either\n      // when modal was unmounted before modalRef gets null\n      if (nextTop.modalRef) {\n        ariaHidden(nextTop.modalRef, false);\n      }\n    }\n    return modalIndex;\n  }\n  isTopModal(modal) {\n    return this.modals.length > 0 && this.modals[this.modals.length - 1] === modal;\n  }\n}", "map": {"version": 3, "names": ["unstable_ownerW<PERSON>ow", "ownerWindow", "unstable_ownerDocument", "ownerDocument", "unstable_getScrollbarSize", "getScrollbarSize", "isOverflowing", "container", "doc", "body", "innerWidth", "documentElement", "clientWidth", "scrollHeight", "clientHeight", "ariaHidden", "element", "show", "setAttribute", "removeAttribute", "getPaddingRight", "parseInt", "getComputedStyle", "paddingRight", "isAriaHiddenForbiddenOnElement", "forbiddenTagNames", "isForbiddenTagName", "indexOf", "tagName", "isInputHidden", "getAttribute", "ariaHiddenSiblings", "mount<PERSON><PERSON>", "currentElement", "elementsToExclude", "blacklist", "for<PERSON>ach", "call", "children", "isNotExcludedElement", "isNotForbiddenElement", "findIndexOf", "items", "callback", "idx", "some", "item", "index", "handleContainer", "containerInfo", "props", "restoreStyle", "disableScrollLock", "scrollbarSize", "push", "value", "style", "property", "el", "fixedElements", "querySelectorAll", "scrollContainer", "parentNode", "DocumentFragment", "parent", "parentElement", "containerWindow", "nodeName", "overflowY", "overflow", "overflowX", "restore", "setProperty", "removeProperty", "getHiddenSiblings", "hiddenSiblings", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "containers", "modals", "add", "modal", "modalIndex", "length", "modalRef", "mount", "containerIndex", "remove", "ariaHiddenState", "splice", "nextTop", "isTopModal"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Modal/ModalManager.js"], "sourcesContent": ["import { unstable_ownerWindow as ownerWindow, unstable_ownerDocument as ownerDocument, unstable_getScrollbarSize as getScrollbarSize } from '@mui/utils';\n// Is a vertical scrollbar displayed?\nfunction isOverflowing(container) {\n  const doc = ownerDocument(container);\n  if (doc.body === container) {\n    return ownerWindow(container).innerWidth > doc.documentElement.clientWidth;\n  }\n  return container.scrollHeight > container.clientHeight;\n}\nexport function ariaHidden(element, show) {\n  if (show) {\n    element.setAttribute('aria-hidden', 'true');\n  } else {\n    element.removeAttribute('aria-hidden');\n  }\n}\nfunction getPaddingRight(element) {\n  return parseInt(ownerWindow(element).getComputedStyle(element).paddingRight, 10) || 0;\n}\nfunction isAriaHiddenForbiddenOnElement(element) {\n  // The forbidden HTML tags are the ones from ARIA specification that\n  // can be children of body and can't have aria-hidden attribute.\n  // cf. https://www.w3.org/TR/html-aria/#docconformance\n  const forbiddenTagNames = ['TEMPLATE', 'SCRIPT', 'STYLE', 'LINK', 'MAP', 'META', 'NOSCRIPT', 'PICTURE', 'COL', 'COLGROUP', 'PARAM', 'SLOT', 'SOURCE', 'TRACK'];\n  const isForbiddenTagName = forbiddenTagNames.indexOf(element.tagName) !== -1;\n  const isInputHidden = element.tagName === 'INPUT' && element.getAttribute('type') === 'hidden';\n  return isForbiddenTagName || isInputHidden;\n}\nfunction ariaHiddenSiblings(container, mountElement, currentElement, elementsToExclude, show) {\n  const blacklist = [mountElement, currentElement, ...elementsToExclude];\n  [].forEach.call(container.children, element => {\n    const isNotExcludedElement = blacklist.indexOf(element) === -1;\n    const isNotForbiddenElement = !isAriaHiddenForbiddenOnElement(element);\n    if (isNotExcludedElement && isNotForbiddenElement) {\n      ariaHidden(element, show);\n    }\n  });\n}\nfunction findIndexOf(items, callback) {\n  let idx = -1;\n  items.some((item, index) => {\n    if (callback(item)) {\n      idx = index;\n      return true;\n    }\n    return false;\n  });\n  return idx;\n}\nfunction handleContainer(containerInfo, props) {\n  const restoreStyle = [];\n  const container = containerInfo.container;\n  if (!props.disableScrollLock) {\n    if (isOverflowing(container)) {\n      // Compute the size before applying overflow hidden to avoid any scroll jumps.\n      const scrollbarSize = getScrollbarSize(ownerDocument(container));\n      restoreStyle.push({\n        value: container.style.paddingRight,\n        property: 'padding-right',\n        el: container\n      });\n      // Use computed style, here to get the real padding to add our scrollbar width.\n      container.style.paddingRight = `${getPaddingRight(container) + scrollbarSize}px`;\n\n      // .mui-fixed is a global helper.\n      const fixedElements = ownerDocument(container).querySelectorAll('.mui-fixed');\n      [].forEach.call(fixedElements, element => {\n        restoreStyle.push({\n          value: element.style.paddingRight,\n          property: 'padding-right',\n          el: element\n        });\n        element.style.paddingRight = `${getPaddingRight(element) + scrollbarSize}px`;\n      });\n    }\n    let scrollContainer;\n    if (container.parentNode instanceof DocumentFragment) {\n      scrollContainer = ownerDocument(container).body;\n    } else {\n      // Support html overflow-y: auto for scroll stability between pages\n      // https://css-tricks.com/snippets/css/force-vertical-scrollbar/\n      const parent = container.parentElement;\n      const containerWindow = ownerWindow(container);\n      scrollContainer = (parent == null ? void 0 : parent.nodeName) === 'HTML' && containerWindow.getComputedStyle(parent).overflowY === 'scroll' ? parent : container;\n    }\n\n    // Block the scroll even if no scrollbar is visible to account for mobile keyboard\n    // screensize shrink.\n    restoreStyle.push({\n      value: scrollContainer.style.overflow,\n      property: 'overflow',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowX,\n      property: 'overflow-x',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowY,\n      property: 'overflow-y',\n      el: scrollContainer\n    });\n    scrollContainer.style.overflow = 'hidden';\n  }\n  const restore = () => {\n    restoreStyle.forEach(({\n      value,\n      el,\n      property\n    }) => {\n      if (value) {\n        el.style.setProperty(property, value);\n      } else {\n        el.style.removeProperty(property);\n      }\n    });\n  };\n  return restore;\n}\nfunction getHiddenSiblings(container) {\n  const hiddenSiblings = [];\n  [].forEach.call(container.children, element => {\n    if (element.getAttribute('aria-hidden') === 'true') {\n      hiddenSiblings.push(element);\n    }\n  });\n  return hiddenSiblings;\n}\n/**\n * @ignore - do not document.\n *\n * Proper state management for containers and the modals in those containers.\n * Simplified, but inspired by react-overlay's ModalManager class.\n * Used by the Modal to ensure proper styling of containers.\n */\nexport class ModalManager {\n  constructor() {\n    this.containers = void 0;\n    this.modals = void 0;\n    this.modals = [];\n    this.containers = [];\n  }\n  add(modal, container) {\n    let modalIndex = this.modals.indexOf(modal);\n    if (modalIndex !== -1) {\n      return modalIndex;\n    }\n    modalIndex = this.modals.length;\n    this.modals.push(modal);\n\n    // If the modal we are adding is already in the DOM.\n    if (modal.modalRef) {\n      ariaHidden(modal.modalRef, false);\n    }\n    const hiddenSiblings = getHiddenSiblings(container);\n    ariaHiddenSiblings(container, modal.mount, modal.modalRef, hiddenSiblings, true);\n    const containerIndex = findIndexOf(this.containers, item => item.container === container);\n    if (containerIndex !== -1) {\n      this.containers[containerIndex].modals.push(modal);\n      return modalIndex;\n    }\n    this.containers.push({\n      modals: [modal],\n      container,\n      restore: null,\n      hiddenSiblings\n    });\n    return modalIndex;\n  }\n  mount(modal, props) {\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    if (!containerInfo.restore) {\n      containerInfo.restore = handleContainer(containerInfo, props);\n    }\n  }\n  remove(modal, ariaHiddenState = true) {\n    const modalIndex = this.modals.indexOf(modal);\n    if (modalIndex === -1) {\n      return modalIndex;\n    }\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    containerInfo.modals.splice(containerInfo.modals.indexOf(modal), 1);\n    this.modals.splice(modalIndex, 1);\n\n    // If that was the last modal in a container, clean up the container.\n    if (containerInfo.modals.length === 0) {\n      // The modal might be closed before it had the chance to be mounted in the DOM.\n      if (containerInfo.restore) {\n        containerInfo.restore();\n      }\n      if (modal.modalRef) {\n        // In case the modal wasn't in the DOM yet.\n        ariaHidden(modal.modalRef, ariaHiddenState);\n      }\n      ariaHiddenSiblings(containerInfo.container, modal.mount, modal.modalRef, containerInfo.hiddenSiblings, false);\n      this.containers.splice(containerIndex, 1);\n    } else {\n      // Otherwise make sure the next top modal is visible to a screen reader.\n      const nextTop = containerInfo.modals[containerInfo.modals.length - 1];\n      // as soon as a modal is adding its modalRef is undefined. it can't set\n      // aria-hidden because the dom element doesn't exist either\n      // when modal was unmounted before modalRef gets null\n      if (nextTop.modalRef) {\n        ariaHidden(nextTop.modalRef, false);\n      }\n    }\n    return modalIndex;\n  }\n  isTopModal(modal) {\n    return this.modals.length > 0 && this.modals[this.modals.length - 1] === modal;\n  }\n}"], "mappings": "AAAA,SAASA,oBAAoB,IAAIC,WAAW,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AACxJ;AACA,SAASC,aAAaA,CAACC,SAAS,EAAE;EAChC,MAAMC,GAAG,GAAGL,aAAa,CAACI,SAAS,CAAC;EACpC,IAAIC,GAAG,CAACC,IAAI,KAAKF,SAAS,EAAE;IAC1B,OAAON,WAAW,CAACM,SAAS,CAAC,CAACG,UAAU,GAAGF,GAAG,CAACG,eAAe,CAACC,WAAW;EAC5E;EACA,OAAOL,SAAS,CAACM,YAAY,GAAGN,SAAS,CAACO,YAAY;AACxD;AACA,OAAO,SAASC,UAAUA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACxC,IAAIA,IAAI,EAAE;IACRD,OAAO,CAACE,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAC7C,CAAC,MAAM;IACLF,OAAO,CAACG,eAAe,CAAC,aAAa,CAAC;EACxC;AACF;AACA,SAASC,eAAeA,CAACJ,OAAO,EAAE;EAChC,OAAOK,QAAQ,CAACpB,WAAW,CAACe,OAAO,CAAC,CAACM,gBAAgB,CAACN,OAAO,CAAC,CAACO,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;AACvF;AACA,SAASC,8BAA8BA,CAACR,OAAO,EAAE;EAC/C;EACA;EACA;EACA,MAAMS,iBAAiB,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;EAC9J,MAAMC,kBAAkB,GAAGD,iBAAiB,CAACE,OAAO,CAACX,OAAO,CAACY,OAAO,CAAC,KAAK,CAAC,CAAC;EAC5E,MAAMC,aAAa,GAAGb,OAAO,CAACY,OAAO,KAAK,OAAO,IAAIZ,OAAO,CAACc,YAAY,CAAC,MAAM,CAAC,KAAK,QAAQ;EAC9F,OAAOJ,kBAAkB,IAAIG,aAAa;AAC5C;AACA,SAASE,kBAAkBA,CAACxB,SAAS,EAAEyB,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,EAAEjB,IAAI,EAAE;EAC5F,MAAMkB,SAAS,GAAG,CAACH,YAAY,EAAEC,cAAc,EAAE,GAAGC,iBAAiB,CAAC;EACtE,EAAE,CAACE,OAAO,CAACC,IAAI,CAAC9B,SAAS,CAAC+B,QAAQ,EAAEtB,OAAO,IAAI;IAC7C,MAAMuB,oBAAoB,GAAGJ,SAAS,CAACR,OAAO,CAACX,OAAO,CAAC,KAAK,CAAC,CAAC;IAC9D,MAAMwB,qBAAqB,GAAG,CAAChB,8BAA8B,CAACR,OAAO,CAAC;IACtE,IAAIuB,oBAAoB,IAAIC,qBAAqB,EAAE;MACjDzB,UAAU,CAACC,OAAO,EAAEC,IAAI,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ;AACA,SAASwB,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACpC,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZF,KAAK,CAACG,IAAI,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC1B,IAAIJ,QAAQ,CAACG,IAAI,CAAC,EAAE;MAClBF,GAAG,GAAGG,KAAK;MACX,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC;EACF,OAAOH,GAAG;AACZ;AACA,SAASI,eAAeA,CAACC,aAAa,EAAEC,KAAK,EAAE;EAC7C,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAM5C,SAAS,GAAG0C,aAAa,CAAC1C,SAAS;EACzC,IAAI,CAAC2C,KAAK,CAACE,iBAAiB,EAAE;IAC5B,IAAI9C,aAAa,CAACC,SAAS,CAAC,EAAE;MAC5B;MACA,MAAM8C,aAAa,GAAGhD,gBAAgB,CAACF,aAAa,CAACI,SAAS,CAAC,CAAC;MAChE4C,YAAY,CAACG,IAAI,CAAC;QAChBC,KAAK,EAAEhD,SAAS,CAACiD,KAAK,CAACjC,YAAY;QACnCkC,QAAQ,EAAE,eAAe;QACzBC,EAAE,EAAEnD;MACN,CAAC,CAAC;MACF;MACAA,SAAS,CAACiD,KAAK,CAACjC,YAAY,GAAG,GAAGH,eAAe,CAACb,SAAS,CAAC,GAAG8C,aAAa,IAAI;;MAEhF;MACA,MAAMM,aAAa,GAAGxD,aAAa,CAACI,SAAS,CAAC,CAACqD,gBAAgB,CAAC,YAAY,CAAC;MAC7E,EAAE,CAACxB,OAAO,CAACC,IAAI,CAACsB,aAAa,EAAE3C,OAAO,IAAI;QACxCmC,YAAY,CAACG,IAAI,CAAC;UAChBC,KAAK,EAAEvC,OAAO,CAACwC,KAAK,CAACjC,YAAY;UACjCkC,QAAQ,EAAE,eAAe;UACzBC,EAAE,EAAE1C;QACN,CAAC,CAAC;QACFA,OAAO,CAACwC,KAAK,CAACjC,YAAY,GAAG,GAAGH,eAAe,CAACJ,OAAO,CAAC,GAAGqC,aAAa,IAAI;MAC9E,CAAC,CAAC;IACJ;IACA,IAAIQ,eAAe;IACnB,IAAItD,SAAS,CAACuD,UAAU,YAAYC,gBAAgB,EAAE;MACpDF,eAAe,GAAG1D,aAAa,CAACI,SAAS,CAAC,CAACE,IAAI;IACjD,CAAC,MAAM;MACL;MACA;MACA,MAAMuD,MAAM,GAAGzD,SAAS,CAAC0D,aAAa;MACtC,MAAMC,eAAe,GAAGjE,WAAW,CAACM,SAAS,CAAC;MAC9CsD,eAAe,GAAG,CAACG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,QAAQ,MAAM,MAAM,IAAID,eAAe,CAAC5C,gBAAgB,CAAC0C,MAAM,CAAC,CAACI,SAAS,KAAK,QAAQ,GAAGJ,MAAM,GAAGzD,SAAS;IAClK;;IAEA;IACA;IACA4C,YAAY,CAACG,IAAI,CAAC;MAChBC,KAAK,EAAEM,eAAe,CAACL,KAAK,CAACa,QAAQ;MACrCZ,QAAQ,EAAE,UAAU;MACpBC,EAAE,EAAEG;IACN,CAAC,EAAE;MACDN,KAAK,EAAEM,eAAe,CAACL,KAAK,CAACc,SAAS;MACtCb,QAAQ,EAAE,YAAY;MACtBC,EAAE,EAAEG;IACN,CAAC,EAAE;MACDN,KAAK,EAAEM,eAAe,CAACL,KAAK,CAACY,SAAS;MACtCX,QAAQ,EAAE,YAAY;MACtBC,EAAE,EAAEG;IACN,CAAC,CAAC;IACFA,eAAe,CAACL,KAAK,CAACa,QAAQ,GAAG,QAAQ;EAC3C;EACA,MAAME,OAAO,GAAGA,CAAA,KAAM;IACpBpB,YAAY,CAACf,OAAO,CAAC,CAAC;MACpBmB,KAAK;MACLG,EAAE;MACFD;IACF,CAAC,KAAK;MACJ,IAAIF,KAAK,EAAE;QACTG,EAAE,CAACF,KAAK,CAACgB,WAAW,CAACf,QAAQ,EAAEF,KAAK,CAAC;MACvC,CAAC,MAAM;QACLG,EAAE,CAACF,KAAK,CAACiB,cAAc,CAAChB,QAAQ,CAAC;MACnC;IACF,CAAC,CAAC;EACJ,CAAC;EACD,OAAOc,OAAO;AAChB;AACA,SAASG,iBAAiBA,CAACnE,SAAS,EAAE;EACpC,MAAMoE,cAAc,GAAG,EAAE;EACzB,EAAE,CAACvC,OAAO,CAACC,IAAI,CAAC9B,SAAS,CAAC+B,QAAQ,EAAEtB,OAAO,IAAI;IAC7C,IAAIA,OAAO,CAACc,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;MAClD6C,cAAc,CAACrB,IAAI,CAACtC,OAAO,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAO2D,cAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,KAAK,CAAC;IACxB,IAAI,CAACC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAI,CAACD,UAAU,GAAG,EAAE;EACtB;EACAE,GAAGA,CAACC,KAAK,EAAE1E,SAAS,EAAE;IACpB,IAAI2E,UAAU,GAAG,IAAI,CAACH,MAAM,CAACpD,OAAO,CAACsD,KAAK,CAAC;IAC3C,IAAIC,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,OAAOA,UAAU;IACnB;IACAA,UAAU,GAAG,IAAI,CAACH,MAAM,CAACI,MAAM;IAC/B,IAAI,CAACJ,MAAM,CAACzB,IAAI,CAAC2B,KAAK,CAAC;;IAEvB;IACA,IAAIA,KAAK,CAACG,QAAQ,EAAE;MAClBrE,UAAU,CAACkE,KAAK,CAACG,QAAQ,EAAE,KAAK,CAAC;IACnC;IACA,MAAMT,cAAc,GAAGD,iBAAiB,CAACnE,SAAS,CAAC;IACnDwB,kBAAkB,CAACxB,SAAS,EAAE0E,KAAK,CAACI,KAAK,EAAEJ,KAAK,CAACG,QAAQ,EAAET,cAAc,EAAE,IAAI,CAAC;IAChF,MAAMW,cAAc,GAAG7C,WAAW,CAAC,IAAI,CAACqC,UAAU,EAAEhC,IAAI,IAAIA,IAAI,CAACvC,SAAS,KAAKA,SAAS,CAAC;IACzF,IAAI+E,cAAc,KAAK,CAAC,CAAC,EAAE;MACzB,IAAI,CAACR,UAAU,CAACQ,cAAc,CAAC,CAACP,MAAM,CAACzB,IAAI,CAAC2B,KAAK,CAAC;MAClD,OAAOC,UAAU;IACnB;IACA,IAAI,CAACJ,UAAU,CAACxB,IAAI,CAAC;MACnByB,MAAM,EAAE,CAACE,KAAK,CAAC;MACf1E,SAAS;MACTgE,OAAO,EAAE,IAAI;MACbI;IACF,CAAC,CAAC;IACF,OAAOO,UAAU;EACnB;EACAG,KAAKA,CAACJ,KAAK,EAAE/B,KAAK,EAAE;IAClB,MAAMoC,cAAc,GAAG7C,WAAW,CAAC,IAAI,CAACqC,UAAU,EAAEhC,IAAI,IAAIA,IAAI,CAACiC,MAAM,CAACpD,OAAO,CAACsD,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9F,MAAMhC,aAAa,GAAG,IAAI,CAAC6B,UAAU,CAACQ,cAAc,CAAC;IACrD,IAAI,CAACrC,aAAa,CAACsB,OAAO,EAAE;MAC1BtB,aAAa,CAACsB,OAAO,GAAGvB,eAAe,CAACC,aAAa,EAAEC,KAAK,CAAC;IAC/D;EACF;EACAqC,MAAMA,CAACN,KAAK,EAAEO,eAAe,GAAG,IAAI,EAAE;IACpC,MAAMN,UAAU,GAAG,IAAI,CAACH,MAAM,CAACpD,OAAO,CAACsD,KAAK,CAAC;IAC7C,IAAIC,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,OAAOA,UAAU;IACnB;IACA,MAAMI,cAAc,GAAG7C,WAAW,CAAC,IAAI,CAACqC,UAAU,EAAEhC,IAAI,IAAIA,IAAI,CAACiC,MAAM,CAACpD,OAAO,CAACsD,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9F,MAAMhC,aAAa,GAAG,IAAI,CAAC6B,UAAU,CAACQ,cAAc,CAAC;IACrDrC,aAAa,CAAC8B,MAAM,CAACU,MAAM,CAACxC,aAAa,CAAC8B,MAAM,CAACpD,OAAO,CAACsD,KAAK,CAAC,EAAE,CAAC,CAAC;IACnE,IAAI,CAACF,MAAM,CAACU,MAAM,CAACP,UAAU,EAAE,CAAC,CAAC;;IAEjC;IACA,IAAIjC,aAAa,CAAC8B,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE;MACrC;MACA,IAAIlC,aAAa,CAACsB,OAAO,EAAE;QACzBtB,aAAa,CAACsB,OAAO,CAAC,CAAC;MACzB;MACA,IAAIU,KAAK,CAACG,QAAQ,EAAE;QAClB;QACArE,UAAU,CAACkE,KAAK,CAACG,QAAQ,EAAEI,eAAe,CAAC;MAC7C;MACAzD,kBAAkB,CAACkB,aAAa,CAAC1C,SAAS,EAAE0E,KAAK,CAACI,KAAK,EAAEJ,KAAK,CAACG,QAAQ,EAAEnC,aAAa,CAAC0B,cAAc,EAAE,KAAK,CAAC;MAC7G,IAAI,CAACG,UAAU,CAACW,MAAM,CAACH,cAAc,EAAE,CAAC,CAAC;IAC3C,CAAC,MAAM;MACL;MACA,MAAMI,OAAO,GAAGzC,aAAa,CAAC8B,MAAM,CAAC9B,aAAa,CAAC8B,MAAM,CAACI,MAAM,GAAG,CAAC,CAAC;MACrE;MACA;MACA;MACA,IAAIO,OAAO,CAACN,QAAQ,EAAE;QACpBrE,UAAU,CAAC2E,OAAO,CAACN,QAAQ,EAAE,KAAK,CAAC;MACrC;IACF;IACA,OAAOF,UAAU;EACnB;EACAS,UAAUA,CAACV,KAAK,EAAE;IAChB,OAAO,IAAI,CAACF,MAAM,CAACI,MAAM,GAAG,CAAC,IAAI,IAAI,CAACJ,MAAM,CAAC,IAAI,CAACA,MAAM,CAACI,MAAM,GAAG,CAAC,CAAC,KAAKF,KAAK;EAChF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}