{"ast": null, "code": "export function getTypeByValue(value) {\n  const valueType = typeof value;\n  switch (valueType) {\n    case 'number':\n      if (Number.isNaN(value)) {\n        return 'NaN';\n      }\n      if (!Number.isFinite(value)) {\n        return 'Infinity';\n      }\n      if (value !== Math.floor(value)) {\n        return 'float';\n      }\n      return 'number';\n    case 'object':\n      if (value === null) {\n        return 'null';\n      }\n      return value.constructor.name;\n    default:\n      return valueType;\n  }\n}\n\n// IE 11 support\nfunction ponyfillIsInteger(x) {\n  // eslint-disable-next-line no-restricted-globals\n  return typeof x === 'number' && isFinite(x) && Math.floor(x) === x;\n}\nconst isInteger = Number.isInteger || ponyfillIsInteger;\nfunction requiredInteger(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue == null || !isInteger(propValue)) {\n    const propType = getTypeByValue(propValue);\n    return new RangeError(`Invalid ${location} \\`${propName}\\` of type \\`${propType}\\` supplied to \\`${componentName}\\`, expected \\`integer\\`.`);\n  }\n  return null;\n}\nfunction validator(props, propName, ...other) {\n  const propValue = props[propName];\n  if (propValue === undefined) {\n    return null;\n  }\n  return requiredInteger(props, propName, ...other);\n}\nfunction validatorNoop() {\n  return null;\n}\nvalidator.isRequired = requiredInteger;\nvalidatorNoop.isRequired = validatorNoop;\nexport default process.env.NODE_ENV === 'production' ? validatorNoop : validator;", "map": {"version": 3, "names": ["getTypeByValue", "value", "valueType", "Number", "isNaN", "isFinite", "Math", "floor", "constructor", "name", "ponyfillIsInteger", "x", "isInteger", "requiredInteger", "props", "propName", "componentName", "location", "propValue", "propType", "RangeError", "validator", "other", "undefined", "validatorNoop", "isRequired", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/utils/esm/integerPropType/integerPropType.js"], "sourcesContent": ["export function getTypeByValue(value) {\n  const valueType = typeof value;\n  switch (valueType) {\n    case 'number':\n      if (Number.isNaN(value)) {\n        return 'NaN';\n      }\n      if (!Number.isFinite(value)) {\n        return 'Infinity';\n      }\n      if (value !== Math.floor(value)) {\n        return 'float';\n      }\n      return 'number';\n    case 'object':\n      if (value === null) {\n        return 'null';\n      }\n      return value.constructor.name;\n    default:\n      return valueType;\n  }\n}\n\n// IE 11 support\nfunction ponyfillIsInteger(x) {\n  // eslint-disable-next-line no-restricted-globals\n  return typeof x === 'number' && isFinite(x) && Math.floor(x) === x;\n}\nconst isInteger = Number.isInteger || ponyfillIsInteger;\nfunction requiredInteger(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue == null || !isInteger(propValue)) {\n    const propType = getTypeByValue(propValue);\n    return new RangeError(`Invalid ${location} \\`${propName}\\` of type \\`${propType}\\` supplied to \\`${componentName}\\`, expected \\`integer\\`.`);\n  }\n  return null;\n}\nfunction validator(props, propName, ...other) {\n  const propValue = props[propName];\n  if (propValue === undefined) {\n    return null;\n  }\n  return requiredInteger(props, propName, ...other);\n}\nfunction validatorNoop() {\n  return null;\n}\nvalidator.isRequired = requiredInteger;\nvalidatorNoop.isRequired = validatorNoop;\nexport default process.env.NODE_ENV === 'production' ? validatorNoop : validator;"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,KAAK,EAAE;EACpC,MAAMC,SAAS,GAAG,OAAOD,KAAK;EAC9B,QAAQC,SAAS;IACf,KAAK,QAAQ;MACX,IAAIC,MAAM,CAACC,KAAK,CAACH,KAAK,CAAC,EAAE;QACvB,OAAO,KAAK;MACd;MACA,IAAI,CAACE,MAAM,CAACE,QAAQ,CAACJ,KAAK,CAAC,EAAE;QAC3B,OAAO,UAAU;MACnB;MACA,IAAIA,KAAK,KAAKK,IAAI,CAACC,KAAK,CAACN,KAAK,CAAC,EAAE;QAC/B,OAAO,OAAO;MAChB;MACA,OAAO,QAAQ;IACjB,KAAK,QAAQ;MACX,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,MAAM;MACf;MACA,OAAOA,KAAK,CAACO,WAAW,CAACC,IAAI;IAC/B;MACE,OAAOP,SAAS;EACpB;AACF;;AAEA;AACA,SAASQ,iBAAiBA,CAACC,CAAC,EAAE;EAC5B;EACA,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIN,QAAQ,CAACM,CAAC,CAAC,IAAIL,IAAI,CAACC,KAAK,CAACI,CAAC,CAAC,KAAKA,CAAC;AACpE;AACA,MAAMC,SAAS,GAAGT,MAAM,CAACS,SAAS,IAAIF,iBAAiB;AACvD,SAASG,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EACjE,MAAMC,SAAS,GAAGJ,KAAK,CAACC,QAAQ,CAAC;EACjC,IAAIG,SAAS,IAAI,IAAI,IAAI,CAACN,SAAS,CAACM,SAAS,CAAC,EAAE;IAC9C,MAAMC,QAAQ,GAAGnB,cAAc,CAACkB,SAAS,CAAC;IAC1C,OAAO,IAAIE,UAAU,CAAC,WAAWH,QAAQ,MAAMF,QAAQ,gBAAgBI,QAAQ,oBAAoBH,aAAa,2BAA2B,CAAC;EAC9I;EACA,OAAO,IAAI;AACb;AACA,SAASK,SAASA,CAACP,KAAK,EAAEC,QAAQ,EAAE,GAAGO,KAAK,EAAE;EAC5C,MAAMJ,SAAS,GAAGJ,KAAK,CAACC,QAAQ,CAAC;EACjC,IAAIG,SAAS,KAAKK,SAAS,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,OAAOV,eAAe,CAACC,KAAK,EAAEC,QAAQ,EAAE,GAAGO,KAAK,CAAC;AACnD;AACA,SAASE,aAAaA,CAAA,EAAG;EACvB,OAAO,IAAI;AACb;AACAH,SAAS,CAACI,UAAU,GAAGZ,eAAe;AACtCW,aAAa,CAACC,UAAU,GAAGD,aAAa;AACxC,eAAeE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,aAAa,GAAGH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}