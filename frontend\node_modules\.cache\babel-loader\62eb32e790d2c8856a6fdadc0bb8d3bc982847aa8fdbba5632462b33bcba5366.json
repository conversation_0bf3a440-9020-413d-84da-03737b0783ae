{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Heading5 = createLucideIcon(\"Heading5\", [[\"path\", {\n  d: \"M4 12h8\",\n  key: \"17cfdx\"\n}], [\"path\", {\n  d: \"M4 18V6\",\n  key: \"1rz3zl\"\n}], [\"path\", {\n  d: \"M12 18V6\",\n  key: \"zqpxq5\"\n}], [\"path\", {\n  d: \"M17 13v-3h4\",\n  key: \"1nvgqp\"\n}], [\"path\", {\n  d: \"M17 17.7c.******* 1.3.3 1.5 0 2.7-1.1 2.7-2.5S19.8 13 18.3 13H17\",\n  key: \"2nebdn\"\n}]]);\nexport { Heading5 as default };", "map": {"version": 3, "names": ["Heading5", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\heading-5.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Heading5\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/heading-5\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heading5 = createLucideIcon('Heading5', [\n  ['path', { d: 'M4 12h8', key: '17cfdx' }],\n  ['path', { d: 'M4 18V6', key: '1rz3zl' }],\n  ['path', { d: 'M12 18V6', key: 'zqpxq5' }],\n  ['path', { d: 'M17 13v-3h4', key: '1nvgqp' }],\n  [\n    'path',\n    { d: 'M17 17.7c.******* 1.3.3 1.5 0 2.7-1.1 2.7-2.5S19.8 13 18.3 13H17', key: '2nebdn' },\n  ],\n]);\n\nexport default Heading5;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CACE,QACA;EAAED,CAAA,EAAG,kEAAoE;EAAAC,GAAA,EAAK;AAAS,EACzF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}