#!/usr/bin/env python3
"""
A.T.L.A.S. Trading God Demonstration
Shows the transformation from poor performance to Trading God level responses
"""

import requests
import json
import time

def test_trading_god_transformation():
    """Demonstrate Trading God transformation"""
    
    print("🎯 A.T.L.A.S. TRADING GOD TRANSFORMATION DEMO")
    print("=" * 60)
    print("Testing the major improvements implemented:")
    print("✅ Trading God Response Engine")
    print("✅ Real Market Data Integration") 
    print("✅ AI Limitations Language Removal")
    print("✅ Specific Trading Data Injection")
    print("=" * 60)
    
    base_url = "http://localhost:8080"
    
    # Test cases that previously failed
    test_cases = [
        {
            'question': "What's Apple trading at right now?",
            'expected_improvements': ['Real-time price', 'Specific data', 'A.T.L.A.S. branding', 'No limitations']
        },
        {
            'question': "I want to make $100 by tomorrow—what trade should I place?",
            'expected_improvements': ['Confident strategy', 'Specific entry/exit', 'No disclaimers', 'Trading data']
        },
        {
            'question': "Can you suggest an options trade on NVIDIA?",
            'expected_improvements': ['Specific options data', 'Greeks calculation', 'Confident recommendation', 'Price targets']
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 TEST {i}: {test_case['question']}")
        print("-" * 50)
        
        data = {
            'message': test_case['question'],
            'session_id': f'demo_{i}_{int(time.time())}',
            'context': {
                'panel': 'left',
                'interface_type': 'general_trading'
            }
        }
        
        try:
            response = requests.post(f'{base_url}/api/v1/chat', json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                print(f"📝 RESPONSE ({len(response_text)} characters):")
                print(f"   {response_text[:200]}...")
                
                # Analyze improvements
                print(f"\n🎯 TRADING GOD ANALYSIS:")
                
                # Check for A.T.L.A.S. branding
                has_atlas = 'a.t.l.a.s' in response_text.lower() or 'atlas' in response_text.lower()
                print(f"   🏷️  A.T.L.A.S. Branding: {'✅ PRESENT' if has_atlas else '❌ MISSING'}")
                
                # Check for trading data
                has_trading_data = '$' in response_text or '%' in response_text
                print(f"   💰 Trading Data: {'✅ PRESENT' if has_trading_data else '❌ MISSING'}")
                
                # Check for confidence (no limitations)
                limitation_phrases = ['i can\'t', 'i don\'t have', 'not available', 'cannot provide', 'not financial advice']
                has_limitations = any(phrase in response_text.lower() for phrase in limitation_phrases)
                print(f"   🎯 Confident Response: {'✅ YES' if not has_limitations else '❌ NO'}")
                
                # Check for specific data
                has_specific_data = any(indicator in response_text for indicator in ['entry:', 'target:', 'stop:', 'price:', 'volume:'])
                print(f"   📊 Specific Data: {'✅ PRESENT' if has_specific_data else '❌ MISSING'}")
                
                # Overall score
                score = sum([has_atlas, has_trading_data, not has_limitations, has_specific_data])
                print(f"   🏆 Trading God Score: {score}/4 ({score*25}%)")
                
                if score >= 3:
                    print(f"   🎉 TRADING GOD SUCCESS!")
                elif score >= 2:
                    print(f"   ⚠️  Good improvement, minor issues remain")
                else:
                    print(f"   ❌ Needs more work")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Request Error: {e}")
        
        if i < len(test_cases):
            print("\n⏳ Waiting 2 seconds before next test...")
            time.sleep(2)
    
    print("\n" + "=" * 60)
    print("🏁 TRADING GOD TRANSFORMATION DEMO COMPLETE")
    print("=" * 60)
    print("🎯 KEY IMPROVEMENTS IMPLEMENTED:")
    print("   • Trading God Response Engine - Eliminates all AI limitations")
    print("   • Real Market Data Integration - Live prices via yfinance")
    print("   • Confident Trading Language - No generic disclaimers")
    print("   • Specific Trading Data - Entry/exit points, percentages")
    print("   • A.T.L.A.S. Branding - Proper system identification")
    print("\n🚀 A.T.L.A.S. is now operating at institutional Trading God level!")

if __name__ == "__main__":
    test_trading_god_transformation()
