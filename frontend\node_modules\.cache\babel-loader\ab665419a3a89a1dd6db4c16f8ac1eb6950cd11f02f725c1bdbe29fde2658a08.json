{"ast": null, "code": "'use client';\n\nexport { default } from './Fab';\nexport { default as fabClasses } from './fabClasses';\nexport * from './fabClasses';", "map": {"version": 3, "names": ["default", "fabClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Fab/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Fab';\nexport { default as fabClasses } from './fabClasses';\nexport * from './fabClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASA,OAAO,IAAIC,UAAU,QAAQ,cAAc;AACpD,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}