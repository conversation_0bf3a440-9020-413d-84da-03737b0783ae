{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"component\", \"onChange\", \"showLabels\", \"value\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getBottomNavigationUtilityClass } from './bottomNavigationClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getBottomNavigationUtilityClass, classes);\n};\nconst BottomNavigationRoot = styled('div', {\n  name: 'MuiBottomNavigation',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'center',\n  height: 56,\n  backgroundColor: (theme.vars || theme).palette.background.paper\n}));\nconst BottomNavigation = /*#__PURE__*/React.forwardRef(function BottomNavigation(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBottomNavigation'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      onChange,\n      showLabels = false,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    showLabels\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(BottomNavigationRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: React.Children.map(children, (child, childIndex) => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The BottomNavigation component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      const childValue = child.props.value === undefined ? childIndex : child.props.value;\n      return /*#__PURE__*/React.cloneElement(child, {\n        selected: childValue === value,\n        showLabel: child.props.showLabel !== undefined ? child.props.showLabel : showLabels,\n        value: childValue,\n        onChange\n      });\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? BottomNavigation.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child.\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, all `BottomNavigationAction`s will show their labels.\n   * By default, only the selected `BottomNavigationAction` will show its label.\n   * @default false\n   */\n  showLabels: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the currently selected `BottomNavigationAction`.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default BottomNavigation;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "isFragment", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getBottomNavigationUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "BottomNavigationRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "display", "justifyContent", "height", "backgroundColor", "vars", "palette", "background", "paper", "BottomNavigation", "forwardRef", "inProps", "ref", "children", "className", "component", "onChange", "showLabels", "value", "other", "as", "Children", "map", "child", "childIndex", "isValidElement", "process", "env", "NODE_ENV", "console", "error", "join", "childValue", "undefined", "cloneElement", "selected", "showLabel", "propTypes", "node", "object", "string", "elementType", "func", "bool", "sx", "oneOfType", "arrayOf", "any"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/BottomNavigation/BottomNavigation.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"component\", \"onChange\", \"showLabels\", \"value\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getBottomNavigationUtilityClass } from './bottomNavigationClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getBottomNavigationUtilityClass, classes);\n};\nconst BottomNavigationRoot = styled('div', {\n  name: 'MuiBottomNavigation',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'center',\n  height: 56,\n  backgroundColor: (theme.vars || theme).palette.background.paper\n}));\nconst BottomNavigation = /*#__PURE__*/React.forwardRef(function BottomNavigation(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBottomNavigation'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      onChange,\n      showLabels = false,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    showLabels\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(BottomNavigationRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: React.Children.map(children, (child, childIndex) => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The BottomNavigation component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      const childValue = child.props.value === undefined ? childIndex : child.props.value;\n      return /*#__PURE__*/React.cloneElement(child, {\n        selected: childValue === value,\n        showLabel: child.props.showLabel !== undefined ? child.props.showLabel : showLabels,\n        value: childValue,\n        onChange\n      });\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? BottomNavigation.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child.\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, all `BottomNavigationAction`s will show their labels.\n   * By default, only the selected `BottomNavigationAction` will show its label.\n   * @default false\n   */\n  showLabels: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the currently selected `BottomNavigationAction`.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default BottomNavigation;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC;AAC3F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,+BAA+B,QAAQ,2BAA2B;AAC3E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEN,+BAA+B,EAAEK,OAAO,CAAC;AACxE,CAAC;AACD,MAAMG,oBAAoB,GAAGV,MAAM,CAAC,KAAK,EAAE;EACzCW,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFO;AACF,CAAC,MAAM;EACLC,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,QAAQ;EACxBC,MAAM,EAAE,EAAE;EACVC,eAAe,EAAE,CAACJ,KAAK,CAACK,IAAI,IAAIL,KAAK,EAAEM,OAAO,CAACC,UAAU,CAACC;AAC5D,CAAC,CAAC,CAAC;AACH,MAAMC,gBAAgB,GAAG,aAAa9B,KAAK,CAAC+B,UAAU,CAAC,SAASD,gBAAgBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7F,MAAMd,KAAK,GAAGb,eAAe,CAAC;IAC5Ba,KAAK,EAAEa,OAAO;IACdhB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFkB,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,QAAQ;MACRC,UAAU,GAAG,KAAK;MAClBC;IACF,CAAC,GAAGpB,KAAK;IACTqB,KAAK,GAAG1C,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAMY,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCiB,SAAS;IACTE;EACF,CAAC,CAAC;EACF,MAAM1B,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,oBAAoB,EAAElB,QAAQ,CAAC;IACtD4C,EAAE,EAAEL,SAAS;IACbD,SAAS,EAAEhC,IAAI,CAACS,OAAO,CAACE,IAAI,EAAEqB,SAAS,CAAC;IACxCF,GAAG,EAAEA,GAAG;IACRtB,UAAU,EAAEA;EACd,CAAC,EAAE6B,KAAK,EAAE;IACRN,QAAQ,EAAElC,KAAK,CAAC0C,QAAQ,CAACC,GAAG,CAACT,QAAQ,EAAE,CAACU,KAAK,EAAEC,UAAU,KAAK;MAC5D,IAAI,EAAE,aAAa7C,KAAK,CAAC8C,cAAc,CAACF,KAAK,CAAC,EAAE;QAC9C,OAAO,IAAI;MACb;MACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIhD,UAAU,CAAC2C,KAAK,CAAC,EAAE;UACrBM,OAAO,CAACC,KAAK,CAAC,CAAC,2EAA2E,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjJ;MACF;MACA,MAAMC,UAAU,GAAGT,KAAK,CAACzB,KAAK,CAACoB,KAAK,KAAKe,SAAS,GAAGT,UAAU,GAAGD,KAAK,CAACzB,KAAK,CAACoB,KAAK;MACnF,OAAO,aAAavC,KAAK,CAACuD,YAAY,CAACX,KAAK,EAAE;QAC5CY,QAAQ,EAAEH,UAAU,KAAKd,KAAK;QAC9BkB,SAAS,EAAEb,KAAK,CAACzB,KAAK,CAACsC,SAAS,KAAKH,SAAS,GAAGV,KAAK,CAACzB,KAAK,CAACsC,SAAS,GAAGnB,UAAU;QACnFC,KAAK,EAAEc,UAAU;QACjBhB;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,gBAAgB,CAAC4B,SAAS,CAAC,yBAAyB;EAC1F;EACA;EACA;EACA;EACA;AACF;AACA;EACExB,QAAQ,EAAEhC,SAAS,CAACyD,IAAI;EACxB;AACF;AACA;EACE/C,OAAO,EAAEV,SAAS,CAAC0D,MAAM;EACzB;AACF;AACA;EACEzB,SAAS,EAAEjC,SAAS,CAAC2D,MAAM;EAC3B;AACF;AACA;AACA;EACEzB,SAAS,EAAElC,SAAS,CAAC4D,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;EACEzB,QAAQ,EAAEnC,SAAS,CAAC6D,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEzB,UAAU,EAAEpC,SAAS,CAAC8D,IAAI;EAC1B;AACF;AACA;EACEC,EAAE,EAAE/D,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAACiE,OAAO,CAACjE,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAAC6D,IAAI,EAAE7D,SAAS,CAAC0D,MAAM,EAAE1D,SAAS,CAAC8D,IAAI,CAAC,CAAC,CAAC,EAAE9D,SAAS,CAAC6D,IAAI,EAAE7D,SAAS,CAAC0D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACErB,KAAK,EAAErC,SAAS,CAACkE;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAetC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}