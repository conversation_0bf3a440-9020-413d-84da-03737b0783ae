#!/usr/bin/env python3
"""
Final A.T.L.A.S. Beginner Test Suite
Tests all 19 beginner-style prompts and provides comprehensive results
"""

import requests
import time
import json
from datetime import datetime

def test_all_prompts():
    base_url = "http://localhost:8080"
    
    # All 19 beginner-style test prompts
    test_prompts = [
        "What's Apple trading at right now?",
        "Are there any stocks that look like they might jump soon?",
        "Why did Tesla move up today? What's the news behind it?",
        "Where do you think Microsoft will be in five days?",
        "Can you suggest an options trade on NVIDIA that could make me money this week?",
        "I want to make $100 by tomorrow—what trade should I place?",
        "Please buy 10 shares of Amazon for me now.",
        "Alert me when Google goes up more than 2% today.",
        "How can I protect my Tesla shares if they start falling?",
        "Can you optimize my portfolio to boost returns?",
        "How risky is buying 20 shares of Shopify right now?",
        "Give me a quick morning market briefing for today.",
        "Show me any unusual options activity in Netflix.",
        "Backtest a simple breakout strategy on the S&P 500 over the last month.",
        "Which forex pair could earn me $50 today?",
        "Alert me when the VIX index rises above 20.",
        "What's the best ETF for steady growth this month?",
        "Help me make $200 this week with minimal trades.",
        "Do any cryptocurrencies look good right now? If so, buy 1 ETH."
    ]
    
    print("🚀 A.T.L.A.S. COMPREHENSIVE BEGINNER TEST SUITE")
    print("=" * 70)
    print(f"📊 Total Tests: {len(test_prompts)}")
    print(f"🔗 Server: {base_url}")
    
    # Check server health
    try:
        health = requests.get(f"{base_url}/api/v1/health", timeout=5)
        if health.status_code == 200:
            print("✅ Server is healthy")
        else:
            print("❌ Server health check failed")
            return
    except:
        print("❌ Cannot connect to server")
        return
    
    print("\n🧪 Running Tests...")
    
    results = []
    passed = 0
    failed = 0
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n[{i:2d}/{len(test_prompts)}] {prompt[:50]}{'...' if len(prompt) > 50 else ''}")
        
        try:
            response = requests.post(
                f"{base_url}/api/v1/chat",
                json={
                    "message": prompt,
                    "session_id": f"test_{i}",
                    "context": {"test_mode": True}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get("response", "")
                
                # Evaluate response
                score = 0
                issues = []
                
                # Check A.T.L.A.S. branding
                if "a.t.l.a.s" in response_text.lower():
                    score += 2
                else:
                    issues.append("Missing A.T.L.A.S. branding")
                
                # Check Predicto branding
                if "predicto" in response_text.lower():
                    score += 1
                else:
                    issues.append("Missing Predicto branding")
                
                # Check for specific trading data
                if "$" in response_text and any(term in response_text.lower() for term in ["target", "entry", "stop", "confidence"]):
                    score += 3
                elif "$" in response_text:
                    score += 2
                    issues.append("Limited trading data")
                else:
                    issues.append("No specific trading data")
                
                # Check for Trading God persona (no limitations)
                if not any(phrase in response_text.lower() for phrase in ["i can't", "i don't have", "i'm unable", "as an ai"]):
                    score += 2
                else:
                    issues.append("Contains AI limitation language")
                
                # Check response quality
                if len(response_text) > 100:
                    score += 2
                elif len(response_text) > 50:
                    score += 1
                    issues.append("Short response")
                else:
                    issues.append("Very short response")
                
                test_passed = score >= 7  # 70% threshold
                if test_passed:
                    passed += 1
                    print(f"      ✅ PASSED - Score: {score}/10")
                else:
                    failed += 1
                    print(f"      ❌ FAILED - Score: {score}/10")
                    print(f"         Issues: {', '.join(issues)}")
                
                results.append({
                    "prompt": prompt,
                    "score": score,
                    "passed": test_passed,
                    "issues": issues,
                    "response_length": len(response_text),
                    "response_preview": response_text[:100] + "..." if len(response_text) > 100 else response_text
                })
                
            else:
                failed += 1
                print(f"      ❌ FAILED - HTTP {response.status_code}")
                results.append({
                    "prompt": prompt,
                    "score": 0,
                    "passed": False,
                    "issues": [f"HTTP {response.status_code}"],
                    "response_length": 0,
                    "response_preview": ""
                })
                
        except Exception as e:
            failed += 1
            print(f"      ❌ FAILED - Exception: {str(e)}")
            results.append({
                "prompt": prompt,
                "score": 0,
                "passed": False,
                "issues": [f"Exception: {str(e)}"],
                "response_length": 0,
                "response_preview": ""
            })
        
        time.sleep(0.5)  # Brief pause between tests
    
    # Generate final report
    total_tests = len(results)
    pass_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    avg_score = sum(r['score'] for r in results) / total_tests if total_tests > 0 else 0
    
    print("\n" + "=" * 70)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 70)
    
    print(f"\n📊 OVERALL PERFORMANCE:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed} ✅")
    print(f"   Failed: {failed} ❌")
    print(f"   Pass Rate: {pass_rate:.1f}%")
    print(f"   Average Score: {avg_score:.1f}/10")
    
    # Show failed tests
    failed_tests = [r for r in results if not r['passed']]
    if failed_tests:
        print(f"\n🚨 FAILED TESTS ({len(failed_tests)}):")
        for i, test in enumerate(failed_tests[:5], 1):  # Show first 5
            print(f"   {i}. \"{test['prompt'][:40]}{'...' if len(test['prompt']) > 40 else ''}\"")
            print(f"      Score: {test['score']}/10 | Issues: {', '.join(test['issues'])}")
        if len(failed_tests) > 5:
            print(f"   ... and {len(failed_tests) - 5} more")
    
    # Overall assessment
    if pass_rate >= 90:
        print(f"\n🏆 EXCELLENT: A.T.L.A.S. is performing at institutional level!")
    elif pass_rate >= 80:
        print(f"\n✅ GOOD: A.T.L.A.S. meets Trading God standards!")
    elif pass_rate >= 70:
        print(f"\n⚠️  ACCEPTABLE: A.T.L.A.S. needs minor improvements")
    else:
        print(f"\n❌ NEEDS WORK: A.T.L.A.S. requires significant improvements")
    
    # Key findings
    print(f"\n🔍 KEY FINDINGS:")
    branding_issues = sum(1 for r in results if "Missing A.T.L.A.S. branding" in r['issues'])
    trading_data_issues = sum(1 for r in results if "No specific trading data" in r['issues'])
    limitation_issues = sum(1 for r in results if "Contains AI limitation language" in r['issues'])
    
    print(f"   Missing A.T.L.A.S. branding: {branding_issues}/{total_tests} tests")
    print(f"   Missing trading data: {trading_data_issues}/{total_tests} tests")
    print(f"   AI limitation language: {limitation_issues}/{total_tests} tests")
    
    print("=" * 70)
    
    # Save detailed report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"atlas_test_report_{timestamp}.json"
    
    with open(report_file, 'w') as f:
        json.dump({
            "summary": {
                "total_tests": total_tests,
                "passed": passed,
                "failed": failed,
                "pass_rate": pass_rate,
                "average_score": avg_score,
                "timestamp": datetime.now().isoformat()
            },
            "results": results
        }, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: {report_file}")

if __name__ == "__main__":
    test_all_prompts()
