{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    d: \"M11.94 18.24c-.24.45-.94.28-.94-.24v-1H7v3.67C7 21.4 7.6 22 8.33 22h7.33c.74 0 1.34-.6 1.34-1.33V17h-4.4l-.66 1.24z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M15.67 4H14V3c0-.55-.45-1-1-1h-2c-.55 0-1 .45-1 1v1H8.33C7.6 4 7 4.6 7 5.33V17h4v-2.5H9.83c-.38 0-.62-.4-.44-.74l2.67-5c.24-.45.94-.28.94.24v3.5h1.17c.38 0 .62.4.44.74L12.6 17H17V5.33C17 4.6 16.4 4 15.67 4z\"\n  })]\n}), 'BatteryCharging20Rounded');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "children", "d", "fillOpacity"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/BatteryCharging20Rounded.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    d: \"M11.94 18.24c-.24.45-.94.28-.94-.24v-1H7v3.67C7 21.4 7.6 22 8.33 22h7.33c.74 0 1.34-.6 1.34-1.33V17h-4.4l-.66 1.24z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M15.67 4H14V3c0-.55-.45-1-1-1h-2c-.55 0-1 .45-1 1v1H8.33C7.6 4 7 4.6 7 5.33V17h4v-2.5H9.83c-.38 0-.62-.4-.44-.74l2.67-5c.24-.45.94-.28.94.24v3.5h1.17c.38 0 .62.4.44.74L12.6 17H17V5.33C17 4.6 16.4 4 15.67 4z\"\n  })]\n}), 'BatteryCharging20Rounded');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,eAAeJ,aAAa,CAAE,aAAaI,KAAK,CAACL,KAAK,CAACM,QAAQ,EAAE;EAC/DC,QAAQ,EAAE,CAAC,aAAaJ,IAAI,CAAC,MAAM,EAAE;IACnCK,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAaL,IAAI,CAAC,MAAM,EAAE;IAC5BM,WAAW,EAAE,IAAI;IACjBD,CAAC,EAAE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC,EAAE,0BAA0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}