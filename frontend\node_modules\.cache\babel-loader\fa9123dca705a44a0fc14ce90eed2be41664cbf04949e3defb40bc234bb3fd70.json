{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\AccountInfo.js\";\nimport React from 'react';\nimport { Card, CardContent, Typography, Grid } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountInfo = ({\n  accountData\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Account Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Portfolio Value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [\"$\", (accountData === null || accountData === void 0 ? void 0 : accountData.portfolio_value) || '0.00']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Buying Power\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [\"$\", (accountData === null || accountData === void 0 ? void 0 : accountData.buying_power) || '0.00']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Day Trade Count\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: (accountData === null || accountData === void 0 ? void 0 : accountData.daytrade_count) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: (accountData === null || accountData === void 0 ? void 0 : accountData.status) || 'ACTIVE'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = AccountInfo;\nexport default AccountInfo;\nvar _c;\n$RefreshReg$(_c, \"AccountInfo\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grid", "jsxDEV", "_jsxDEV", "AccountInfo", "accountData", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "xs", "color", "portfolio_value", "buying_power", "daytrade_count", "status", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AccountInfo.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography, Grid } from '@mui/material';\n\nconst AccountInfo = ({ accountData }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Account Information\n        </Typography>\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Portfolio Value\n            </Typography>\n            <Typography variant=\"h6\">\n              ${accountData?.portfolio_value || '0.00'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Buying Power\n            </Typography>\n            <Typography variant=\"h6\">\n              ${accountData?.buying_power || '0.00'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Day Trade Count\n            </Typography>\n            <Typography variant=\"h6\">\n              {accountData?.daytrade_count || 0}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Status\n            </Typography>\n            <Typography variant=\"h6\">\n              {accountData?.status || 'ACTIVE'}\n            </Typography>\n          </Grid>\n        </Grid>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AccountInfo;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EACvC,oBACEF,OAAA,CAACL,IAAI;IAAAQ,QAAA,eACHH,OAAA,CAACJ,WAAW;MAAAO,QAAA,gBACVH,OAAA,CAACH,UAAU;QAACO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbT,OAAA,CAACF,IAAI;QAACY,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBACzBH,OAAA,CAACF,IAAI;UAACc,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACfH,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,OAAO;YAACU,KAAK,EAAC,eAAe;YAAAX,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbT,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,IAAI;YAAAD,QAAA,GAAC,GACtB,EAAC,CAAAD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEa,eAAe,KAAI,MAAM;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPT,OAAA,CAACF,IAAI;UAACc,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACfH,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,OAAO;YAACU,KAAK,EAAC,eAAe;YAAAX,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbT,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,IAAI;YAAAD,QAAA,GAAC,GACtB,EAAC,CAAAD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,YAAY,KAAI,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPT,OAAA,CAACF,IAAI;UAACc,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACfH,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,OAAO;YAACU,KAAK,EAAC,eAAe;YAAAX,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbT,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,IAAI;YAAAD,QAAA,EACrB,CAAAD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,cAAc,KAAI;UAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPT,OAAA,CAACF,IAAI;UAACc,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACfH,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,OAAO;YAACU,KAAK,EAAC,eAAe;YAAAX,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbT,OAAA,CAACH,UAAU;YAACO,OAAO,EAAC,IAAI;YAAAD,QAAA,EACrB,CAAAD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACU,EAAA,GA5CIlB,WAAW;AA8CjB,eAAeA,WAAW;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}