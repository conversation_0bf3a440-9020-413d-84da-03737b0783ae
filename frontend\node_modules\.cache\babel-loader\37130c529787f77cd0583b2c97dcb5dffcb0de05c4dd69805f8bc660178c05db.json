{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ChevronsDownUp = createLucideIcon(\"ChevronsDownUp\", [[\"path\", {\n  d: \"m7 20 5-5 5 5\",\n  key: \"13a0gw\"\n}], [\"path\", {\n  d: \"m7 4 5 5 5-5\",\n  key: \"1kwcof\"\n}]]);\nexport { ChevronsDownUp as default };", "map": {"version": 3, "names": ["ChevronsDownUp", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\chevrons-down-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronsDownUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNyAyMCA1LTUgNSA1IiAvPgogIDxwYXRoIGQ9Im03IDQgNSA1IDUtNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevrons-down-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronsDownUp = createLucideIcon('ChevronsDownUp', [\n  ['path', { d: 'm7 20 5-5 5 5', key: '13a0gw' }],\n  ['path', { d: 'm7 4 5 5 5-5', key: '1kwcof' }],\n]);\n\nexport default ChevronsDownUp;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}