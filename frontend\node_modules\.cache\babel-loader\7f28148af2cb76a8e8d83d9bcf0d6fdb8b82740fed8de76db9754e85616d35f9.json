{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { interpolate } from './interpolate.mjs';\nconst isCustomValueType = v => {\n  return v && typeof v === \"object\" && v.mix;\n};\nconst getMixer = v => isCustomValueType(v) ? v.mix : undefined;\nfunction transform() {\n  const useImmediate = !Array.isArray(arguments.length <= 0 ? undefined : arguments[0]);\n  const argOffset = useImmediate ? 0 : -1;\n  const inputValue = 0 + argOffset < 0 || arguments.length <= 0 + argOffset ? undefined : arguments[0 + argOffset];\n  const inputRange = 1 + argOffset < 0 || arguments.length <= 1 + argOffset ? undefined : arguments[1 + argOffset];\n  const outputRange = 2 + argOffset < 0 || arguments.length <= 2 + argOffset ? undefined : arguments[2 + argOffset];\n  const options = 3 + argOffset < 0 || arguments.length <= 3 + argOffset ? undefined : arguments[3 + argOffset];\n  const interpolator = interpolate(inputRange, outputRange, _objectSpread({\n    mixer: getMixer(outputRange[0])\n  }, options));\n  return useImmediate ? interpolator(inputValue) : interpolator;\n}\nexport { transform };", "map": {"version": 3, "names": ["interpolate", "isCustomValueType", "v", "mix", "getMixer", "undefined", "transform", "useImmediate", "Array", "isArray", "arguments", "length", "argOffset", "inputValue", "inputRange", "outputRange", "options", "interpolator", "_objectSpread", "mixer"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/utils/transform.mjs"], "sourcesContent": ["import { interpolate } from './interpolate.mjs';\n\nconst isCustomValueType = (v) => {\n    return v && typeof v === \"object\" && v.mix;\n};\nconst getMixer = (v) => (isCustomValueType(v) ? v.mix : undefined);\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = interpolate(inputRange, outputRange, {\n        mixer: getMixer(outputRange[0]),\n        ...options,\n    });\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\nexport { transform };\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;EAC7B,OAAOA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,GAAG;AAC9C,CAAC;AACD,MAAMC,QAAQ,GAAIF,CAAC,IAAMD,iBAAiB,CAACC,CAAC,CAAC,GAAGA,CAAC,CAACC,GAAG,GAAGE,SAAU;AAClE,SAASC,SAASA,CAAA,EAAU;EACxB,MAAMC,YAAY,GAAG,CAACC,KAAK,CAACC,OAAO,CAAAC,SAAA,CAAAC,MAAA,QAAAN,SAAA,GAAAK,SAAA,GAAQ,CAAC;EAC5C,MAAME,SAAS,GAAGL,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,MAAMM,UAAU,GAAQ,CAAC,GAAGD,SAAS,QAAAF,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGC,SAAS,GAAAP,SAAA,GAAAK,SAAA,CAAb,CAAC,GAAGE,SAAS,CAAC;EACtC,MAAME,UAAU,GAAQ,CAAC,GAAGF,SAAS,QAAAF,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGC,SAAS,GAAAP,SAAA,GAAAK,SAAA,CAAb,CAAC,GAAGE,SAAS,CAAC;EACtC,MAAMG,WAAW,GAAQ,CAAC,GAAGH,SAAS,QAAAF,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGC,SAAS,GAAAP,SAAA,GAAAK,SAAA,CAAb,CAAC,GAAGE,SAAS,CAAC;EACvC,MAAMI,OAAO,GAAQ,CAAC,GAAGJ,SAAS,QAAAF,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGC,SAAS,GAAAP,SAAA,GAAAK,SAAA,CAAb,CAAC,GAAGE,SAAS,CAAC;EACnC,MAAMK,YAAY,GAAGjB,WAAW,CAACc,UAAU,EAAEC,WAAW,EAAAG,aAAA;IACpDC,KAAK,EAAEf,QAAQ,CAACW,WAAW,CAAC,CAAC,CAAC;EAAC,GAC5BC,OAAO,CACb,CAAC;EACF,OAAOT,YAAY,GAAGU,YAAY,CAACJ,UAAU,CAAC,GAAGI,YAAY;AACjE;AAEA,SAASX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}