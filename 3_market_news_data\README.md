# 3. Market & News Data

This folder contains all market data fetching, analysis, and prediction components.

## Files:
- `atlas_market_engine.py` - Real-time market data engine
- `atlas_market_context.py` - Market context and analysis
- `atlas_sentiment_analyzer.py` - News and social sentiment analysis
- `atlas_ml_predictor.py` - LSTM neural network predictions
- `atlas_realtime_scanner.py` - Real-time market scanning
- `atlas_stock_intelligence_hub.py` - Comprehensive stock intelligence
- `atlas_options_flow_analyzer.py` - Options flow and unusual activity

## Purpose:
These files handle all external data sources - stock prices, news feeds, social sentiment, options flow, and ML predictions. This is A.T.L.A.S.'s "eyes and ears" on the market.
