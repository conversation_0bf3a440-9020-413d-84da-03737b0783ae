{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst LampWallDown = createLucideIcon(\"LampWallDown\", [[\"path\", {\n  d: \"M11 13h6l3 7H8l3-7Z\",\n  key: \"9n3qlo\"\n}], [\"path\", {\n  d: \"M14 13V8a2 2 0 0 0-2-2H8\",\n  key: \"1hu4hb\"\n}], [\"path\", {\n  d: \"M4 9h2a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H4v6Z\",\n  key: \"s053bc\"\n}]]);\nexport { LampWallDown as default };", "map": {"version": 3, "names": ["LampWallDown", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\lamp-wall-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LampWallDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMTNoNmwzIDdIOGwzLTdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAxM1Y4YTIgMiAwIDAgMC0yLTJIOCIgLz4KICA8cGF0aCBkPSJNNCA5aDJhMiAyIDAgMCAwIDItMlY1YTIgMiAwIDAgMC0yLTJINHY2WiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/lamp-wall-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LampWallDown = createLucideIcon('LampWallDown', [\n  ['path', { d: 'M11 13h6l3 7H8l3-7Z', key: '9n3qlo' }],\n  ['path', { d: 'M14 13V8a2 2 0 0 0-2-2H8', key: '1hu4hb' }],\n  ['path', { d: 'M4 9h2a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H4v6Z', key: 's053bc' }],\n]);\n\nexport default LampWallDown;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}