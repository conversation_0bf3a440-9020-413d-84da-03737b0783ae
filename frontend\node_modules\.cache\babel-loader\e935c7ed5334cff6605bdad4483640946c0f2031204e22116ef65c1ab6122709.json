{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rows\", \"style\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport ImageListContext from '../ImageList/ImageListContext';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport isMuiElement from '../utils/isMuiElement';\nimport imageListItemClasses, { getImageListItemUtilityClass } from './imageListItemClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant],\n    img: ['img']\n  };\n  return composeClasses(slots, getImageListItemUtilityClass, classes);\n};\nconst ImageListItemRoot = styled('li', {\n  name: 'MuiImageListItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [\"& .\".concat(imageListItemClasses.img)]: styles.img\n    }, styles.root, styles[ownerState.variant]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    display: 'block',\n    position: 'relative'\n  }, ownerState.variant === 'standard' && {\n    // For titlebar under list item\n    display: 'flex',\n    flexDirection: 'column'\n  }, ownerState.variant === 'woven' && {\n    height: '100%',\n    alignSelf: 'center',\n    '&:nth-of-type(even)': {\n      height: '70%'\n    }\n  }, {\n    [\"& .\".concat(imageListItemClasses.img)]: _extends({\n      objectFit: 'cover',\n      width: '100%',\n      height: '100%',\n      display: 'block'\n    }, ownerState.variant === 'standard' && {\n      height: 'auto',\n      flexGrow: 1\n    })\n  });\n});\nconst ImageListItem = /*#__PURE__*/React.forwardRef(function ImageListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiImageListItem'\n  });\n\n  // TODO: - Use jsdoc @default?: \"cols rows default values are for docs only\"\n  const {\n      children,\n      className,\n      cols = 1,\n      component = 'li',\n      rows = 1,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    rowHeight = 'auto',\n    gap,\n    variant\n  } = React.useContext(ImageListContext);\n  let height = 'auto';\n  if (variant === 'woven') {\n    height = undefined;\n  } else if (rowHeight !== 'auto') {\n    height = rowHeight * rows + gap * (rows - 1);\n  }\n  const ownerState = _extends({}, props, {\n    cols,\n    component,\n    gap,\n    rowHeight,\n    rows,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListItemRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: _extends({\n      height,\n      gridColumnEnd: variant !== 'masonry' ? \"span \".concat(cols) : undefined,\n      gridRowEnd: variant !== 'masonry' ? \"span \".concat(rows) : undefined,\n      marginBottom: variant === 'masonry' ? gap : undefined,\n      breakInside: variant === 'masonry' ? 'avoid' : undefined\n    }, style),\n    ownerState: ownerState\n  }, other, {\n    children: React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The ImageListItem component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      if (child.type === 'img' || isMuiElement(child, ['Image'])) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          className: clsx(classes.img, child.props.className)\n        });\n      }\n      return child;\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `<img>`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Width of the item in number of grid columns.\n   * @default 1\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Height of the item in number of grid rows.\n   * @default 1\n   */\n  rows: integerPropType,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ImageListItem;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "composeClasses", "integerPropType", "clsx", "PropTypes", "React", "isFragment", "ImageListContext", "styled", "useDefaultProps", "isMuiElement", "imageListItemClasses", "getImageListItemUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "variant", "slots", "root", "img", "ImageListItemRoot", "name", "slot", "overridesResolver", "props", "styles", "concat", "_ref", "display", "position", "flexDirection", "height", "alignSelf", "objectFit", "width", "flexGrow", "ImageListItem", "forwardRef", "inProps", "ref", "children", "className", "cols", "component", "rows", "style", "other", "rowHeight", "gap", "useContext", "undefined", "as", "gridColumnEnd", "gridRowEnd", "marginBottom", "breakInside", "Children", "map", "child", "isValidElement", "process", "env", "NODE_ENV", "console", "error", "join", "type", "cloneElement", "propTypes", "node", "object", "string", "elementType", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/ImageListItem/ImageListItem.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rows\", \"style\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport ImageListContext from '../ImageList/ImageListContext';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport isMuiElement from '../utils/isMuiElement';\nimport imageListItemClasses, { getImageListItemUtilityClass } from './imageListItemClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant],\n    img: ['img']\n  };\n  return composeClasses(slots, getImageListItemUtilityClass, classes);\n};\nconst ImageListItemRoot = styled('li', {\n  name: 'MuiImageListItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${imageListItemClasses.img}`]: styles.img\n    }, styles.root, styles[ownerState.variant]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'block',\n  position: 'relative'\n}, ownerState.variant === 'standard' && {\n  // For titlebar under list item\n  display: 'flex',\n  flexDirection: 'column'\n}, ownerState.variant === 'woven' && {\n  height: '100%',\n  alignSelf: 'center',\n  '&:nth-of-type(even)': {\n    height: '70%'\n  }\n}, {\n  [`& .${imageListItemClasses.img}`]: _extends({\n    objectFit: 'cover',\n    width: '100%',\n    height: '100%',\n    display: 'block'\n  }, ownerState.variant === 'standard' && {\n    height: 'auto',\n    flexGrow: 1\n  })\n}));\nconst ImageListItem = /*#__PURE__*/React.forwardRef(function ImageListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiImageListItem'\n  });\n\n  // TODO: - Use jsdoc @default?: \"cols rows default values are for docs only\"\n  const {\n      children,\n      className,\n      cols = 1,\n      component = 'li',\n      rows = 1,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    rowHeight = 'auto',\n    gap,\n    variant\n  } = React.useContext(ImageListContext);\n  let height = 'auto';\n  if (variant === 'woven') {\n    height = undefined;\n  } else if (rowHeight !== 'auto') {\n    height = rowHeight * rows + gap * (rows - 1);\n  }\n  const ownerState = _extends({}, props, {\n    cols,\n    component,\n    gap,\n    rowHeight,\n    rows,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListItemRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: _extends({\n      height,\n      gridColumnEnd: variant !== 'masonry' ? `span ${cols}` : undefined,\n      gridRowEnd: variant !== 'masonry' ? `span ${rows}` : undefined,\n      marginBottom: variant === 'masonry' ? gap : undefined,\n      breakInside: variant === 'masonry' ? 'avoid' : undefined\n    }, style),\n    ownerState: ownerState\n  }, other, {\n    children: React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The ImageListItem component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      if (child.type === 'img' || isMuiElement(child, ['Image'])) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          className: clsx(classes.img, child.props.className)\n        });\n      }\n      return child;\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `<img>`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Width of the item in number of grid columns.\n   * @default 1\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Height of the item in number of grid rows.\n   * @default 1\n   */\n  rows: integerPropType,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ImageListItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC;AACjF,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,oBAAoB,IAAIC,4BAA4B,QAAQ,wBAAwB;AAC3F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,OAAO,CAAC;IACvBG,GAAG,EAAE,CAAC,KAAK;EACb,CAAC;EACD,OAAOpB,cAAc,CAACkB,KAAK,EAAEP,4BAA4B,EAAEK,OAAO,CAAC;AACrE,CAAC;AACD,MAAMK,iBAAiB,GAAGd,MAAM,CAAC,IAAI,EAAE;EACrCe,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAAC;MACN,OAAAE,MAAA,CAAOjB,oBAAoB,CAACU,GAAG,IAAKM,MAAM,CAACN;IAC7C,CAAC,EAAEM,MAAM,CAACP,IAAI,EAAEO,MAAM,CAACX,UAAU,CAACE,OAAO,CAAC,CAAC;EAC7C;AACF,CAAC,CAAC,CAACW,IAAA;EAAA,IAAC;IACFb;EACF,CAAC,GAAAa,IAAA;EAAA,OAAK9B,QAAQ,CAAC;IACb+B,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE;EACZ,CAAC,EAAEf,UAAU,CAACE,OAAO,KAAK,UAAU,IAAI;IACtC;IACAY,OAAO,EAAE,MAAM;IACfE,aAAa,EAAE;EACjB,CAAC,EAAEhB,UAAU,CAACE,OAAO,KAAK,OAAO,IAAI;IACnCe,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,QAAQ;IACnB,qBAAqB,EAAE;MACrBD,MAAM,EAAE;IACV;EACF,CAAC,EAAE;IACD,OAAAL,MAAA,CAAOjB,oBAAoB,CAACU,GAAG,IAAKtB,QAAQ,CAAC;MAC3CoC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,MAAM;MACbH,MAAM,EAAE,MAAM;MACdH,OAAO,EAAE;IACX,CAAC,EAAEd,UAAU,CAACE,OAAO,KAAK,UAAU,IAAI;MACtCe,MAAM,EAAE,MAAM;MACdI,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,aAAa,GAAG,aAAajC,KAAK,CAACkC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMf,KAAK,GAAGjB,eAAe,CAAC;IAC5BiB,KAAK,EAAEc,OAAO;IACdjB,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM;MACFmB,QAAQ;MACRC,SAAS;MACTC,IAAI,GAAG,CAAC;MACRC,SAAS,GAAG,IAAI;MAChBC,IAAI,GAAG,CAAC;MACRC;IACF,CAAC,GAAGrB,KAAK;IACTsB,KAAK,GAAGlD,6BAA6B,CAAC4B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAM;IACJiD,SAAS,GAAG,MAAM;IAClBC,GAAG;IACHhC;EACF,CAAC,GAAGb,KAAK,CAAC8C,UAAU,CAAC5C,gBAAgB,CAAC;EACtC,IAAI0B,MAAM,GAAG,MAAM;EACnB,IAAIf,OAAO,KAAK,OAAO,EAAE;IACvBe,MAAM,GAAGmB,SAAS;EACpB,CAAC,MAAM,IAAIH,SAAS,KAAK,MAAM,EAAE;IAC/BhB,MAAM,GAAGgB,SAAS,GAAGH,IAAI,GAAGI,GAAG,IAAIJ,IAAI,GAAG,CAAC,CAAC;EAC9C;EACA,MAAM9B,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IACrCkB,IAAI;IACJC,SAAS;IACTK,GAAG;IACHD,SAAS;IACTH,IAAI;IACJ5B;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACQ,iBAAiB,EAAEvB,QAAQ,CAAC;IACnDsD,EAAE,EAAER,SAAS;IACbF,SAAS,EAAExC,IAAI,CAACc,OAAO,CAACG,IAAI,EAAEH,OAAO,CAACC,OAAO,CAAC,EAAEyB,SAAS,CAAC;IAC1DF,GAAG,EAAEA,GAAG;IACRM,KAAK,EAAEhD,QAAQ,CAAC;MACdkC,MAAM;MACNqB,aAAa,EAAEpC,OAAO,KAAK,SAAS,WAAAU,MAAA,CAAWgB,IAAI,IAAKQ,SAAS;MACjEG,UAAU,EAAErC,OAAO,KAAK,SAAS,WAAAU,MAAA,CAAWkB,IAAI,IAAKM,SAAS;MAC9DI,YAAY,EAAEtC,OAAO,KAAK,SAAS,GAAGgC,GAAG,GAAGE,SAAS;MACrDK,WAAW,EAAEvC,OAAO,KAAK,SAAS,GAAG,OAAO,GAAGkC;IACjD,CAAC,EAAEL,KAAK,CAAC;IACT/B,UAAU,EAAEA;EACd,CAAC,EAAEgC,KAAK,EAAE;IACRN,QAAQ,EAAErC,KAAK,CAACqD,QAAQ,CAACC,GAAG,CAACjB,QAAQ,EAAEkB,KAAK,IAAI;MAC9C,IAAI,EAAE,aAAavD,KAAK,CAACwD,cAAc,CAACD,KAAK,CAAC,EAAE;QAC9C,OAAO,IAAI;MACb;MACA,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI1D,UAAU,CAACsD,KAAK,CAAC,EAAE;UACrBK,OAAO,CAACC,KAAK,CAAC,CAAC,wEAAwE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9I;MACF;MACA,IAAIP,KAAK,CAACQ,IAAI,KAAK,KAAK,IAAI1D,YAAY,CAACkD,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE;QAC1D,OAAO,aAAavD,KAAK,CAACgE,YAAY,CAACT,KAAK,EAAE;UAC5CjB,SAAS,EAAExC,IAAI,CAACc,OAAO,CAACI,GAAG,EAAEuC,KAAK,CAAClC,KAAK,CAACiB,SAAS;QACpD,CAAC,CAAC;MACJ;MACA,OAAOiB,KAAK;IACd,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,aAAa,CAACgC,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACE5B,QAAQ,EAAEtC,SAAS,CAACmE,IAAI;EACxB;AACF;AACA;EACEtD,OAAO,EAAEb,SAAS,CAACoE,MAAM;EACzB;AACF;AACA;EACE7B,SAAS,EAAEvC,SAAS,CAACqE,MAAM;EAC3B;AACF;AACA;AACA;EACE7B,IAAI,EAAE1C,eAAe;EACrB;AACF;AACA;AACA;EACE2C,SAAS,EAAEzC,SAAS,CAACsE,WAAW;EAChC;AACF;AACA;AACA;EACE5B,IAAI,EAAE5C,eAAe;EACrB;AACF;AACA;EACE6C,KAAK,EAAE3C,SAAS,CAACoE,MAAM;EACvB;AACF;AACA;EACEG,EAAE,EAAEvE,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAACyE,OAAO,CAACzE,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAAC0E,IAAI,EAAE1E,SAAS,CAACoE,MAAM,EAAEpE,SAAS,CAAC2E,IAAI,CAAC,CAAC,CAAC,EAAE3E,SAAS,CAAC0E,IAAI,EAAE1E,SAAS,CAACoE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}