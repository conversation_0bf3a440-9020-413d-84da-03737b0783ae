{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\n\n// https://github.com/sindresorhus/is-plain-obj/blob/main/index.js\nexport function isPlainObject(item) {\n  if (typeof item !== 'object' || item === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(item);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);\n}\nfunction deepClone(source) {\n  if (/*#__PURE__*/React.isValidElement(source) || !isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\nexport default function deepmerge(target, source) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    clone: true\n  };\n  const output = options.clone ? _extends({}, target) : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (/*#__PURE__*/React.isValidElement(source[key])) {\n        output[key] = source[key];\n      } else if (isPlainObject(source[key]) &&\n      // Avoid prototype pollution\n      Object.prototype.hasOwnProperty.call(target, key) && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}", "map": {"version": 3, "names": ["_extends", "React", "isPlainObject", "item", "prototype", "Object", "getPrototypeOf", "Symbol", "toStringTag", "iterator", "deepClone", "source", "isValidElement", "output", "keys", "for<PERSON>ach", "key", "deepmerge", "target", "options", "arguments", "length", "undefined", "clone", "hasOwnProperty", "call"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/utils/esm/deepmerge/deepmerge.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\n\n// https://github.com/sindresorhus/is-plain-obj/blob/main/index.js\nexport function isPlainObject(item) {\n  if (typeof item !== 'object' || item === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(item);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);\n}\nfunction deepClone(source) {\n  if ( /*#__PURE__*/React.isValidElement(source) || !isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\nexport default function deepmerge(target, source, options = {\n  clone: true\n}) {\n  const output = options.clone ? _extends({}, target) : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if ( /*#__PURE__*/React.isValidElement(source[key])) {\n        output[key] = source[key];\n      } else if (isPlainObject(source[key]) &&\n      // Avoid prototype pollution\n      Object.prototype.hasOwnProperty.call(target, key) && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC7C,OAAO,KAAK;EACd;EACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,cAAc,CAACH,IAAI,CAAC;EAC7C,OAAO,CAACC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKC,MAAM,CAACD,SAAS,IAAIC,MAAM,CAACC,cAAc,CAACF,SAAS,CAAC,KAAK,IAAI,KAAK,EAAEG,MAAM,CAACC,WAAW,IAAIL,IAAI,CAAC,IAAI,EAAEI,MAAM,CAACE,QAAQ,IAAIN,IAAI,CAAC;AAC3K;AACA,SAASO,SAASA,CAACC,MAAM,EAAE;EACzB,IAAK,aAAaV,KAAK,CAACW,cAAc,CAACD,MAAM,CAAC,IAAI,CAACT,aAAa,CAACS,MAAM,CAAC,EAAE;IACxE,OAAOA,MAAM;EACf;EACA,MAAME,MAAM,GAAG,CAAC,CAAC;EACjBR,MAAM,CAACS,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IACjCH,MAAM,CAACG,GAAG,CAAC,GAAGN,SAAS,CAACC,MAAM,CAACK,GAAG,CAAC,CAAC;EACtC,CAAC,CAAC;EACF,OAAOH,MAAM;AACf;AACA,eAAe,SAASI,SAASA,CAACC,MAAM,EAAEP,MAAM,EAE7C;EAAA,IAF+CQ,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IAC1DG,KAAK,EAAE;EACT,CAAC;EACC,MAAMV,MAAM,GAAGM,OAAO,CAACI,KAAK,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,MAAM,CAAC,GAAGA,MAAM;EAC5D,IAAIhB,aAAa,CAACgB,MAAM,CAAC,IAAIhB,aAAa,CAACS,MAAM,CAAC,EAAE;IAClDN,MAAM,CAACS,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;MACjC,IAAK,aAAaf,KAAK,CAACW,cAAc,CAACD,MAAM,CAACK,GAAG,CAAC,CAAC,EAAE;QACnDH,MAAM,CAACG,GAAG,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;MAC3B,CAAC,MAAM,IAAId,aAAa,CAACS,MAAM,CAACK,GAAG,CAAC,CAAC;MACrC;MACAX,MAAM,CAACD,SAAS,CAACoB,cAAc,CAACC,IAAI,CAACP,MAAM,EAAEF,GAAG,CAAC,IAAId,aAAa,CAACgB,MAAM,CAACF,GAAG,CAAC,CAAC,EAAE;QAC/E;QACAH,MAAM,CAACG,GAAG,CAAC,GAAGC,SAAS,CAACC,MAAM,CAACF,GAAG,CAAC,EAAEL,MAAM,CAACK,GAAG,CAAC,EAAEG,OAAO,CAAC;MAC5D,CAAC,MAAM,IAAIA,OAAO,CAACI,KAAK,EAAE;QACxBV,MAAM,CAACG,GAAG,CAAC,GAAGd,aAAa,CAACS,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGN,SAAS,CAACC,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;MACjF,CAAC,MAAM;QACLH,MAAM,CAACG,GAAG,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ;EACA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}