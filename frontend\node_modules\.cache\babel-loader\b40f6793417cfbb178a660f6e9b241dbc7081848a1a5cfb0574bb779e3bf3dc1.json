{"ast": null, "code": "import React from'react';import{Card,CardContent,Typography,Grid}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AccountInfo=_ref=>{let{accountData}=_ref;return/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Account Information\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"Portfolio Value\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"$\",(accountData===null||accountData===void 0?void 0:accountData.portfolio_value)||'0.00']})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"Buying Power\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"$\",(accountData===null||accountData===void 0?void 0:accountData.buying_power)||'0.00']})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"Day Trade Count\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:(accountData===null||accountData===void 0?void 0:accountData.daytrade_count)||0})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"Status\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:(accountData===null||accountData===void 0?void 0:accountData.status)||'ACTIVE'})]})]})]})});};export default AccountInfo;", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grid", "jsx", "_jsx", "jsxs", "_jsxs", "AccountInfo", "_ref", "accountData", "children", "variant", "gutterBottom", "container", "spacing", "item", "xs", "color", "portfolio_value", "buying_power", "daytrade_count", "status"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AccountInfo.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography, Grid } from '@mui/material';\n\nconst AccountInfo = ({ accountData }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Account Information\n        </Typography>\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Portfolio Value\n            </Typography>\n            <Typography variant=\"h6\">\n              ${accountData?.portfolio_value || '0.00'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Buying Power\n            </Typography>\n            <Typography variant=\"h6\">\n              ${accountData?.buying_power || '0.00'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Day Trade Count\n            </Typography>\n            <Typography variant=\"h6\">\n              {accountData?.daytrade_count || 0}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Status\n            </Typography>\n            <Typography variant=\"h6\">\n              {accountData?.status || 'ACTIVE'}\n            </Typography>\n          </Grid>\n        </Grid>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AccountInfo;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,CAAEC,UAAU,CAAEC,IAAI,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpE,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAqB,IAApB,CAAEC,WAAY,CAAC,CAAAD,IAAA,CAClC,mBACEJ,IAAA,CAACL,IAAI,EAAAW,QAAA,cACHJ,KAAA,CAACN,WAAW,EAAAU,QAAA,eACVN,IAAA,CAACH,UAAU,EAACU,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,qBAEtC,CAAY,CAAC,cACbJ,KAAA,CAACJ,IAAI,EAACW,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAJ,QAAA,eACzBJ,KAAA,CAACJ,IAAI,EAACa,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACfN,IAAA,CAACH,UAAU,EAACU,OAAO,CAAC,OAAO,CAACM,KAAK,CAAC,eAAe,CAAAP,QAAA,CAAC,iBAElD,CAAY,CAAC,cACbJ,KAAA,CAACL,UAAU,EAACU,OAAO,CAAC,IAAI,CAAAD,QAAA,EAAC,GACtB,CAAC,CAAAD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAES,eAAe,GAAI,MAAM,EAC9B,CAAC,EACT,CAAC,cACPZ,KAAA,CAACJ,IAAI,EAACa,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACfN,IAAA,CAACH,UAAU,EAACU,OAAO,CAAC,OAAO,CAACM,KAAK,CAAC,eAAe,CAAAP,QAAA,CAAC,cAElD,CAAY,CAAC,cACbJ,KAAA,CAACL,UAAU,EAACU,OAAO,CAAC,IAAI,CAAAD,QAAA,EAAC,GACtB,CAAC,CAAAD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEU,YAAY,GAAI,MAAM,EAC3B,CAAC,EACT,CAAC,cACPb,KAAA,CAACJ,IAAI,EAACa,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACfN,IAAA,CAACH,UAAU,EAACU,OAAO,CAAC,OAAO,CAACM,KAAK,CAAC,eAAe,CAAAP,QAAA,CAAC,iBAElD,CAAY,CAAC,cACbN,IAAA,CAACH,UAAU,EAACU,OAAO,CAAC,IAAI,CAAAD,QAAA,CACrB,CAAAD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEW,cAAc,GAAI,CAAC,CACvB,CAAC,EACT,CAAC,cACPd,KAAA,CAACJ,IAAI,EAACa,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACfN,IAAA,CAACH,UAAU,EAACU,OAAO,CAAC,OAAO,CAACM,KAAK,CAAC,eAAe,CAAAP,QAAA,CAAC,QAElD,CAAY,CAAC,cACbN,IAAA,CAACH,UAAU,EAACU,OAAO,CAAC,IAAI,CAAAD,QAAA,CACrB,CAAAD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEY,MAAM,GAAI,QAAQ,CACtB,CAAC,EACT,CAAC,EACH,CAAC,EACI,CAAC,CACV,CAAC,CAEX,CAAC,CAED,cAAe,CAAAd,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}