{"ast": null, "code": "import React from'react';import{Card,CardContent,Typography,List,ListItem,ListItemText}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SignalsList=_ref=>{let{signals}=_ref;return/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Trading Signals\"}),/*#__PURE__*/_jsx(List,{children:(signals===null||signals===void 0?void 0:signals.length)>0?signals.map((signal,index)=>/*#__PURE__*/_jsx(ListItem,{children:/*#__PURE__*/_jsx(ListItemText,{primary:\"\".concat(signal.symbol,\" - \").concat(signal.action),secondary:\"Price: $\".concat(signal.price,\" | Confidence: \").concat(signal.confidence,\"%\")})},index)):/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"No signals available\"})})]})});};export default SignalsList;", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "List", "ListItem", "ListItemText", "jsx", "_jsx", "jsxs", "_jsxs", "SignalsList", "_ref", "signals", "children", "variant", "gutterBottom", "length", "map", "signal", "index", "primary", "concat", "symbol", "action", "secondary", "price", "confidence", "color"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/SignalsList.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';\n\nconst SignalsList = ({ signals }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Trading Signals\n        </Typography>\n        <List>\n          {signals?.length > 0 ? (\n            signals.map((signal, index) => (\n              <ListItem key={index}>\n                <ListItemText\n                  primary={`${signal.symbol} - ${signal.action}`}\n                  secondary={`Price: $${signal.price} | Confidence: ${signal.confidence}%`}\n                />\n              </ListItem>\n            ))\n          ) : (\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              No signals available\n            </Typography>\n          )}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default SignalsList;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,CAAEC,UAAU,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,YAAY,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5F,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CAC9B,mBACEJ,IAAA,CAACP,IAAI,EAAAa,QAAA,cACHJ,KAAA,CAACR,WAAW,EAAAY,QAAA,eACVN,IAAA,CAACL,UAAU,EAACY,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,iBAEtC,CAAY,CAAC,cACbN,IAAA,CAACJ,IAAI,EAAAU,QAAA,CACF,CAAAD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEI,MAAM,EAAG,CAAC,CAClBJ,OAAO,CAACK,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBACxBZ,IAAA,CAACH,QAAQ,EAAAS,QAAA,cACPN,IAAA,CAACF,YAAY,EACXe,OAAO,IAAAC,MAAA,CAAKH,MAAM,CAACI,MAAM,QAAAD,MAAA,CAAMH,MAAM,CAACK,MAAM,CAAG,CAC/CC,SAAS,YAAAH,MAAA,CAAaH,MAAM,CAACO,KAAK,oBAAAJ,MAAA,CAAkBH,MAAM,CAACQ,UAAU,KAAI,CAC1E,CAAC,EAJWP,KAKL,CACX,CAAC,cAEFZ,IAAA,CAACL,UAAU,EAACY,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,eAAe,CAAAd,QAAA,CAAC,sBAElD,CAAY,CACb,CACG,CAAC,EACI,CAAC,CACV,CAAC,CAEX,CAAC,CAED,cAAe,CAAAH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}