#!/usr/bin/env python3
"""
Simple A.T.L.A.S. Test Script
"""

import sys
import os

# Add streamlined directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlined'))

def test_imports():
    """Test if all our modules can be imported"""
    print("🧪 Testing A.T.L.A.S. Implementation...")
    print("="*50)
    
    tests = []
    
    # Test 1: Server import
    try:
        from atlas_server import app
        print("✅ Atlas Server: Import successful")
        tests.append(True)
    except Exception as e:
        print(f"❌ Atlas Server: Import failed - {e}")
        tests.append(False)
    
    # Test 2: Predicto Engine
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        predicto = PredictoConversationalEngine()
        print("✅ Predicto Engine: Import successful")
        tests.append(True)
    except Exception as e:
        print(f"❌ Predicto Engine: Import failed - {e}")
        tests.append(False)
    
    # Test 3: Market Engine
    try:
        from atlas_market_engine import AtlasMarketEngine
        market = AtlasMarketEngine()
        print("✅ Market Engine: Import successful")
        tests.append(True)
    except Exception as e:
        print(f"❌ Market Engine: Import failed - {e}")
        tests.append(False)
    
    # Test 4: Orchestrator
    try:
        from atlas_orchestrator import AtlasOrchestrator
        orchestrator = AtlasOrchestrator()
        print("✅ Orchestrator: Import successful")
        tests.append(True)
    except Exception as e:
        print(f"❌ Orchestrator: Import failed - {e}")
        tests.append(False)
    
    # Test 5: Check key methods
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        predicto = PredictoConversationalEngine()
        
        key_methods = [
            '_process_pattern_scanner_request',
            '_execute_ttm_squeeze_scan',
            '_execute_trade_request',
            '_manage_alerts'
        ]
        
        missing = []
        for method in key_methods:
            if not hasattr(predicto, method):
                missing.append(method)
        
        if not missing:
            print("✅ Key Methods: All implemented")
            tests.append(True)
        else:
            print(f"❌ Key Methods: Missing {missing}")
            tests.append(False)
            
    except Exception as e:
        print(f"❌ Key Methods: Check failed - {e}")
        tests.append(False)
    
    # Test 6: Frontend files
    frontend_files = [
        "frontend/src/components/AtlasInterface.js",
        "frontend/src/App.js"
    ]
    
    frontend_exists = all(os.path.exists(f) for f in frontend_files)
    if frontend_exists:
        print("✅ Frontend: Files exist")
        tests.append(True)
    else:
        print("❌ Frontend: Some files missing")
        tests.append(False)
    
    # Summary
    passed = sum(tests)
    total = len(tests)
    
    print("\n" + "="*50)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ A.T.L.A.S. implementation is ready!")
    else:
        print("⚠️  Some issues found, but core functionality should work")
    
    return passed == total

if __name__ == "__main__":
    success = test_imports()
    
    if success:
        print("\n🚀 Ready to start A.T.L.A.S.!")
        print("💡 To run the system:")
        print("   1. Backend: python main.py")
        print("   2. Frontend: cd frontend && npm start")
        print("   3. Open: http://localhost:3000")
    else:
        print("\n🔧 Some components need attention before running")
