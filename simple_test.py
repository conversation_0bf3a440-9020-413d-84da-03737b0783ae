#!/usr/bin/env python3
"""
Simple A.T.L.A.S. Test Script
"""

import requests
import json

def test_atlas():
    print("🔗 Testing A.T.L.A.S. server at: http://localhost:8080")
    
    # Test health endpoint
    try:
        health_response = requests.get("http://localhost:8080/api/v1/health", timeout=5)
        print(f"Health check: {health_response.status_code}")
        if health_response.status_code == 200:
            print("✅ Server is healthy")
        else:
            print("❌ Server health check failed")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Test a simple chat request
    test_prompts = [
        "What's Apple trading at right now?",
        "Are there any stocks that look like they might jump soon?",
        "I want to make $100 by tomorrow—what trade should I place?"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n[{i}] Testing: \"{prompt}\"")
        
        try:
            response = requests.post(
                "http://localhost:8080/api/v1/chat",
                json={
                    "message": prompt,
                    "session_id": f"test_{i}",
                    "context": {"test_mode": True}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get("response", "")
                print(f"✅ Response received ({len(response_text)} chars)")
                print(f"Preview: {response_text[:200]}...")
                
                # Check for key indicators
                has_atlas = "a.t.l.a.s" in response_text.lower()
                has_predicto = "predicto" in response_text.lower()
                has_prices = "$" in response_text
                has_trading = any(word in response_text.lower() for word in ["buy", "sell", "trade", "target"])
                
                print(f"   A.T.L.A.S branding: {'✅' if has_atlas else '❌'}")
                print(f"   Predicto branding: {'✅' if has_predicto else '❌'}")
                print(f"   Price data: {'✅' if has_prices else '❌'}")
                print(f"   Trading terms: {'✅' if has_trading else '❌'}")
                
            else:
                print(f"❌ HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_atlas()
