{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst AlignHorizontalDistributeCenter = createLucideIcon(\"AlignHorizontalDistributeCenter\", [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"4\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"1wwnby\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"14\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1fe6j6\"\n}], [\"path\", {\n  d: \"M17 22v-5\",\n  key: \"4b6g73\"\n}], [\"path\", {\n  d: \"M17 7V2\",\n  key: \"hnrr36\"\n}], [\"path\", {\n  d: \"M7 22v-3\",\n  key: \"1r4jpn\"\n}], [\"path\", {\n  d: \"M7 5V2\",\n  key: \"liy1u9\"\n}]]);\nexport { AlignHorizontalDistributeCenter as default };", "map": {"version": 3, "names": ["AlignHorizontalDistributeCenter", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\align-horizontal-distribute-center.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignHorizontalDistributeCenter\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNCIgeD0iNCIgeT0iNSIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iMTAiIHg9IjE0IiB5PSI3IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMTcgMjJ2LTUiIC8+CiAgPHBhdGggZD0iTTE3IDdWMiIgLz4KICA8cGF0aCBkPSJNNyAyMnYtMyIgLz4KICA8cGF0aCBkPSJNNyA1VjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/align-horizontal-distribute-center\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignHorizontalDistributeCenter = createLucideIcon('AlignHorizontalDistributeCenter', [\n  ['rect', { width: '6', height: '14', x: '4', y: '5', rx: '2', key: '1wwnby' }],\n  ['rect', { width: '6', height: '10', x: '14', y: '7', rx: '2', key: '1fe6j6' }],\n  ['path', { d: 'M17 22v-5', key: '4b6g73' }],\n  ['path', { d: 'M17 7V2', key: 'hnrr36' }],\n  ['path', { d: 'M7 22v-3', key: '1r4jpn' }],\n  ['path', { d: 'M7 5V2', key: 'liy1u9' }],\n]);\n\nexport default AlignHorizontalDistributeCenter;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,+BAAA,GAAkCC,gBAAA,CAAiB,iCAAmC,GAC1F,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}