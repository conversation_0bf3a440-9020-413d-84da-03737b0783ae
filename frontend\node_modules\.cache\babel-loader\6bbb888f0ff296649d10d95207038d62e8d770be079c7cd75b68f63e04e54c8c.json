{"ast": null, "code": "'use client';\n\nexport { default } from './Zoom';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Zoom/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Zoom';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}