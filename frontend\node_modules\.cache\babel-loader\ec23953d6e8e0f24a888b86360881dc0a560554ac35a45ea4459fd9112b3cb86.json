{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m2.47 10.42 3.07 9.22c.28.81 1.04 1.36 1.9 1.36h9.12c.86 0 1.63-.55 1.9-1.37l3.07-9.22c.28-.84-.03-1.76-.75-2.27L13.15 2.8c-.69-.48-1.61-.48-2.29 0L3.22 8.14c-.72.51-1.03 1.44-.75 2.28\"\n}), 'PentagonRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/PentagonRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m2.47 10.42 3.07 9.22c.28.81 1.04 1.36 1.9 1.36h9.12c.86 0 1.63-.55 1.9-1.37l3.07-9.22c.28-.84-.03-1.76-.75-2.27L13.15 2.8c-.69-.48-1.61-.48-2.29 0L3.22 8.14c-.72.51-1.03 1.44-.75 2.28\"\n}), 'PentagonRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}