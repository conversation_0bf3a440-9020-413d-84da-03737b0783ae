# 1. Main Chat Engine

This folder contains the core conversational AI components that power A.T.L.A.S.

## Files:
- `atlas_predicto_engine.py` - Primary conversational AI interface with Stock Market God 6-point format
- `atlas_ai_engine.py` - Core AI processing engine
- `atlas_conversation_flow_manager.py` - Manages conversation state and flow
- `atlas_orchestrator.py` - Coordinates all system components
- `atlas_server.py` - Web server and API endpoints
- `atlas_unified_access_layer.py` - Unified interface to all capabilities
- `atlas_interface.html` - Web interface
- `atlas_trading_god_demo.py` - Demo interface
- `start_production.py` - Production startup script

## Purpose:
These files handle all user interactions, conversation management, and coordinate access to the trading system's 25+ capabilities through natural language.
