{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 6c0 .55.45 1 1 1h1.25l1.45 1.3L11 11l-9.12-.96c-1-.11-1.88.68-1.88 1.69 0 .75.49 1.41 1.21 1.63l3.33 1-3.49 1.88C-.77 17.22-.07 20 2 20h6c2.21 0 4-1.79 4-4h4l2 2h-2c-.55 0-1 .45-1 1s.45 1 1 1h5c1.13 0 2.11-.62 2.63-1.55.36-.65-.15-1.45-.9-1.45-.34 0-.68.16-.84.47-.17.31-.51.53-.89.53h-.17l-2.2-2.2C20.58 15.37 22 14.4 22 13c0-.89-7.72-7.75-7.72-7.75-.18-.16-.41-.25-.66-.25H12c-.55 0-1 .45-1 1M8 18H2l5.25-2.83L10 16c0 1.1-.89 2-2 2\"\n}), 'SnowmobileRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/SnowmobileRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 6c0 .55.45 1 1 1h1.25l1.45 1.3L11 11l-9.12-.96c-1-.11-1.88.68-1.88 1.69 0 .75.49 1.41 1.21 1.63l3.33 1-3.49 1.88C-.77 17.22-.07 20 2 20h6c2.21 0 4-1.79 4-4h4l2 2h-2c-.55 0-1 .45-1 1s.45 1 1 1h5c1.13 0 2.11-.62 2.63-1.55.36-.65-.15-1.45-.9-1.45-.34 0-.68.16-.84.47-.17.31-.51.53-.89.53h-.17l-2.2-2.2C20.58 15.37 22 14.4 22 13c0-.89-7.72-7.75-7.72-7.75-.18-.16-.41-.25-.66-.25H12c-.55 0-1 .45-1 1M8 18H2l5.25-2.83L10 16c0 1.1-.89 2-2 2\"\n}), 'SnowmobileRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}