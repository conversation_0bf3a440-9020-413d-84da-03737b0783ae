{"ast": null, "code": "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "map": {"version": 3, "names": ["getBoundingClientRect", "getLayoutRect", "element", "clientRect", "width", "offsetWidth", "height", "offsetHeight", "Math", "abs", "x", "offsetLeft", "y", "offsetTop"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js"], "sourcesContent": ["import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,4BAA4B,CAAC,CAAC;AAChE;;AAEA,eAAe,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC7C,IAAIC,UAAU,GAAGH,qBAAqB,CAACE,OAAO,CAAC,CAAC,CAAC;EACjD;;EAEA,IAAIE,KAAK,GAAGF,OAAO,CAACG,WAAW;EAC/B,IAAIC,MAAM,GAAGJ,OAAO,CAACK,YAAY;EAEjC,IAAIC,IAAI,CAACC,GAAG,CAACN,UAAU,CAACC,KAAK,GAAGA,KAAK,CAAC,IAAI,CAAC,EAAE;IAC3CA,KAAK,GAAGD,UAAU,CAACC,KAAK;EAC1B;EAEA,IAAII,IAAI,CAACC,GAAG,CAACN,UAAU,CAACG,MAAM,GAAGA,MAAM,CAAC,IAAI,CAAC,EAAE;IAC7CA,MAAM,GAAGH,UAAU,CAACG,MAAM;EAC5B;EAEA,OAAO;IACLI,CAAC,EAAER,OAAO,CAACS,UAAU;IACrBC,CAAC,EAAEV,OAAO,CAACW,SAAS;IACpBT,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}