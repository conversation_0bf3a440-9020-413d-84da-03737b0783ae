{"ast": null, "code": "// Corresponds to 10 frames at 60 Hz.\n// A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\nexport default function debounce(func, wait = 166) {\n  let timeout;\n  function debounced(...args) {\n    const later = () => {\n      // @ts-ignore\n      func.apply(this, args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  }\n  debounced.clear = () => {\n    clearTimeout(timeout);\n  };\n  return debounced;\n}", "map": {"version": 3, "names": ["debounce", "func", "wait", "timeout", "debounced", "args", "later", "apply", "clearTimeout", "setTimeout", "clear"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/utils/esm/debounce/debounce.js"], "sourcesContent": ["// Corresponds to 10 frames at 60 Hz.\n// A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\nexport default function debounce(func, wait = 166) {\n  let timeout;\n  function debounced(...args) {\n    const later = () => {\n      // @ts-ignore\n      func.apply(this, args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  }\n  debounced.clear = () => {\n    clearTimeout(timeout);\n  };\n  return debounced;\n}"], "mappings": "AAAA;AACA;AACA,eAAe,SAASA,QAAQA,CAACC,IAAI,EAAEC,IAAI,GAAG,GAAG,EAAE;EACjD,IAAIC,OAAO;EACX,SAASC,SAASA,CAAC,GAAGC,IAAI,EAAE;IAC1B,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClB;MACAL,IAAI,CAACM,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IACxB,CAAC;IACDG,YAAY,CAACL,OAAO,CAAC;IACrBA,OAAO,GAAGM,UAAU,CAACH,KAAK,EAAEJ,IAAI,CAAC;EACnC;EACAE,SAAS,CAACM,KAAK,GAAG,MAAM;IACtBF,YAAY,CAACL,OAAO,CAAC;EACvB,CAAC;EACD,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}