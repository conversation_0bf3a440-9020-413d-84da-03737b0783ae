{"ast": null, "code": "'use client';\n\nexport { default } from './Drawer';\nexport { default as drawerClasses } from './drawerClasses';\nexport * from './drawerClasses';", "map": {"version": 3, "names": ["default", "drawerClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Drawer/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Drawer';\nexport { default as drawerClasses } from './drawerClasses';\nexport * from './drawerClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}