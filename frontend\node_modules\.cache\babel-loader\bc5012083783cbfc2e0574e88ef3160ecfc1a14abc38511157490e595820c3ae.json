{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Upload = createLucideIcon(\"Upload\", [[\"path\", {\n  d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n  key: \"ih7n3h\"\n}], [\"polyline\", {\n  points: \"17 8 12 3 7 8\",\n  key: \"t8dd8p\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"3\",\n  y2: \"15\",\n  key: \"widbto\"\n}]]);\nexport { Upload as default };", "map": {"version": 3, "names": ["Upload", "createLucideIcon", "d", "key", "points", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\upload.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNyA4IDEyIDMgNyA4IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMyIgeTI9IjE1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('Upload', [\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['polyline', { points: '17 8 12 3 7 8', key: 't8dd8p' }],\n  ['line', { x1: '12', x2: '12', y1: '3', y2: '15', key: 'widbto' }],\n]);\n\nexport default Upload;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,UAAY;EAAEC,MAAA,EAAQ,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEE,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAL,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}