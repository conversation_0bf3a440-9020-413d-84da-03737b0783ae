{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\PositionsList.js\";\nimport React from 'react';\nimport { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PositionsList = ({\n  positions\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Current Positions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        children: (positions === null || positions === void 0 ? void 0 : positions.length) > 0 ? positions.map((position, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          children: /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: `${position.symbol} - ${position.qty} shares`,\n            secondary: `Value: $${position.market_value} | P&L: ${position.unrealized_pl}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"No positions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = PositionsList;\nexport default PositionsList;\nvar _c;\n$RefreshReg$(_c, \"PositionsList\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "List", "ListItem", "ListItemText", "jsxDEV", "_jsxDEV", "PositionsList", "positions", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "position", "index", "primary", "symbol", "qty", "secondary", "market_value", "unrealized_pl", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/PositionsList.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';\n\nconst PositionsList = ({ positions }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Current Positions\n        </Typography>\n        <List>\n          {positions?.length > 0 ? (\n            positions.map((position, index) => (\n              <ListItem key={index}>\n                <ListItemText\n                  primary={`${position.symbol} - ${position.qty} shares`}\n                  secondary={`Value: $${position.market_value} | P&L: ${position.unrealized_pl}`}\n                />\n              </ListItem>\n            ))\n          ) : (\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              No positions\n            </Typography>\n          )}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default PositionsList;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5F,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EACvC,oBACEF,OAAA,CAACP,IAAI;IAAAU,QAAA,eACHH,OAAA,CAACN,WAAW;MAAAS,QAAA,gBACVH,OAAA,CAACL,UAAU;QAACS,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbT,OAAA,CAACJ,IAAI;QAAAO,QAAA,EACF,CAAAD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,MAAM,IAAG,CAAC,GACpBR,SAAS,CAACS,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC5Bb,OAAA,CAACH,QAAQ;UAAAM,QAAA,eACPH,OAAA,CAACF,YAAY;YACXgB,OAAO,EAAE,GAAGF,QAAQ,CAACG,MAAM,MAAMH,QAAQ,CAACI,GAAG,SAAU;YACvDC,SAAS,EAAE,WAAWL,QAAQ,CAACM,YAAY,WAAWN,QAAQ,CAACO,aAAa;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF;QAAC,GAJWI,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACX,CAAC,gBAEFT,OAAA,CAACL,UAAU;UAACS,OAAO,EAAC,OAAO;UAACgB,KAAK,EAAC,eAAe;UAAAjB,QAAA,EAAC;QAElD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACY,EAAA,GA1BIpB,aAAa;AA4BnB,eAAeA,aAAa;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}