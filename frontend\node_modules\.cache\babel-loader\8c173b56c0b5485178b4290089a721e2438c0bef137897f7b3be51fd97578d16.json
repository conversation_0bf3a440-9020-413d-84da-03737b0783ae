{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\AIFeatures.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './AIFeatures.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AIFeatures = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('regime');\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState({});\n  const [error, setError] = useState(null);\n\n  // Form states\n  const [regimeSymbols, setRegimeSymbols] = useState('SPY,QQQ,VIX');\n  const [sentimentSymbol, setSentimentSymbol] = useState('AAPL');\n  const [sentimentHours, setSentimentHours] = useState(24);\n  const [embeddingSymbol, setEmbeddingSymbol] = useState('AAPL');\n  const [embeddingContext, setEmbeddingContext] = useState('SPY,QQQ,VIX,TLT');\n  const [ttmSymbols, setTtmSymbols] = useState('AAPL,TSLA,NVDA');\n  const [ttmTimeframe, setTtmTimeframe] = useState('5min');\n  const [newsQuery, setNewsQuery] = useState('market news today');\n  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n  const analyzeRegime = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/regime?symbols=${regimeSymbols}`);\n      const data = await response.json();\n      setResults(prev => ({\n        ...prev,\n        regime: data\n      }));\n    } catch (err) {\n      setError('Failed to analyze market regime');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const analyzeSentiment = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/sentiment/${sentimentSymbol}?hours_back=${sentimentHours}`);\n      const data = await response.json();\n      setResults(prev => ({\n        ...prev,\n        sentiment: data\n      }));\n    } catch (err) {\n      setError('Failed to analyze sentiment');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateEmbeddings = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/embeddings/${embeddingSymbol}?context=${embeddingContext}`);\n      const data = await response.json();\n      setResults(prev => ({\n        ...prev,\n        embeddings: data\n      }));\n    } catch (err) {\n      setError('Failed to generate embeddings');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const scanTTM = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/ttm-scan?symbols=${ttmSymbols}&timeframe=${ttmTimeframe}`);\n      const data = await response.json();\n      setResults(prev => ({\n        ...prev,\n        ttm: data\n      }));\n    } catch (err) {\n      setError('Failed to scan TTM signals');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const searchNews = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/news-search?query=${encodeURIComponent(newsQuery)}`);\n      const data = await response.json();\n      setResults(prev => ({\n        ...prev,\n        news: data\n      }));\n    } catch (err) {\n      setError('Failed to search news');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderRegimeTab = () => {\n    var _results$regime$regim;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-tab-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83C\\uDF0A Market Regime Detection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Analyze current market conditions and regime classification\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Symbols to Analyze:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: regimeSymbols,\n          onChange: e => setRegimeSymbols(e.target.value),\n          placeholder: \"SPY,QQQ,VIX\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: analyzeRegime,\n        disabled: loading,\n        className: \"ai-button\",\n        children: loading ? 'Analyzing...' : 'Analyze Market Regime'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 7\n      }, this), results.regime && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Regime Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"regime-summary\",\n          children: ((_results$regime$regim = results.regime.regime_analysis) === null || _results$regime$regim === void 0 ? void 0 : _results$regime$regim.market_regime_analysis) && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"regime-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Current Regime:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), \" \", results.regime.regime_analysis.market_regime_analysis.current_regime]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"regime-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Confidence:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this), \" \", (results.regime.regime_analysis.market_regime_analysis.confidence * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"regime-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Duration:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), \" \", results.regime.regime_analysis.market_regime_analysis.regime_duration_days, \" days\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"holly-interpretation\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Holly's Analysis:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: results.regime.holly_interpretation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 5\n    }, this);\n  };\n  const renderSentimentTab = () => {\n    var _results$sentiment$se;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-tab-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCF1 Social Sentiment Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Analyze crowd mood from social media platforms\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Symbol:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: sentimentSymbol,\n          onChange: e => setSentimentSymbol(e.target.value),\n          placeholder: \"AAPL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Hours Back:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: sentimentHours,\n          onChange: e => setSentimentHours(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 6,\n            children: \"6 hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 12,\n            children: \"12 hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 24,\n            children: \"24 hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 48,\n            children: \"48 hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: analyzeSentiment,\n        disabled: loading,\n        className: \"ai-button\",\n        children: loading ? 'Analyzing...' : 'Analyze Sentiment'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 7\n      }, this), results.sentiment && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Sentiment Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sentiment-summary\",\n          children: ((_results$sentiment$se = results.sentiment.sentiment_analysis) === null || _results$sentiment$se === void 0 ? void 0 : _results$sentiment$se.analysis_summary) && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sentiment-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Overall Sentiment:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this), \" \", results.sentiment.sentiment_analysis.analysis_summary.overall_sentiment]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sentiment-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Crowd Mood:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), \" \", results.sentiment.sentiment_analysis.analysis_summary.crowd_mood]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sentiment-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Posts Analyzed:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), \" \", results.sentiment.sentiment_analysis.analysis_summary.total_posts_analyzed]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"holly-interpretation\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Holly's Insights:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: results.sentiment.holly_insights\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 5\n    }, this);\n  };\n  const renderEmbeddingsTab = () => {\n    var _results$embeddings$e;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-tab-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83E\\uDDE0 Market Embeddings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Advanced pattern matching and market context analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Primary Symbol:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: embeddingSymbol,\n          onChange: e => setEmbeddingSymbol(e.target.value),\n          placeholder: \"AAPL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Context Symbols:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: embeddingContext,\n          onChange: e => setEmbeddingContext(e.target.value),\n          placeholder: \"SPY,QQQ,VIX,TLT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: generateEmbeddings,\n        disabled: loading,\n        className: \"ai-button\",\n        children: loading ? 'Generating...' : 'Generate Embeddings'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 7\n      }, this), results.embeddings && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Embedding Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"embeddings-summary\",\n          children: ((_results$embeddings$e = results.embeddings.embedding_analysis) === null || _results$embeddings$e === void 0 ? void 0 : _results$embeddings$e.pattern_analysis) && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"embedding-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Similar Patterns:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), \" \", results.embeddings.embedding_analysis.pattern_analysis.similar_patterns_found]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"embedding-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Pattern Confidence:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), \" \", (results.embeddings.embedding_analysis.pattern_analysis.pattern_confidence * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"holly-interpretation\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Holly's Insights:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: results.embeddings.holly_insights\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 5\n    }, this);\n  };\n  const renderTTMTab = () => {\n    var _results$ttm$scan_res;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-tab-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCC8 TTM Squeeze Scanner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"AI-enhanced TTM Squeeze pattern recognition\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Symbols to Scan:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: ttmSymbols,\n          onChange: e => setTtmSymbols(e.target.value),\n          placeholder: \"AAPL,TSLA,NVDA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Timeframe:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: ttmTimeframe,\n          onChange: e => setTtmTimeframe(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"1min\",\n            children: \"1 minute\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"5min\",\n            children: \"5 minutes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"15min\",\n            children: \"15 minutes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"30min\",\n            children: \"30 minutes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"1hour\",\n            children: \"1 hour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: scanTTM,\n        disabled: loading,\n        className: \"ai-button\",\n        children: loading ? 'Scanning...' : 'Scan TTM Signals'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 7\n      }, this), results.ttm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"TTM Scan Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ttm-summary\",\n          children: ((_results$ttm$scan_res = results.ttm.scan_results) === null || _results$ttm$scan_res === void 0 ? void 0 : _results$ttm$scan_res.signals_found) !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ttm-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Signals Found:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), \" \", results.ttm.scan_results.signals_found]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"holly-interpretation\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Holly's Analysis:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: results.ttm.holly_analysis\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 5\n    }, this);\n  };\n  const renderNewsTab = () => {\n    var _results$news$news_re;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-tab-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDD0D News Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Search current market news and events\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Search Query:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: newsQuery,\n          onChange: e => setNewsQuery(e.target.value),\n          placeholder: \"Apple earnings, Fed meeting, market news\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: searchNews,\n        disabled: loading,\n        className: \"ai-button\",\n        children: loading ? 'Searching...' : 'Search News'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 7\n      }, this), results.news && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"News Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-summary\",\n          children: ((_results$news$news_re = results.news.news_results) === null || _results$news$news_re === void 0 ? void 0 : _results$news$news_re.total_results) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Results Found:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), \" \", results.news.news_results.total_results]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"holly-interpretation\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Holly's Analysis:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: results.news.holly_analysis\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 5\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"ai-features-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83E\\uDD16 Holly AI Advanced Features\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Explore Holly's advanced AI capabilities for market analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'regime' ? 'active' : ''}`,\n        onClick: () => setActiveTab('regime'),\n        children: \"\\uD83C\\uDF0A Regime\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'sentiment' ? 'active' : ''}`,\n        onClick: () => setActiveTab('sentiment'),\n        children: \"\\uD83D\\uDCF1 Sentiment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'embeddings' ? 'active' : ''}`,\n        onClick: () => setActiveTab('embeddings'),\n        children: \"\\uD83E\\uDDE0 Embeddings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'ttm' ? 'active' : ''}`,\n        onClick: () => setActiveTab('ttm'),\n        children: \"\\uD83D\\uDCC8 TTM Scan\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'news' ? 'active' : ''}`,\n        onClick: () => setActiveTab('news'),\n        children: \"\\uD83D\\uDD0D News\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [\"\\u274C \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ai-content\",\n      children: [activeTab === 'regime' && renderRegimeTab(), activeTab === 'sentiment' && renderSentimentTab(), activeTab === 'embeddings' && renderEmbeddingsTab(), activeTab === 'ttm' && renderTTMTab(), activeTab === 'news' && renderNewsTab()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s(AIFeatures, \"4KuQgsiVOLfbH3HqverzeUPhb4w=\");\n_c = AIFeatures;\nexport default AIFeatures;\nvar _c;\n$RefreshReg$(_c, \"AIFeatures\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AIFeatures", "_s", "activeTab", "setActiveTab", "loading", "setLoading", "results", "setResults", "error", "setError", "regimeSymbols", "setRegimeSymbols", "sentimentSymbol", "setSentimentSymbol", "sentimentHours", "setSentimentHours", "embeddingSymbol", "setEmbeddingSymbol", "embeddingContext", "setEmbeddingContext", "ttmSymbols", "setTtmSymbols", "ttmTimeframe", "setTtmTimeframe", "newsQuery", "set<PERSON><PERSON><PERSON>uery", "API_BASE", "process", "env", "REACT_APP_API_URL", "analyzeRegime", "response", "fetch", "data", "json", "prev", "regime", "err", "analyzeSentiment", "sentiment", "generateEmbeddings", "embeddings", "scanTTM", "ttm", "searchNews", "encodeURIComponent", "news", "renderRegimeTab", "_results$regime$regim", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "regime_analysis", "market_regime_analysis", "current_regime", "confidence", "toFixed", "regime_duration_days", "holly_interpretation", "renderSentimentTab", "_results$sentiment$se", "sentiment_analysis", "analysis_summary", "overall_sentiment", "crowd_mood", "total_posts_analyzed", "holly_insights", "renderEmbeddingsTab", "_results$embeddings$e", "embedding_analysis", "pattern_analysis", "similar_patterns_found", "pattern_confidence", "renderTTMTab", "_results$ttm$scan_res", "scan_results", "signals_found", "undefined", "holly_analysis", "renderNewsTab", "_results$news$news_re", "news_results", "total_results", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AIFeatures.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './AIFeatures.css';\n\nconst AIFeatures = () => {\n  const [activeTab, setActiveTab] = useState('regime');\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState({});\n  const [error, setError] = useState(null);\n\n  // Form states\n  const [regimeSymbols, setRegimeSymbols] = useState('SPY,QQQ,VIX');\n  const [sentimentSymbol, setSentimentSymbol] = useState('AAPL');\n  const [sentimentHours, setSentimentHours] = useState(24);\n  const [embeddingSymbol, setEmbeddingSymbol] = useState('AAPL');\n  const [embeddingContext, setEmbeddingContext] = useState('SPY,QQQ,VIX,TLT');\n  const [ttmSymbols, setTtmSymbols] = useState('AAPL,TSLA,NVDA');\n  const [ttmTimeframe, setTtmTimeframe] = useState('5min');\n  const [newsQuery, setNewsQuery] = useState('market news today');\n\n  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n  const analyzeRegime = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/regime?symbols=${regimeSymbols}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, regime: data }));\n    } catch (err) {\n      setError('Failed to analyze market regime');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const analyzeSentiment = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/sentiment/${sentimentSymbol}?hours_back=${sentimentHours}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, sentiment: data }));\n    } catch (err) {\n      setError('Failed to analyze sentiment');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateEmbeddings = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/embeddings/${embeddingSymbol}?context=${embeddingContext}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, embeddings: data }));\n    } catch (err) {\n      setError('Failed to generate embeddings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const scanTTM = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/ttm-scan?symbols=${ttmSymbols}&timeframe=${ttmTimeframe}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, ttm: data }));\n    } catch (err) {\n      setError('Failed to scan TTM signals');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const searchNews = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/news-search?query=${encodeURIComponent(newsQuery)}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, news: data }));\n    } catch (err) {\n      setError('Failed to search news');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderRegimeTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>🌊 Market Regime Detection</h3>\n      <p>Analyze current market conditions and regime classification</p>\n      \n      <div className=\"input-group\">\n        <label>Symbols to Analyze:</label>\n        <input\n          type=\"text\"\n          value={regimeSymbols}\n          onChange={(e) => setRegimeSymbols(e.target.value)}\n          placeholder=\"SPY,QQQ,VIX\"\n        />\n      </div>\n      \n      <button onClick={analyzeRegime} disabled={loading} className=\"ai-button\">\n        {loading ? 'Analyzing...' : 'Analyze Market Regime'}\n      </button>\n      \n      {results.regime && (\n        <div className=\"results-section\">\n          <h4>Regime Analysis Results</h4>\n          <div className=\"regime-summary\">\n            {results.regime.regime_analysis?.market_regime_analysis && (\n              <>\n                <div className=\"regime-item\">\n                  <strong>Current Regime:</strong> {results.regime.regime_analysis.market_regime_analysis.current_regime}\n                </div>\n                <div className=\"regime-item\">\n                  <strong>Confidence:</strong> {(results.regime.regime_analysis.market_regime_analysis.confidence * 100).toFixed(1)}%\n                </div>\n                <div className=\"regime-item\">\n                  <strong>Duration:</strong> {results.regime.regime_analysis.market_regime_analysis.regime_duration_days} days\n                </div>\n              </>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Analysis:</h5>\n            <p>{results.regime.holly_interpretation}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderSentimentTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>📱 Social Sentiment Analysis</h3>\n      <p>Analyze crowd mood from social media platforms</p>\n      \n      <div className=\"input-group\">\n        <label>Symbol:</label>\n        <input\n          type=\"text\"\n          value={sentimentSymbol}\n          onChange={(e) => setSentimentSymbol(e.target.value)}\n          placeholder=\"AAPL\"\n        />\n      </div>\n      \n      <div className=\"input-group\">\n        <label>Hours Back:</label>\n        <select value={sentimentHours} onChange={(e) => setSentimentHours(e.target.value)}>\n          <option value={6}>6 hours</option>\n          <option value={12}>12 hours</option>\n          <option value={24}>24 hours</option>\n          <option value={48}>48 hours</option>\n        </select>\n      </div>\n      \n      <button onClick={analyzeSentiment} disabled={loading} className=\"ai-button\">\n        {loading ? 'Analyzing...' : 'Analyze Sentiment'}\n      </button>\n      \n      {results.sentiment && (\n        <div className=\"results-section\">\n          <h4>Sentiment Analysis Results</h4>\n          <div className=\"sentiment-summary\">\n            {results.sentiment.sentiment_analysis?.analysis_summary && (\n              <>\n                <div className=\"sentiment-item\">\n                  <strong>Overall Sentiment:</strong> {results.sentiment.sentiment_analysis.analysis_summary.overall_sentiment}\n                </div>\n                <div className=\"sentiment-item\">\n                  <strong>Crowd Mood:</strong> {results.sentiment.sentiment_analysis.analysis_summary.crowd_mood}\n                </div>\n                <div className=\"sentiment-item\">\n                  <strong>Posts Analyzed:</strong> {results.sentiment.sentiment_analysis.analysis_summary.total_posts_analyzed}\n                </div>\n              </>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Insights:</h5>\n            <p>{results.sentiment.holly_insights}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderEmbeddingsTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>🧠 Market Embeddings</h3>\n      <p>Advanced pattern matching and market context analysis</p>\n      \n      <div className=\"input-group\">\n        <label>Primary Symbol:</label>\n        <input\n          type=\"text\"\n          value={embeddingSymbol}\n          onChange={(e) => setEmbeddingSymbol(e.target.value)}\n          placeholder=\"AAPL\"\n        />\n      </div>\n      \n      <div className=\"input-group\">\n        <label>Context Symbols:</label>\n        <input\n          type=\"text\"\n          value={embeddingContext}\n          onChange={(e) => setEmbeddingContext(e.target.value)}\n          placeholder=\"SPY,QQQ,VIX,TLT\"\n        />\n      </div>\n      \n      <button onClick={generateEmbeddings} disabled={loading} className=\"ai-button\">\n        {loading ? 'Generating...' : 'Generate Embeddings'}\n      </button>\n      \n      {results.embeddings && (\n        <div className=\"results-section\">\n          <h4>Embedding Analysis Results</h4>\n          <div className=\"embeddings-summary\">\n            {results.embeddings.embedding_analysis?.pattern_analysis && (\n              <>\n                <div className=\"embedding-item\">\n                  <strong>Similar Patterns:</strong> {results.embeddings.embedding_analysis.pattern_analysis.similar_patterns_found}\n                </div>\n                <div className=\"embedding-item\">\n                  <strong>Pattern Confidence:</strong> {(results.embeddings.embedding_analysis.pattern_analysis.pattern_confidence * 100).toFixed(1)}%\n                </div>\n              </>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Insights:</h5>\n            <p>{results.embeddings.holly_insights}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderTTMTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>📈 TTM Squeeze Scanner</h3>\n      <p>AI-enhanced TTM Squeeze pattern recognition</p>\n      \n      <div className=\"input-group\">\n        <label>Symbols to Scan:</label>\n        <input\n          type=\"text\"\n          value={ttmSymbols}\n          onChange={(e) => setTtmSymbols(e.target.value)}\n          placeholder=\"AAPL,TSLA,NVDA\"\n        />\n      </div>\n      \n      <div className=\"input-group\">\n        <label>Timeframe:</label>\n        <select value={ttmTimeframe} onChange={(e) => setTtmTimeframe(e.target.value)}>\n          <option value=\"1min\">1 minute</option>\n          <option value=\"5min\">5 minutes</option>\n          <option value=\"15min\">15 minutes</option>\n          <option value=\"30min\">30 minutes</option>\n          <option value=\"1hour\">1 hour</option>\n        </select>\n      </div>\n      \n      <button onClick={scanTTM} disabled={loading} className=\"ai-button\">\n        {loading ? 'Scanning...' : 'Scan TTM Signals'}\n      </button>\n      \n      {results.ttm && (\n        <div className=\"results-section\">\n          <h4>TTM Scan Results</h4>\n          <div className=\"ttm-summary\">\n            {results.ttm.scan_results?.signals_found !== undefined && (\n              <div className=\"ttm-item\">\n                <strong>Signals Found:</strong> {results.ttm.scan_results.signals_found}\n              </div>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Analysis:</h5>\n            <p>{results.ttm.holly_analysis}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderNewsTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>🔍 News Search</h3>\n      <p>Search current market news and events</p>\n      \n      <div className=\"input-group\">\n        <label>Search Query:</label>\n        <input\n          type=\"text\"\n          value={newsQuery}\n          onChange={(e) => setNewsQuery(e.target.value)}\n          placeholder=\"Apple earnings, Fed meeting, market news\"\n        />\n      </div>\n      \n      <button onClick={searchNews} disabled={loading} className=\"ai-button\">\n        {loading ? 'Searching...' : 'Search News'}\n      </button>\n      \n      {results.news && (\n        <div className=\"results-section\">\n          <h4>News Search Results</h4>\n          <div className=\"news-summary\">\n            {results.news.news_results?.total_results && (\n              <div className=\"news-item\">\n                <strong>Results Found:</strong> {results.news.news_results.total_results}\n              </div>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Analysis:</h5>\n            <p>{results.news.holly_analysis}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"ai-features-container\">\n      <div className=\"ai-header\">\n        <h2>🤖 Holly AI Advanced Features</h2>\n        <p>Explore Holly's advanced AI capabilities for market analysis</p>\n      </div>\n\n      <div className=\"ai-tabs\">\n        <button \n          className={`tab-button ${activeTab === 'regime' ? 'active' : ''}`}\n          onClick={() => setActiveTab('regime')}\n        >\n          🌊 Regime\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'sentiment' ? 'active' : ''}`}\n          onClick={() => setActiveTab('sentiment')}\n        >\n          📱 Sentiment\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'embeddings' ? 'active' : ''}`}\n          onClick={() => setActiveTab('embeddings')}\n        >\n          🧠 Embeddings\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'ttm' ? 'active' : ''}`}\n          onClick={() => setActiveTab('ttm')}\n        >\n          📈 TTM Scan\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'news' ? 'active' : ''}`}\n          onClick={() => setActiveTab('news')}\n        >\n          🔍 News\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"error-message\">\n          ❌ {error}\n        </div>\n      )}\n\n      <div className=\"ai-content\">\n        {activeTab === 'regime' && renderRegimeTab()}\n        {activeTab === 'sentiment' && renderSentimentTab()}\n        {activeTab === 'embeddings' && renderEmbeddingsTab()}\n        {activeTab === 'ttm' && renderTTMTab()}\n        {activeTab === 'news' && renderNewsTab()}\n      </div>\n    </div>\n  );\n};\n\nexport default AIFeatures;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,aAAa,CAAC;EACjE,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,MAAM,CAAC;EAC9D,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,MAAM,CAAC;EAC9D,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,iBAAiB,CAAC;EAC3E,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,gBAAgB,CAAC;EAC9D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,MAAM,CAAC;EACxD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,mBAAmB,CAAC;EAE/D,MAAMgC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAEzE,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCzB,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,QAAQ,4BAA4BhB,aAAa,EAAE,CAAC;MACpF,MAAMuB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC3B,UAAU,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEC,MAAM,EAAEH;MAAK,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ5B,QAAQ,CAAC,iCAAiC,CAAC;IAC7C,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCjC,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,QAAQ,uBAAuBd,eAAe,eAAeE,cAAc,EAAE,CAAC;MAC9G,MAAMmB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC3B,UAAU,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEI,SAAS,EAAEN;MAAK,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ5B,QAAQ,CAAC,6BAA6B,CAAC;IACzC,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCnC,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,QAAQ,wBAAwBV,eAAe,YAAYE,gBAAgB,EAAE,CAAC;MAC9G,MAAMe,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC3B,UAAU,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEM,UAAU,EAAER;MAAK,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ5B,QAAQ,CAAC,+BAA+B,CAAC;IAC3C,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1BrC,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,QAAQ,8BAA8BN,UAAU,cAAcE,YAAY,EAAE,CAAC;MAC7G,MAAMW,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC3B,UAAU,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEQ,GAAG,EAAEV;MAAK,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ5B,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BvC,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,QAAQ,+BAA+BmB,kBAAkB,CAACrB,SAAS,CAAC,EAAE,CAAC;MACvG,MAAMS,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC3B,UAAU,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEW,IAAI,EAAEb;MAAK,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ5B,QAAQ,CAAC,uBAAuB,CAAC;IACnC,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,eAAe,GAAGA,CAAA;IAAA,IAAAC,qBAAA;IAAA,oBACtBnD,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrD,OAAA;QAAAqD,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCzD,OAAA;QAAAqD,QAAA,EAAG;MAA2D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAElEzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClCzD,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE9C,aAAc;UACrB+C,QAAQ,EAAGC,CAAC,IAAK/C,gBAAgB,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClDI,WAAW,EAAC;QAAa;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QAAQgE,OAAO,EAAE/B,aAAc;QAACgC,QAAQ,EAAE1D,OAAQ;QAAC6C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACrE9C,OAAO,GAAG,cAAc,GAAG;MAAuB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EAERhD,OAAO,CAAC8B,MAAM,iBACbvC,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCzD,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5B,EAAAF,qBAAA,GAAA1C,OAAO,CAAC8B,MAAM,CAAC2B,eAAe,cAAAf,qBAAA,uBAA9BA,qBAAA,CAAgCgB,sBAAsB,kBACrDnE,OAAA,CAAAE,SAAA;YAAAmD,QAAA,gBACErD,OAAA;cAAKoD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAAC8B,MAAM,CAAC2B,eAAe,CAACC,sBAAsB,CAACC,cAAc;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,CAAChD,OAAO,CAAC8B,MAAM,CAAC2B,eAAe,CAACC,sBAAsB,CAACE,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACpH;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAAC8B,MAAM,CAAC2B,eAAe,CAACC,sBAAsB,CAACI,oBAAoB,EAAC,OACzG;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,eACN;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCrD,OAAA;YAAAqD,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BzD,OAAA;YAAAqD,QAAA,EAAI5C,OAAO,CAAC8B,MAAM,CAACiC;UAAoB;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAMgB,kBAAkB,GAAGA,CAAA;IAAA,IAAAC,qBAAA;IAAA,oBACzB1E,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrD,OAAA;QAAAqD,QAAA,EAAI;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrCzD,OAAA;QAAAqD,QAAA,EAAG;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAErDzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtBzD,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE5C,eAAgB;UACvB6C,QAAQ,EAAGC,CAAC,IAAK7C,kBAAkB,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BzD,OAAA;UAAQ2D,KAAK,EAAE1C,cAAe;UAAC2C,QAAQ,EAAGC,CAAC,IAAK3C,iBAAiB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAN,QAAA,gBAChFrD,OAAA;YAAQ2D,KAAK,EAAE,CAAE;YAAAN,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClCzD,OAAA;YAAQ2D,KAAK,EAAE,EAAG;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCzD,OAAA;YAAQ2D,KAAK,EAAE,EAAG;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCzD,OAAA;YAAQ2D,KAAK,EAAE,EAAG;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENzD,OAAA;QAAQgE,OAAO,EAAEvB,gBAAiB;QAACwB,QAAQ,EAAE1D,OAAQ;QAAC6C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACxE9C,OAAO,GAAG,cAAc,GAAG;MAAmB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EAERhD,OAAO,CAACiC,SAAS,iBAChB1C,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCzD,OAAA;UAAKoD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/B,EAAAqB,qBAAA,GAAAjE,OAAO,CAACiC,SAAS,CAACiC,kBAAkB,cAAAD,qBAAA,uBAApCA,qBAAA,CAAsCE,gBAAgB,kBACrD5E,OAAA,CAAAE,SAAA;YAAAmD,QAAA,gBACErD,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAACiC,SAAS,CAACiC,kBAAkB,CAACC,gBAAgB,CAACC,iBAAiB;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAACiC,SAAS,CAACiC,kBAAkB,CAACC,gBAAgB,CAACE,UAAU;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAACiC,SAAS,CAACiC,kBAAkB,CAACC,gBAAgB,CAACG,oBAAoB;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC;UAAA,eACN;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCrD,OAAA;YAAAqD,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BzD,OAAA;YAAAqD,QAAA,EAAI5C,OAAO,CAACiC,SAAS,CAACsC;UAAc;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAMwB,mBAAmB,GAAGA,CAAA;IAAA,IAAAC,qBAAA;IAAA,oBAC1BlF,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrD,OAAA;QAAAqD,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BzD,OAAA;QAAAqD,QAAA,EAAG;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAE5DzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9BzD,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXC,KAAK,EAAExC,eAAgB;UACvByC,QAAQ,EAAGC,CAAC,IAAKzC,kBAAkB,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/BzD,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEtC,gBAAiB;UACxBuC,QAAQ,EAAGC,CAAC,IAAKvC,mBAAmB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACrDI,WAAW,EAAC;QAAiB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QAAQgE,OAAO,EAAErB,kBAAmB;QAACsB,QAAQ,EAAE1D,OAAQ;QAAC6C,SAAS,EAAC,WAAW;QAAAC,QAAA,EAC1E9C,OAAO,GAAG,eAAe,GAAG;MAAqB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,EAERhD,OAAO,CAACmC,UAAU,iBACjB5C,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCzD,OAAA;UAAKoD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChC,EAAA6B,qBAAA,GAAAzE,OAAO,CAACmC,UAAU,CAACuC,kBAAkB,cAAAD,qBAAA,uBAArCA,qBAAA,CAAuCE,gBAAgB,kBACtDpF,OAAA,CAAAE,SAAA;YAAAmD,QAAA,gBACErD,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAACmC,UAAU,CAACuC,kBAAkB,CAACC,gBAAgB,CAACC,sBAAsB;YAAA;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,CAAChD,OAAO,CAACmC,UAAU,CAACuC,kBAAkB,CAACC,gBAAgB,CAACE,kBAAkB,GAAG,GAAG,EAAEhB,OAAO,CAAC,CAAC,CAAC,EAAC,GACrI;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,eACN;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCrD,OAAA;YAAAqD,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BzD,OAAA;YAAAqD,QAAA,EAAI5C,OAAO,CAACmC,UAAU,CAACoC;UAAc;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAM8B,YAAY,GAAGA,CAAA;IAAA,IAAAC,qBAAA;IAAA,oBACnBxF,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrD,OAAA;QAAAqD,QAAA,EAAI;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/BzD,OAAA;QAAAqD,QAAA,EAAG;MAA2C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAElDzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/BzD,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEpC,UAAW;UAClBqC,QAAQ,EAAGC,CAAC,IAAKrC,aAAa,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,WAAW,EAAC;QAAgB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzBzD,OAAA;UAAQ2D,KAAK,EAAElC,YAAa;UAACmC,QAAQ,EAAGC,CAAC,IAAKnC,eAAe,CAACmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAN,QAAA,gBAC5ErD,OAAA;YAAQ2D,KAAK,EAAC,MAAM;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCzD,OAAA;YAAQ2D,KAAK,EAAC,MAAM;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvCzD,OAAA;YAAQ2D,KAAK,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzCzD,OAAA;YAAQ2D,KAAK,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzCzD,OAAA;YAAQ2D,KAAK,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENzD,OAAA;QAAQgE,OAAO,EAAEnB,OAAQ;QAACoB,QAAQ,EAAE1D,OAAQ;QAAC6C,SAAS,EAAC,WAAW;QAAAC,QAAA,EAC/D9C,OAAO,GAAG,aAAa,GAAG;MAAkB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,EAERhD,OAAO,CAACqC,GAAG,iBACV9C,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBzD,OAAA;UAAKoD,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB,EAAAmC,qBAAA,GAAA/E,OAAO,CAACqC,GAAG,CAAC2C,YAAY,cAAAD,qBAAA,uBAAxBA,qBAAA,CAA0BE,aAAa,MAAKC,SAAS,iBACpD3F,OAAA;YAAKoD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBrD,OAAA;cAAAqD,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAACqC,GAAG,CAAC2C,YAAY,CAACC,aAAa;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCrD,OAAA;YAAAqD,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BzD,OAAA;YAAAqD,QAAA,EAAI5C,OAAO,CAACqC,GAAG,CAAC8C;UAAc;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAMoC,aAAa,GAAGA,CAAA;IAAA,IAAAC,qBAAA;IAAA,oBACpB9F,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrD,OAAA;QAAAqD,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBzD,OAAA;QAAAqD,QAAA,EAAG;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAE5CzD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAAqD,QAAA,EAAO;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5BzD,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEhC,SAAU;UACjBiC,QAAQ,EAAGC,CAAC,IAAKjC,YAAY,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC9CI,WAAW,EAAC;QAA0C;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QAAQgE,OAAO,EAAEjB,UAAW;QAACkB,QAAQ,EAAE1D,OAAQ;QAAC6C,SAAS,EAAC,WAAW;QAAAC,QAAA,EAClE9C,OAAO,GAAG,cAAc,GAAG;MAAa;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,EAERhD,OAAO,CAACwC,IAAI,iBACXjD,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BzD,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,EAAAyC,qBAAA,GAAArF,OAAO,CAACwC,IAAI,CAAC8C,YAAY,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BE,aAAa,kBACvChG,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAAqD,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAChD,OAAO,CAACwC,IAAI,CAAC8C,YAAY,CAACC,aAAa;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCrD,OAAA;YAAAqD,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BzD,OAAA;YAAAqD,QAAA,EAAI5C,OAAO,CAACwC,IAAI,CAAC2C;UAAc;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,oBACEzD,OAAA;IAAKoD,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCrD,OAAA;MAAKoD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBrD,OAAA;QAAAqD,QAAA,EAAI;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtCzD,OAAA;QAAAqD,QAAA,EAAG;MAA4D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eAENzD,OAAA;MAAKoD,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBrD,OAAA;QACEoD,SAAS,EAAE,cAAc/C,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAClE2D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,QAAQ,CAAE;QAAA+C,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzD,OAAA;QACEoD,SAAS,EAAE,cAAc/C,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;QACrE2D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,WAAW,CAAE;QAAA+C,QAAA,EAC1C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzD,OAAA;QACEoD,SAAS,EAAE,cAAc/C,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;QACtE2D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,YAAY,CAAE;QAAA+C,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzD,OAAA;QACEoD,SAAS,EAAE,cAAc/C,SAAS,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC/D2D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,KAAK,CAAE;QAAA+C,QAAA,EACpC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzD,OAAA;QACEoD,SAAS,EAAE,cAAc/C,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;QAChE2D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,MAAM,CAAE;QAAA+C,QAAA,EACrC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL9C,KAAK,iBACJX,OAAA;MAAKoD,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,SAC3B,EAAC1C,KAAK;IAAA;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAEDzD,OAAA;MAAKoD,SAAS,EAAC,YAAY;MAAAC,QAAA,GACxBhD,SAAS,KAAK,QAAQ,IAAI6C,eAAe,CAAC,CAAC,EAC3C7C,SAAS,KAAK,WAAW,IAAIoE,kBAAkB,CAAC,CAAC,EACjDpE,SAAS,KAAK,YAAY,IAAI4E,mBAAmB,CAAC,CAAC,EACnD5E,SAAS,KAAK,KAAK,IAAIkF,YAAY,CAAC,CAAC,EACrClF,SAAS,KAAK,MAAM,IAAIwF,aAAa,CAAC,CAAC;IAAA;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAjYID,UAAU;AAAA8F,EAAA,GAAV9F,UAAU;AAmYhB,eAAeA,UAAU;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}