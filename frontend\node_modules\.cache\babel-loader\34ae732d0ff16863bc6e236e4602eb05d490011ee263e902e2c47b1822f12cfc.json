{"ast": null, "code": "'use client';\n\nexport { default } from './TableContainer';\nexport { default as tableContainerClasses } from './tableContainerClasses';\nexport * from './tableContainerClasses';", "map": {"version": 3, "names": ["default", "tableContainerClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/TableContainer/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TableContainer';\nexport { default as tableContainerClasses } from './tableContainerClasses';\nexport * from './tableContainerClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,yBAAyB;AAC1E,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}