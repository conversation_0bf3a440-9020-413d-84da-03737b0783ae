{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./AIFeatures.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AIFeatures=()=>{const[activeTab,setActiveTab]=useState('regime');const[loading,setLoading]=useState(false);const[results,setResults]=useState({});const[error,setError]=useState(null);// Form states\nconst[regimeSymbols,setRegimeSymbols]=useState('SPY,QQQ,VIX');const[sentimentSymbol,setSentimentSymbol]=useState('AAPL');const[sentimentHours,setSentimentHours]=useState(24);const[embeddingSymbol,setEmbeddingSymbol]=useState('AAPL');const[embeddingContext,setEmbeddingContext]=useState('SPY,QQQ,VIX,TLT');const[ttmSymbols,setTtmSymbols]=useState('AAPL,TSLA,NVDA');const[ttmTimeframe,setTtmTimeframe]=useState('5min');const[newsQuery,setNewsQuery]=useState('market news today');const API_BASE=process.env.REACT_APP_API_URL||'http://localhost:8000';const analyzeRegime=async()=>{setLoading(true);setError(null);try{const response=await fetch(\"\".concat(API_BASE,\"/holly/ai/regime?symbols=\").concat(regimeSymbols));const data=await response.json();setResults(prev=>_objectSpread(_objectSpread({},prev),{},{regime:data}));}catch(err){setError('Failed to analyze market regime');}finally{setLoading(false);}};const analyzeSentiment=async()=>{setLoading(true);setError(null);try{const response=await fetch(\"\".concat(API_BASE,\"/holly/ai/sentiment/\").concat(sentimentSymbol,\"?hours_back=\").concat(sentimentHours));const data=await response.json();setResults(prev=>_objectSpread(_objectSpread({},prev),{},{sentiment:data}));}catch(err){setError('Failed to analyze sentiment');}finally{setLoading(false);}};const generateEmbeddings=async()=>{setLoading(true);setError(null);try{const response=await fetch(\"\".concat(API_BASE,\"/holly/ai/embeddings/\").concat(embeddingSymbol,\"?context=\").concat(embeddingContext));const data=await response.json();setResults(prev=>_objectSpread(_objectSpread({},prev),{},{embeddings:data}));}catch(err){setError('Failed to generate embeddings');}finally{setLoading(false);}};const scanTTM=async()=>{setLoading(true);setError(null);try{const response=await fetch(\"\".concat(API_BASE,\"/holly/ai/ttm-scan?symbols=\").concat(ttmSymbols,\"&timeframe=\").concat(ttmTimeframe));const data=await response.json();setResults(prev=>_objectSpread(_objectSpread({},prev),{},{ttm:data}));}catch(err){setError('Failed to scan TTM signals');}finally{setLoading(false);}};const searchNews=async()=>{setLoading(true);setError(null);try{const response=await fetch(\"\".concat(API_BASE,\"/holly/ai/news-search?query=\").concat(encodeURIComponent(newsQuery)));const data=await response.json();setResults(prev=>_objectSpread(_objectSpread({},prev),{},{news:data}));}catch(err){setError('Failed to search news');}finally{setLoading(false);}};const renderRegimeTab=()=>{var _results$regime$regim;return/*#__PURE__*/_jsxs(\"div\",{className:\"ai-tab-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83C\\uDF0A Market Regime Detection\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Analyze current market conditions and regime classification\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Symbols to Analyze:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:regimeSymbols,onChange:e=>setRegimeSymbols(e.target.value),placeholder:\"SPY,QQQ,VIX\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:analyzeRegime,disabled:loading,className:\"ai-button\",children:loading?'Analyzing...':'Analyze Market Regime'}),results.regime&&/*#__PURE__*/_jsxs(\"div\",{className:\"results-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Regime Analysis Results\"}),/*#__PURE__*/_jsx(\"div\",{className:\"regime-summary\",children:((_results$regime$regim=results.regime.regime_analysis)===null||_results$regime$regim===void 0?void 0:_results$regime$regim.market_regime_analysis)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"regime-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Current Regime:\"}),\" \",results.regime.regime_analysis.market_regime_analysis.current_regime]}),/*#__PURE__*/_jsxs(\"div\",{className:\"regime-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Confidence:\"}),\" \",(results.regime.regime_analysis.market_regime_analysis.confidence*100).toFixed(1),\"%\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"regime-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Duration:\"}),\" \",results.regime.regime_analysis.market_regime_analysis.regime_duration_days,\" days\"]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"holly-interpretation\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Holly's Analysis:\"}),/*#__PURE__*/_jsx(\"p\",{children:results.regime.holly_interpretation})]})]})]});};const renderSentimentTab=()=>{var _results$sentiment$se;return/*#__PURE__*/_jsxs(\"div\",{className:\"ai-tab-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDCF1 Social Sentiment Analysis\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Analyze crowd mood from social media platforms\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Symbol:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:sentimentSymbol,onChange:e=>setSentimentSymbol(e.target.value),placeholder:\"AAPL\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Hours Back:\"}),/*#__PURE__*/_jsxs(\"select\",{value:sentimentHours,onChange:e=>setSentimentHours(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:6,children:\"6 hours\"}),/*#__PURE__*/_jsx(\"option\",{value:12,children:\"12 hours\"}),/*#__PURE__*/_jsx(\"option\",{value:24,children:\"24 hours\"}),/*#__PURE__*/_jsx(\"option\",{value:48,children:\"48 hours\"})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:analyzeSentiment,disabled:loading,className:\"ai-button\",children:loading?'Analyzing...':'Analyze Sentiment'}),results.sentiment&&/*#__PURE__*/_jsxs(\"div\",{className:\"results-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Sentiment Analysis Results\"}),/*#__PURE__*/_jsx(\"div\",{className:\"sentiment-summary\",children:((_results$sentiment$se=results.sentiment.sentiment_analysis)===null||_results$sentiment$se===void 0?void 0:_results$sentiment$se.analysis_summary)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sentiment-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Overall Sentiment:\"}),\" \",results.sentiment.sentiment_analysis.analysis_summary.overall_sentiment]}),/*#__PURE__*/_jsxs(\"div\",{className:\"sentiment-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Crowd Mood:\"}),\" \",results.sentiment.sentiment_analysis.analysis_summary.crowd_mood]}),/*#__PURE__*/_jsxs(\"div\",{className:\"sentiment-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Posts Analyzed:\"}),\" \",results.sentiment.sentiment_analysis.analysis_summary.total_posts_analyzed]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"holly-interpretation\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Holly's Insights:\"}),/*#__PURE__*/_jsx(\"p\",{children:results.sentiment.holly_insights})]})]})]});};const renderEmbeddingsTab=()=>{var _results$embeddings$e;return/*#__PURE__*/_jsxs(\"div\",{className:\"ai-tab-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83E\\uDDE0 Market Embeddings\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Advanced pattern matching and market context analysis\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Primary Symbol:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:embeddingSymbol,onChange:e=>setEmbeddingSymbol(e.target.value),placeholder:\"AAPL\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Context Symbols:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:embeddingContext,onChange:e=>setEmbeddingContext(e.target.value),placeholder:\"SPY,QQQ,VIX,TLT\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:generateEmbeddings,disabled:loading,className:\"ai-button\",children:loading?'Generating...':'Generate Embeddings'}),results.embeddings&&/*#__PURE__*/_jsxs(\"div\",{className:\"results-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Embedding Analysis Results\"}),/*#__PURE__*/_jsx(\"div\",{className:\"embeddings-summary\",children:((_results$embeddings$e=results.embeddings.embedding_analysis)===null||_results$embeddings$e===void 0?void 0:_results$embeddings$e.pattern_analysis)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"embedding-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Similar Patterns:\"}),\" \",results.embeddings.embedding_analysis.pattern_analysis.similar_patterns_found]}),/*#__PURE__*/_jsxs(\"div\",{className:\"embedding-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Pattern Confidence:\"}),\" \",(results.embeddings.embedding_analysis.pattern_analysis.pattern_confidence*100).toFixed(1),\"%\"]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"holly-interpretation\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Holly's Insights:\"}),/*#__PURE__*/_jsx(\"p\",{children:results.embeddings.holly_insights})]})]})]});};const renderTTMTab=()=>{var _results$ttm$scan_res;return/*#__PURE__*/_jsxs(\"div\",{className:\"ai-tab-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDCC8 TTM Squeeze Scanner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"AI-enhanced TTM Squeeze pattern recognition\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Symbols to Scan:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:ttmSymbols,onChange:e=>setTtmSymbols(e.target.value),placeholder:\"AAPL,TSLA,NVDA\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Timeframe:\"}),/*#__PURE__*/_jsxs(\"select\",{value:ttmTimeframe,onChange:e=>setTtmTimeframe(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"1min\",children:\"1 minute\"}),/*#__PURE__*/_jsx(\"option\",{value:\"5min\",children:\"5 minutes\"}),/*#__PURE__*/_jsx(\"option\",{value:\"15min\",children:\"15 minutes\"}),/*#__PURE__*/_jsx(\"option\",{value:\"30min\",children:\"30 minutes\"}),/*#__PURE__*/_jsx(\"option\",{value:\"1hour\",children:\"1 hour\"})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:scanTTM,disabled:loading,className:\"ai-button\",children:loading?'Scanning...':'Scan TTM Signals'}),results.ttm&&/*#__PURE__*/_jsxs(\"div\",{className:\"results-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"TTM Scan Results\"}),/*#__PURE__*/_jsx(\"div\",{className:\"ttm-summary\",children:((_results$ttm$scan_res=results.ttm.scan_results)===null||_results$ttm$scan_res===void 0?void 0:_results$ttm$scan_res.signals_found)!==undefined&&/*#__PURE__*/_jsxs(\"div\",{className:\"ttm-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Signals Found:\"}),\" \",results.ttm.scan_results.signals_found]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"holly-interpretation\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Holly's Analysis:\"}),/*#__PURE__*/_jsx(\"p\",{children:results.ttm.holly_analysis})]})]})]});};const renderNewsTab=()=>{var _results$news$news_re;return/*#__PURE__*/_jsxs(\"div\",{className:\"ai-tab-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDD0D News Search\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Search current market news and events\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Search Query:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newsQuery,onChange:e=>setNewsQuery(e.target.value),placeholder:\"Apple earnings, Fed meeting, market news\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:searchNews,disabled:loading,className:\"ai-button\",children:loading?'Searching...':'Search News'}),results.news&&/*#__PURE__*/_jsxs(\"div\",{className:\"results-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"News Search Results\"}),/*#__PURE__*/_jsx(\"div\",{className:\"news-summary\",children:((_results$news$news_re=results.news.news_results)===null||_results$news$news_re===void 0?void 0:_results$news$news_re.total_results)&&/*#__PURE__*/_jsxs(\"div\",{className:\"news-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Results Found:\"}),\" \",results.news.news_results.total_results]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"holly-interpretation\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Holly's Analysis:\"}),/*#__PURE__*/_jsx(\"p\",{children:results.news.holly_analysis})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"ai-features-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"ai-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83E\\uDD16 Holly AI Advanced Features\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Explore Holly's advanced AI capabilities for market analysis\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"ai-tabs\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"tab-button \".concat(activeTab==='regime'?'active':''),onClick:()=>setActiveTab('regime'),children:\"\\uD83C\\uDF0A Regime\"}),/*#__PURE__*/_jsx(\"button\",{className:\"tab-button \".concat(activeTab==='sentiment'?'active':''),onClick:()=>setActiveTab('sentiment'),children:\"\\uD83D\\uDCF1 Sentiment\"}),/*#__PURE__*/_jsx(\"button\",{className:\"tab-button \".concat(activeTab==='embeddings'?'active':''),onClick:()=>setActiveTab('embeddings'),children:\"\\uD83E\\uDDE0 Embeddings\"}),/*#__PURE__*/_jsx(\"button\",{className:\"tab-button \".concat(activeTab==='ttm'?'active':''),onClick:()=>setActiveTab('ttm'),children:\"\\uD83D\\uDCC8 TTM Scan\"}),/*#__PURE__*/_jsx(\"button\",{className:\"tab-button \".concat(activeTab==='news'?'active':''),onClick:()=>setActiveTab('news'),children:\"\\uD83D\\uDD0D News\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[\"\\u274C \",error]}),/*#__PURE__*/_jsxs(\"div\",{className:\"ai-content\",children:[activeTab==='regime'&&renderRegimeTab(),activeTab==='sentiment'&&renderSentimentTab(),activeTab==='embeddings'&&renderEmbeddingsTab(),activeTab==='ttm'&&renderTTMTab(),activeTab==='news'&&renderNewsTab()]})]});};export default AIFeatures;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AIFeatures", "activeTab", "setActiveTab", "loading", "setLoading", "results", "setResults", "error", "setError", "regimeSymbols", "setRegimeSymbols", "sentimentSymbol", "setSentimentSymbol", "sentimentHours", "setSentimentHours", "embeddingSymbol", "setEmbeddingSymbol", "embeddingContext", "setEmbeddingContext", "ttmSymbols", "setTtmSymbols", "ttmTimeframe", "setTtmTimeframe", "newsQuery", "set<PERSON><PERSON><PERSON>uery", "API_BASE", "process", "env", "REACT_APP_API_URL", "analyzeRegime", "response", "fetch", "concat", "data", "json", "prev", "_objectSpread", "regime", "err", "analyzeSentiment", "sentiment", "generateEmbeddings", "embeddings", "scanTTM", "ttm", "searchNews", "encodeURIComponent", "news", "renderRegimeTab", "_results$regime$regim", "className", "children", "type", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "regime_analysis", "market_regime_analysis", "current_regime", "confidence", "toFixed", "regime_duration_days", "holly_interpretation", "renderSentimentTab", "_results$sentiment$se", "sentiment_analysis", "analysis_summary", "overall_sentiment", "crowd_mood", "total_posts_analyzed", "holly_insights", "renderEmbeddingsTab", "_results$embeddings$e", "embedding_analysis", "pattern_analysis", "similar_patterns_found", "pattern_confidence", "renderTTMTab", "_results$ttm$scan_res", "scan_results", "signals_found", "undefined", "holly_analysis", "renderNewsTab", "_results$news$news_re", "news_results", "total_results"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AIFeatures.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './AIFeatures.css';\n\nconst AIFeatures = () => {\n  const [activeTab, setActiveTab] = useState('regime');\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState({});\n  const [error, setError] = useState(null);\n\n  // Form states\n  const [regimeSymbols, setRegimeSymbols] = useState('SPY,QQQ,VIX');\n  const [sentimentSymbol, setSentimentSymbol] = useState('AAPL');\n  const [sentimentHours, setSentimentHours] = useState(24);\n  const [embeddingSymbol, setEmbeddingSymbol] = useState('AAPL');\n  const [embeddingContext, setEmbeddingContext] = useState('SPY,QQQ,VIX,TLT');\n  const [ttmSymbols, setTtmSymbols] = useState('AAPL,TSLA,NVDA');\n  const [ttmTimeframe, setTtmTimeframe] = useState('5min');\n  const [newsQuery, setNewsQuery] = useState('market news today');\n\n  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n  const analyzeRegime = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/regime?symbols=${regimeSymbols}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, regime: data }));\n    } catch (err) {\n      setError('Failed to analyze market regime');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const analyzeSentiment = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/sentiment/${sentimentSymbol}?hours_back=${sentimentHours}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, sentiment: data }));\n    } catch (err) {\n      setError('Failed to analyze sentiment');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateEmbeddings = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/embeddings/${embeddingSymbol}?context=${embeddingContext}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, embeddings: data }));\n    } catch (err) {\n      setError('Failed to generate embeddings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const scanTTM = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/ttm-scan?symbols=${ttmSymbols}&timeframe=${ttmTimeframe}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, ttm: data }));\n    } catch (err) {\n      setError('Failed to scan TTM signals');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const searchNews = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch(`${API_BASE}/holly/ai/news-search?query=${encodeURIComponent(newsQuery)}`);\n      const data = await response.json();\n      setResults(prev => ({ ...prev, news: data }));\n    } catch (err) {\n      setError('Failed to search news');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderRegimeTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>🌊 Market Regime Detection</h3>\n      <p>Analyze current market conditions and regime classification</p>\n      \n      <div className=\"input-group\">\n        <label>Symbols to Analyze:</label>\n        <input\n          type=\"text\"\n          value={regimeSymbols}\n          onChange={(e) => setRegimeSymbols(e.target.value)}\n          placeholder=\"SPY,QQQ,VIX\"\n        />\n      </div>\n      \n      <button onClick={analyzeRegime} disabled={loading} className=\"ai-button\">\n        {loading ? 'Analyzing...' : 'Analyze Market Regime'}\n      </button>\n      \n      {results.regime && (\n        <div className=\"results-section\">\n          <h4>Regime Analysis Results</h4>\n          <div className=\"regime-summary\">\n            {results.regime.regime_analysis?.market_regime_analysis && (\n              <>\n                <div className=\"regime-item\">\n                  <strong>Current Regime:</strong> {results.regime.regime_analysis.market_regime_analysis.current_regime}\n                </div>\n                <div className=\"regime-item\">\n                  <strong>Confidence:</strong> {(results.regime.regime_analysis.market_regime_analysis.confidence * 100).toFixed(1)}%\n                </div>\n                <div className=\"regime-item\">\n                  <strong>Duration:</strong> {results.regime.regime_analysis.market_regime_analysis.regime_duration_days} days\n                </div>\n              </>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Analysis:</h5>\n            <p>{results.regime.holly_interpretation}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderSentimentTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>📱 Social Sentiment Analysis</h3>\n      <p>Analyze crowd mood from social media platforms</p>\n      \n      <div className=\"input-group\">\n        <label>Symbol:</label>\n        <input\n          type=\"text\"\n          value={sentimentSymbol}\n          onChange={(e) => setSentimentSymbol(e.target.value)}\n          placeholder=\"AAPL\"\n        />\n      </div>\n      \n      <div className=\"input-group\">\n        <label>Hours Back:</label>\n        <select value={sentimentHours} onChange={(e) => setSentimentHours(e.target.value)}>\n          <option value={6}>6 hours</option>\n          <option value={12}>12 hours</option>\n          <option value={24}>24 hours</option>\n          <option value={48}>48 hours</option>\n        </select>\n      </div>\n      \n      <button onClick={analyzeSentiment} disabled={loading} className=\"ai-button\">\n        {loading ? 'Analyzing...' : 'Analyze Sentiment'}\n      </button>\n      \n      {results.sentiment && (\n        <div className=\"results-section\">\n          <h4>Sentiment Analysis Results</h4>\n          <div className=\"sentiment-summary\">\n            {results.sentiment.sentiment_analysis?.analysis_summary && (\n              <>\n                <div className=\"sentiment-item\">\n                  <strong>Overall Sentiment:</strong> {results.sentiment.sentiment_analysis.analysis_summary.overall_sentiment}\n                </div>\n                <div className=\"sentiment-item\">\n                  <strong>Crowd Mood:</strong> {results.sentiment.sentiment_analysis.analysis_summary.crowd_mood}\n                </div>\n                <div className=\"sentiment-item\">\n                  <strong>Posts Analyzed:</strong> {results.sentiment.sentiment_analysis.analysis_summary.total_posts_analyzed}\n                </div>\n              </>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Insights:</h5>\n            <p>{results.sentiment.holly_insights}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderEmbeddingsTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>🧠 Market Embeddings</h3>\n      <p>Advanced pattern matching and market context analysis</p>\n      \n      <div className=\"input-group\">\n        <label>Primary Symbol:</label>\n        <input\n          type=\"text\"\n          value={embeddingSymbol}\n          onChange={(e) => setEmbeddingSymbol(e.target.value)}\n          placeholder=\"AAPL\"\n        />\n      </div>\n      \n      <div className=\"input-group\">\n        <label>Context Symbols:</label>\n        <input\n          type=\"text\"\n          value={embeddingContext}\n          onChange={(e) => setEmbeddingContext(e.target.value)}\n          placeholder=\"SPY,QQQ,VIX,TLT\"\n        />\n      </div>\n      \n      <button onClick={generateEmbeddings} disabled={loading} className=\"ai-button\">\n        {loading ? 'Generating...' : 'Generate Embeddings'}\n      </button>\n      \n      {results.embeddings && (\n        <div className=\"results-section\">\n          <h4>Embedding Analysis Results</h4>\n          <div className=\"embeddings-summary\">\n            {results.embeddings.embedding_analysis?.pattern_analysis && (\n              <>\n                <div className=\"embedding-item\">\n                  <strong>Similar Patterns:</strong> {results.embeddings.embedding_analysis.pattern_analysis.similar_patterns_found}\n                </div>\n                <div className=\"embedding-item\">\n                  <strong>Pattern Confidence:</strong> {(results.embeddings.embedding_analysis.pattern_analysis.pattern_confidence * 100).toFixed(1)}%\n                </div>\n              </>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Insights:</h5>\n            <p>{results.embeddings.holly_insights}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderTTMTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>📈 TTM Squeeze Scanner</h3>\n      <p>AI-enhanced TTM Squeeze pattern recognition</p>\n      \n      <div className=\"input-group\">\n        <label>Symbols to Scan:</label>\n        <input\n          type=\"text\"\n          value={ttmSymbols}\n          onChange={(e) => setTtmSymbols(e.target.value)}\n          placeholder=\"AAPL,TSLA,NVDA\"\n        />\n      </div>\n      \n      <div className=\"input-group\">\n        <label>Timeframe:</label>\n        <select value={ttmTimeframe} onChange={(e) => setTtmTimeframe(e.target.value)}>\n          <option value=\"1min\">1 minute</option>\n          <option value=\"5min\">5 minutes</option>\n          <option value=\"15min\">15 minutes</option>\n          <option value=\"30min\">30 minutes</option>\n          <option value=\"1hour\">1 hour</option>\n        </select>\n      </div>\n      \n      <button onClick={scanTTM} disabled={loading} className=\"ai-button\">\n        {loading ? 'Scanning...' : 'Scan TTM Signals'}\n      </button>\n      \n      {results.ttm && (\n        <div className=\"results-section\">\n          <h4>TTM Scan Results</h4>\n          <div className=\"ttm-summary\">\n            {results.ttm.scan_results?.signals_found !== undefined && (\n              <div className=\"ttm-item\">\n                <strong>Signals Found:</strong> {results.ttm.scan_results.signals_found}\n              </div>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Analysis:</h5>\n            <p>{results.ttm.holly_analysis}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderNewsTab = () => (\n    <div className=\"ai-tab-content\">\n      <h3>🔍 News Search</h3>\n      <p>Search current market news and events</p>\n      \n      <div className=\"input-group\">\n        <label>Search Query:</label>\n        <input\n          type=\"text\"\n          value={newsQuery}\n          onChange={(e) => setNewsQuery(e.target.value)}\n          placeholder=\"Apple earnings, Fed meeting, market news\"\n        />\n      </div>\n      \n      <button onClick={searchNews} disabled={loading} className=\"ai-button\">\n        {loading ? 'Searching...' : 'Search News'}\n      </button>\n      \n      {results.news && (\n        <div className=\"results-section\">\n          <h4>News Search Results</h4>\n          <div className=\"news-summary\">\n            {results.news.news_results?.total_results && (\n              <div className=\"news-item\">\n                <strong>Results Found:</strong> {results.news.news_results.total_results}\n              </div>\n            )}\n          </div>\n          <div className=\"holly-interpretation\">\n            <h5>Holly's Analysis:</h5>\n            <p>{results.news.holly_analysis}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"ai-features-container\">\n      <div className=\"ai-header\">\n        <h2>🤖 Holly AI Advanced Features</h2>\n        <p>Explore Holly's advanced AI capabilities for market analysis</p>\n      </div>\n\n      <div className=\"ai-tabs\">\n        <button \n          className={`tab-button ${activeTab === 'regime' ? 'active' : ''}`}\n          onClick={() => setActiveTab('regime')}\n        >\n          🌊 Regime\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'sentiment' ? 'active' : ''}`}\n          onClick={() => setActiveTab('sentiment')}\n        >\n          📱 Sentiment\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'embeddings' ? 'active' : ''}`}\n          onClick={() => setActiveTab('embeddings')}\n        >\n          🧠 Embeddings\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'ttm' ? 'active' : ''}`}\n          onClick={() => setActiveTab('ttm')}\n        >\n          📈 TTM Scan\n        </button>\n        <button \n          className={`tab-button ${activeTab === 'news' ? 'active' : ''}`}\n          onClick={() => setActiveTab('news')}\n        >\n          🔍 News\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"error-message\">\n          ❌ {error}\n        </div>\n      )}\n\n      <div className=\"ai-content\">\n        {activeTab === 'regime' && renderRegimeTab()}\n        {activeTab === 'sentiment' && renderSentimentTab()}\n        {activeTab === 'embeddings' && renderEmbeddingsTab()}\n        {activeTab === 'ttm' && renderTTMTab()}\n        {activeTab === 'news' && renderNewsTab()}\n      </div>\n    </div>\n  );\n};\n\nexport default AIFeatures;\n"], "mappings": "iIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1B,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGV,QAAQ,CAAC,QAAQ,CAAC,CACpD,KAAM,CAACW,OAAO,CAAEC,UAAU,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACa,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC1C,KAAM,CAACe,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAACiB,aAAa,CAAEC,gBAAgB,CAAC,CAAGlB,QAAQ,CAAC,aAAa,CAAC,CACjE,KAAM,CAACmB,eAAe,CAAEC,kBAAkB,CAAC,CAAGpB,QAAQ,CAAC,MAAM,CAAC,CAC9D,KAAM,CAACqB,cAAc,CAAEC,iBAAiB,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACuB,eAAe,CAAEC,kBAAkB,CAAC,CAAGxB,QAAQ,CAAC,MAAM,CAAC,CAC9D,KAAM,CAACyB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1B,QAAQ,CAAC,iBAAiB,CAAC,CAC3E,KAAM,CAAC2B,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAC,gBAAgB,CAAC,CAC9D,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,MAAM,CAAC,CACxD,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,mBAAmB,CAAC,CAE/D,KAAM,CAAAiC,QAAQ,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAEzE,KAAM,CAAAC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCzB,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIP,QAAQ,8BAAAO,MAAA,CAA4BvB,aAAa,CAAE,CAAC,CACpF,KAAM,CAAAwB,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC5B,UAAU,CAAC6B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEE,MAAM,CAAEJ,IAAI,EAAG,CAAC,CACjD,CAAE,MAAOK,GAAG,CAAE,CACZ9B,QAAQ,CAAC,iCAAiC,CAAC,CAC7C,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmC,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnCnC,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIP,QAAQ,yBAAAO,MAAA,CAAuBrB,eAAe,iBAAAqB,MAAA,CAAenB,cAAc,CAAE,CAAC,CAC9G,KAAM,CAAAoB,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC5B,UAAU,CAAC6B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEK,SAAS,CAAEP,IAAI,EAAG,CAAC,CACpD,CAAE,MAAOK,GAAG,CAAE,CACZ9B,QAAQ,CAAC,6BAA6B,CAAC,CACzC,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAqC,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrCrC,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIP,QAAQ,0BAAAO,MAAA,CAAwBjB,eAAe,cAAAiB,MAAA,CAAYf,gBAAgB,CAAE,CAAC,CAC9G,KAAM,CAAAgB,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC5B,UAAU,CAAC6B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEO,UAAU,CAAET,IAAI,EAAG,CAAC,CACrD,CAAE,MAAOK,GAAG,CAAE,CACZ9B,QAAQ,CAAC,+BAA+B,CAAC,CAC3C,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAuC,OAAO,CAAG,KAAAA,CAAA,GAAY,CAC1BvC,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIP,QAAQ,gCAAAO,MAAA,CAA8Bb,UAAU,gBAAAa,MAAA,CAAcX,YAAY,CAAE,CAAC,CAC7G,KAAM,CAAAY,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC5B,UAAU,CAAC6B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAES,GAAG,CAAEX,IAAI,EAAG,CAAC,CAC9C,CAAE,MAAOK,GAAG,CAAE,CACZ9B,QAAQ,CAAC,4BAA4B,CAAC,CACxC,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAyC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7BzC,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIP,QAAQ,iCAAAO,MAAA,CAA+Bc,kBAAkB,CAACvB,SAAS,CAAC,CAAE,CAAC,CACvG,KAAM,CAAAU,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC5B,UAAU,CAAC6B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEY,IAAI,CAAEd,IAAI,EAAG,CAAC,CAC/C,CAAE,MAAOK,GAAG,CAAE,CACZ9B,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4C,eAAe,CAAGA,CAAA,QAAAC,qBAAA,oBACtBpD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,OAAAwD,QAAA,CAAI,sCAA0B,CAAI,CAAC,cACnCxD,IAAA,MAAAwD,QAAA,CAAG,6DAA2D,CAAG,CAAC,cAElEtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,qBAAmB,CAAO,CAAC,cAClCxD,IAAA,UACEyD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE5C,aAAc,CACrB6C,QAAQ,CAAGC,CAAC,EAAK7C,gBAAgB,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDI,WAAW,CAAC,aAAa,CAC1B,CAAC,EACC,CAAC,cAEN9D,IAAA,WAAQ+D,OAAO,CAAE7B,aAAc,CAAC8B,QAAQ,CAAExD,OAAQ,CAAC+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACrEhD,OAAO,CAAG,cAAc,CAAG,uBAAuB,CAC7C,CAAC,CAERE,OAAO,CAACgC,MAAM,eACbxC,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxD,IAAA,OAAAwD,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCxD,IAAA,QAAKuD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5B,EAAAF,qBAAA,CAAA5C,OAAO,CAACgC,MAAM,CAACuB,eAAe,UAAAX,qBAAA,iBAA9BA,qBAAA,CAAgCY,sBAAsB,gBACrDhE,KAAA,CAAAE,SAAA,EAAAoD,QAAA,eACEtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,WAAAwD,QAAA,CAAQ,iBAAe,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAACgC,MAAM,CAACuB,eAAe,CAACC,sBAAsB,CAACC,cAAc,EACnG,CAAC,cACNjE,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,WAAAwD,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAAC,CAAC9C,OAAO,CAACgC,MAAM,CAACuB,eAAe,CAACC,sBAAsB,CAACE,UAAU,CAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,GACpH,EAAK,CAAC,cACNnE,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,WAAAwD,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAACgC,MAAM,CAACuB,eAAe,CAACC,sBAAsB,CAACI,oBAAoB,CAAC,OACzG,EAAK,CAAC,EACN,CACH,CACE,CAAC,cACNpE,KAAA,QAAKqD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCxD,IAAA,OAAAwD,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BxD,IAAA,MAAAwD,QAAA,CAAI9C,OAAO,CAACgC,MAAM,CAAC6B,oBAAoB,CAAI,CAAC,EACzC,CAAC,EACH,CACN,EACE,CAAC,EACP,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,QAAAC,qBAAA,oBACzBvE,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,OAAAwD,QAAA,CAAI,wCAA4B,CAAI,CAAC,cACrCxD,IAAA,MAAAwD,QAAA,CAAG,gDAA8C,CAAG,CAAC,cAErDtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,SAAO,CAAO,CAAC,cACtBxD,IAAA,UACEyD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE1C,eAAgB,CACvB2C,QAAQ,CAAGC,CAAC,EAAK3C,kBAAkB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDI,WAAW,CAAC,MAAM,CACnB,CAAC,EACC,CAAC,cAEN5D,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1BtD,KAAA,WAAQwD,KAAK,CAAExC,cAAe,CAACyC,QAAQ,CAAGC,CAAC,EAAKzC,iBAAiB,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAF,QAAA,eAChFxD,IAAA,WAAQ0D,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,SAAO,CAAQ,CAAC,cAClCxD,IAAA,WAAQ0D,KAAK,CAAE,EAAG,CAAAF,QAAA,CAAC,UAAQ,CAAQ,CAAC,cACpCxD,IAAA,WAAQ0D,KAAK,CAAE,EAAG,CAAAF,QAAA,CAAC,UAAQ,CAAQ,CAAC,cACpCxD,IAAA,WAAQ0D,KAAK,CAAE,EAAG,CAAAF,QAAA,CAAC,UAAQ,CAAQ,CAAC,EAC9B,CAAC,EACN,CAAC,cAENxD,IAAA,WAAQ+D,OAAO,CAAEnB,gBAAiB,CAACoB,QAAQ,CAAExD,OAAQ,CAAC+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACxEhD,OAAO,CAAG,cAAc,CAAG,mBAAmB,CACzC,CAAC,CAERE,OAAO,CAACmC,SAAS,eAChB3C,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxD,IAAA,OAAAwD,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnCxD,IAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC/B,EAAAiB,qBAAA,CAAA/D,OAAO,CAACmC,SAAS,CAAC6B,kBAAkB,UAAAD,qBAAA,iBAApCA,qBAAA,CAAsCE,gBAAgB,gBACrDzE,KAAA,CAAAE,SAAA,EAAAoD,QAAA,eACEtD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,WAAAwD,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAACmC,SAAS,CAAC6B,kBAAkB,CAACC,gBAAgB,CAACC,iBAAiB,EACzG,CAAC,cACN1E,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,WAAAwD,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAACmC,SAAS,CAAC6B,kBAAkB,CAACC,gBAAgB,CAACE,UAAU,EAC3F,CAAC,cACN3E,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,WAAAwD,QAAA,CAAQ,iBAAe,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAACmC,SAAS,CAAC6B,kBAAkB,CAACC,gBAAgB,CAACG,oBAAoB,EACzG,CAAC,EACN,CACH,CACE,CAAC,cACN5E,KAAA,QAAKqD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCxD,IAAA,OAAAwD,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BxD,IAAA,MAAAwD,QAAA,CAAI9C,OAAO,CAACmC,SAAS,CAACkC,cAAc,CAAI,CAAC,EACtC,CAAC,EACH,CACN,EACE,CAAC,EACP,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,QAAAC,qBAAA,oBAC1B/E,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,OAAAwD,QAAA,CAAI,gCAAoB,CAAI,CAAC,cAC7BxD,IAAA,MAAAwD,QAAA,CAAG,uDAAqD,CAAG,CAAC,cAE5DtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,iBAAe,CAAO,CAAC,cAC9BxD,IAAA,UACEyD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEtC,eAAgB,CACvBuC,QAAQ,CAAGC,CAAC,EAAKvC,kBAAkB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDI,WAAW,CAAC,MAAM,CACnB,CAAC,EACC,CAAC,cAEN5D,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,kBAAgB,CAAO,CAAC,cAC/BxD,IAAA,UACEyD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEpC,gBAAiB,CACxBqC,QAAQ,CAAGC,CAAC,EAAKrC,mBAAmB,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDI,WAAW,CAAC,iBAAiB,CAC9B,CAAC,EACC,CAAC,cAEN9D,IAAA,WAAQ+D,OAAO,CAAEjB,kBAAmB,CAACkB,QAAQ,CAAExD,OAAQ,CAAC+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAC1EhD,OAAO,CAAG,eAAe,CAAG,qBAAqB,CAC5C,CAAC,CAERE,OAAO,CAACqC,UAAU,eACjB7C,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxD,IAAA,OAAAwD,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnCxD,IAAA,QAAKuD,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChC,EAAAyB,qBAAA,CAAAvE,OAAO,CAACqC,UAAU,CAACmC,kBAAkB,UAAAD,qBAAA,iBAArCA,qBAAA,CAAuCE,gBAAgB,gBACtDjF,KAAA,CAAAE,SAAA,EAAAoD,QAAA,eACEtD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,WAAAwD,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAACqC,UAAU,CAACmC,kBAAkB,CAACC,gBAAgB,CAACC,sBAAsB,EAC9G,CAAC,cACNlF,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,WAAAwD,QAAA,CAAQ,qBAAmB,CAAQ,CAAC,IAAC,CAAC,CAAC9C,OAAO,CAACqC,UAAU,CAACmC,kBAAkB,CAACC,gBAAgB,CAACE,kBAAkB,CAAG,GAAG,EAAEhB,OAAO,CAAC,CAAC,CAAC,CAAC,GACrI,EAAK,CAAC,EACN,CACH,CACE,CAAC,cACNnE,KAAA,QAAKqD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCxD,IAAA,OAAAwD,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BxD,IAAA,MAAAwD,QAAA,CAAI9C,OAAO,CAACqC,UAAU,CAACgC,cAAc,CAAI,CAAC,EACvC,CAAC,EACH,CACN,EACE,CAAC,EACP,CAED,KAAM,CAAAO,YAAY,CAAGA,CAAA,QAAAC,qBAAA,oBACnBrF,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,OAAAwD,QAAA,CAAI,kCAAsB,CAAI,CAAC,cAC/BxD,IAAA,MAAAwD,QAAA,CAAG,6CAA2C,CAAG,CAAC,cAElDtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,kBAAgB,CAAO,CAAC,cAC/BxD,IAAA,UACEyD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAElC,UAAW,CAClBmC,QAAQ,CAAGC,CAAC,EAAKnC,aAAa,CAACmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,WAAW,CAAC,gBAAgB,CAC7B,CAAC,EACC,CAAC,cAEN5D,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,YAAU,CAAO,CAAC,cACzBtD,KAAA,WAAQwD,KAAK,CAAEhC,YAAa,CAACiC,QAAQ,CAAGC,CAAC,EAAKjC,eAAe,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAF,QAAA,eAC5ExD,IAAA,WAAQ0D,KAAK,CAAC,MAAM,CAAAF,QAAA,CAAC,UAAQ,CAAQ,CAAC,cACtCxD,IAAA,WAAQ0D,KAAK,CAAC,MAAM,CAAAF,QAAA,CAAC,WAAS,CAAQ,CAAC,cACvCxD,IAAA,WAAQ0D,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,YAAU,CAAQ,CAAC,cACzCxD,IAAA,WAAQ0D,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,YAAU,CAAQ,CAAC,cACzCxD,IAAA,WAAQ0D,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,QAAM,CAAQ,CAAC,EAC/B,CAAC,EACN,CAAC,cAENxD,IAAA,WAAQ+D,OAAO,CAAEf,OAAQ,CAACgB,QAAQ,CAAExD,OAAQ,CAAC+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAC/DhD,OAAO,CAAG,aAAa,CAAG,kBAAkB,CACvC,CAAC,CAERE,OAAO,CAACuC,GAAG,eACV/C,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxD,IAAA,OAAAwD,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBxD,IAAA,QAAKuD,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzB,EAAA+B,qBAAA,CAAA7E,OAAO,CAACuC,GAAG,CAACuC,YAAY,UAAAD,qBAAA,iBAAxBA,qBAAA,CAA0BE,aAAa,IAAKC,SAAS,eACpDxF,KAAA,QAAKqD,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBxD,IAAA,WAAAwD,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAACuC,GAAG,CAACuC,YAAY,CAACC,aAAa,EACpE,CACN,CACE,CAAC,cACNvF,KAAA,QAAKqD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCxD,IAAA,OAAAwD,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BxD,IAAA,MAAAwD,QAAA,CAAI9C,OAAO,CAACuC,GAAG,CAAC0C,cAAc,CAAI,CAAC,EAChC,CAAC,EACH,CACN,EACE,CAAC,EACP,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,QAAAC,qBAAA,oBACpB3F,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BxD,IAAA,OAAAwD,QAAA,CAAI,0BAAc,CAAI,CAAC,cACvBxD,IAAA,MAAAwD,QAAA,CAAG,uCAAqC,CAAG,CAAC,cAE5CtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,UAAAwD,QAAA,CAAO,eAAa,CAAO,CAAC,cAC5BxD,IAAA,UACEyD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE9B,SAAU,CACjB+B,QAAQ,CAAGC,CAAC,EAAK/B,YAAY,CAAC+B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9CI,WAAW,CAAC,0CAA0C,CACvD,CAAC,EACC,CAAC,cAEN9D,IAAA,WAAQ+D,OAAO,CAAEb,UAAW,CAACc,QAAQ,CAAExD,OAAQ,CAAC+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAClEhD,OAAO,CAAG,cAAc,CAAG,aAAa,CACnC,CAAC,CAERE,OAAO,CAAC0C,IAAI,eACXlD,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxD,IAAA,OAAAwD,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BxD,IAAA,QAAKuD,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1B,EAAAqC,qBAAA,CAAAnF,OAAO,CAAC0C,IAAI,CAAC0C,YAAY,UAAAD,qBAAA,iBAAzBA,qBAAA,CAA2BE,aAAa,gBACvC7F,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxD,IAAA,WAAAwD,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAAC9C,OAAO,CAAC0C,IAAI,CAAC0C,YAAY,CAACC,aAAa,EACrE,CACN,CACE,CAAC,cACN7F,KAAA,QAAKqD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCxD,IAAA,OAAAwD,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BxD,IAAA,MAAAwD,QAAA,CAAI9C,OAAO,CAAC0C,IAAI,CAACuC,cAAc,CAAI,CAAC,EACjC,CAAC,EACH,CACN,EACE,CAAC,EACP,CAED,mBACEzF,KAAA,QAAKqD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCtD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxD,IAAA,OAAAwD,QAAA,CAAI,yCAA6B,CAAI,CAAC,cACtCxD,IAAA,MAAAwD,QAAA,CAAG,8DAA4D,CAAG,CAAC,EAChE,CAAC,cAENtD,KAAA,QAAKqD,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBxD,IAAA,WACEuD,SAAS,eAAAlB,MAAA,CAAgB/B,SAAS,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAE,CAAG,CAClEyD,OAAO,CAAEA,CAAA,GAAMxD,YAAY,CAAC,QAAQ,CAAE,CAAAiD,QAAA,CACvC,qBAED,CAAQ,CAAC,cACTxD,IAAA,WACEuD,SAAS,eAAAlB,MAAA,CAAgB/B,SAAS,GAAK,WAAW,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrEyD,OAAO,CAAEA,CAAA,GAAMxD,YAAY,CAAC,WAAW,CAAE,CAAAiD,QAAA,CAC1C,wBAED,CAAQ,CAAC,cACTxD,IAAA,WACEuD,SAAS,eAAAlB,MAAA,CAAgB/B,SAAS,GAAK,YAAY,CAAG,QAAQ,CAAG,EAAE,CAAG,CACtEyD,OAAO,CAAEA,CAAA,GAAMxD,YAAY,CAAC,YAAY,CAAE,CAAAiD,QAAA,CAC3C,yBAED,CAAQ,CAAC,cACTxD,IAAA,WACEuD,SAAS,eAAAlB,MAAA,CAAgB/B,SAAS,GAAK,KAAK,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC/DyD,OAAO,CAAEA,CAAA,GAAMxD,YAAY,CAAC,KAAK,CAAE,CAAAiD,QAAA,CACpC,uBAED,CAAQ,CAAC,cACTxD,IAAA,WACEuD,SAAS,eAAAlB,MAAA,CAAgB/B,SAAS,GAAK,MAAM,CAAG,QAAQ,CAAG,EAAE,CAAG,CAChEyD,OAAO,CAAEA,CAAA,GAAMxD,YAAY,CAAC,MAAM,CAAE,CAAAiD,QAAA,CACrC,mBAED,CAAQ,CAAC,EACN,CAAC,CAEL5C,KAAK,eACJV,KAAA,QAAKqD,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,SAC3B,CAAC5C,KAAK,EACL,CACN,cAEDV,KAAA,QAAKqD,SAAS,CAAC,YAAY,CAAAC,QAAA,EACxBlD,SAAS,GAAK,QAAQ,EAAI+C,eAAe,CAAC,CAAC,CAC3C/C,SAAS,GAAK,WAAW,EAAIkE,kBAAkB,CAAC,CAAC,CACjDlE,SAAS,GAAK,YAAY,EAAI0E,mBAAmB,CAAC,CAAC,CACnD1E,SAAS,GAAK,KAAK,EAAIgF,YAAY,CAAC,CAAC,CACrChF,SAAS,GAAK,MAAM,EAAIsF,aAAa,CAAC,CAAC,EACrC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}