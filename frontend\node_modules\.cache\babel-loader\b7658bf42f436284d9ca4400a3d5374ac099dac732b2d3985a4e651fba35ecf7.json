{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\AtlasInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { createChart } from 'lightweight-charts';\nimport { Send, TrendingUp, BarChart3, Sparkles } from 'lucide-react';\nimport SpaceBackground from './SpaceBackground';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AtlasInterface = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: 'system',\n    content: \"Certainly! Here is the latest stock quote for AAPL.\",\n    timestamp: new Date()\n  }, {\n    id: 2,\n    type: 'stock-quote',\n    symbol: 'AAPL',\n    price: 149.36,\n    change: 1.32,\n    changePercent: 0.89,\n    company: 'Apple Inc.',\n    chartData: generateMockChartData(),\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const chartRef = useRef(null);\n  const chartInstanceRef = useRef(null);\n\n  // Initialize chart when stock quote message is rendered\n  useEffect(() => {\n    const stockMessage = messages.find(msg => msg.type === 'stock-quote');\n    if (stockMessage && chartRef.current && !chartInstanceRef.current) {\n      initializeChart(stockMessage.chartData);\n    }\n  }, [messages]);\n  const initializeChart = data => {\n    if (chartInstanceRef.current) {\n      chartInstanceRef.current.remove();\n    }\n    const chart = createChart(chartRef.current, {\n      width: 280,\n      height: 120,\n      layout: {\n        background: {\n          color: 'transparent'\n        },\n        textColor: '#67e8f9'\n      },\n      grid: {\n        vertLines: {\n          color: 'rgba(6, 182, 212, 0.1)'\n        },\n        horzLines: {\n          color: 'rgba(6, 182, 212, 0.1)'\n        }\n      },\n      crosshair: {\n        mode: 0\n      },\n      rightPriceScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9'\n      },\n      timeScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9',\n        timeVisible: false,\n        secondsVisible: false\n      }\n    });\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#22d3ee',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#22d3ee',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#22d3ee'\n    });\n    candlestickSeries.setData(data);\n    chartInstanceRef.current = chart;\n\n    // Auto-fit content\n    chart.timeScale().fitContent();\n  };\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n    const newMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, newMessage]);\n    const messageToSend = inputMessage;\n    setInputMessage('');\n    setIsTyping(true);\n    try {\n      // Call the real Holly AI backend\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'atlas_session',\n            interface: 'atlas'\n          }\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      setIsTyping(false);\n\n      // Create Holly's response message\n      const hollyResponse = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: data.response || \"I'm having trouble processing that request right now.\",\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n      setMessages(prev => [...prev, hollyResponse]);\n\n      // If Holly provided a stock quote, add it as a separate message\n      if (data.type === 'stock_quote' && data.trading_plan) {\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: data.trading_plan.symbol || 'UNKNOWN',\n          price: data.trading_plan.current_price || 0,\n          change: data.trading_plan.price_change || 0,\n          changePercent: data.trading_plan.price_change_percent || 0,\n          company: data.trading_plan.company_name || 'Unknown Company',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      }\n    } catch (error) {\n      console.error('Error calling Holly AI:', error);\n      setIsTyping(false);\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: \"I'm having trouble connecting to my AI brain right now. Please try again in a moment.\",\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen space-bg relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(SpaceBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex items-center justify-center min-h-screen p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-8 h-8 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-2\",\n            children: \"H.O.L.L.Y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-cyan-300/80 text-sm\",\n            children: \"Stock Analysis Chatbot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.4\n          },\n          className: \"glass-card p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 max-h-96 overflow-y-auto\",\n            children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -10\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: [message.type === 'system' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-cyan-100 text-sm leading-relaxed\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 23\n                }, this), message.type === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm\",\n                    children: message.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this), message.type === 'stock-quote' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-2xl p-4 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: message.symbol\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-cyan-300/80 text-sm\",\n                        children: message.company\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: message.price\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center text-sm text-green-400\",\n                        children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                          className: \"w-4 h-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 257,\n                          columnNumber: 31\n                        }, this), \"+\", message.change, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-black/20 rounded-xl p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      ref: chartRef,\n                      className: \"w-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\",\n                      children: \"Show earnings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\",\n                      children: \"Analyze trend\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this)]\n              }, message.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -10\n                },\n                className: \"flex items-center space-x-2 text-cyan-400\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.1s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"H.O.L.L.Y is typing...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: inputMessage,\n              onChange: e => setInputMessage(e.target.value),\n              onKeyPress: handleKeyPress,\n              placeholder: \"Send a message...\",\n              className: \"w-full bg-black/20 border border-cyan-500/30 rounded-2xl px-4 py-3 pr-12 text-white placeholder-cyan-300/50 focus:outline-none focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSendMessage,\n              className: \"absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center hover:scale-105 transition-transform duration-200 btn-glow\",\n              children: /*#__PURE__*/_jsxDEV(Send, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n\n// Generate mock chart data\n_s(AtlasInterface, \"yppJNJt70J5qrbHD3s+ZVL/gCJU=\");\n_c = AtlasInterface;\nfunction generateMockChartData() {\n  const data = [];\n  let basePrice = 148;\n  for (let i = 0; i < 50; i++) {\n    const time = Math.floor(Date.now() / 1000) - (50 - i) * 3600;\n    const volatility = 0.02;\n    const change = (Math.random() - 0.5) * volatility * basePrice;\n    const open = basePrice;\n    const close = basePrice + change;\n    const high = Math.max(open, close) + Math.random() * 0.5;\n    const low = Math.min(open, close) - Math.random() * 0.5;\n    data.push({\n      time,\n      open: parseFloat(open.toFixed(2)),\n      high: parseFloat(high.toFixed(2)),\n      low: parseFloat(low.toFixed(2)),\n      close: parseFloat(close.toFixed(2))\n    });\n    basePrice = close;\n  }\n  return data;\n}\nexport default AtlasInterface;\nvar _c;\n$RefreshReg$(_c, \"AtlasInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "createChart", "Send", "TrendingUp", "BarChart3", "<PERSON><PERSON><PERSON>", "SpaceBackground", "jsxDEV", "_jsxDEV", "AtlasInterface", "_s", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "symbol", "price", "change", "changePercent", "company", "chartData", "generateMockChartData", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "chartRef", "chartInstanceRef", "stockMessage", "find", "msg", "current", "initializeChart", "data", "remove", "chart", "width", "height", "layout", "background", "color", "textColor", "grid", "vertLines", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "candlestickSeries", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "setData", "<PERSON><PERSON><PERSON><PERSON>", "handleSendMessage", "trim", "newMessage", "now", "prev", "messageToSend", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "message", "user_context", "toISOString", "session_id", "interface", "ok", "Error", "status", "json", "hollyResponse", "response_type", "requires_action", "trading_plan", "plan_id", "function_called", "current_price", "price_change", "price_change_percent", "company_name", "error", "console", "errorMessage", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "scale", "map", "exit", "ref", "style", "animationDelay", "value", "onChange", "target", "onKeyPress", "placeholder", "onClick", "_c", "basePrice", "i", "time", "Math", "floor", "volatility", "random", "open", "close", "high", "max", "low", "min", "push", "parseFloat", "toFixed", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AtlasInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { createChart } from 'lightweight-charts';\nimport { Send, TrendingUp, BarChart3, Sparkles } from 'lucide-react';\nimport SpaceBackground from './SpaceBackground';\n\nconst AtlasInterface = () => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: 'system',\n      content: \"Certainly! Here is the latest stock quote for AAPL.\",\n      timestamp: new Date()\n    },\n    {\n      id: 2,\n      type: 'stock-quote',\n      symbol: 'AAPL',\n      price: 149.36,\n      change: 1.32,\n      changePercent: 0.89,\n      company: 'Apple Inc.',\n      chartData: generateMockChartData(),\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const chartRef = useRef(null);\n  const chartInstanceRef = useRef(null);\n\n  // Initialize chart when stock quote message is rendered\n  useEffect(() => {\n    const stockMessage = messages.find(msg => msg.type === 'stock-quote');\n    if (stockMessage && chartRef.current && !chartInstanceRef.current) {\n      initializeChart(stockMessage.chartData);\n    }\n  }, [messages]);\n\n  const initializeChart = (data) => {\n    if (chartInstanceRef.current) {\n      chartInstanceRef.current.remove();\n    }\n\n    const chart = createChart(chartRef.current, {\n      width: 280,\n      height: 120,\n      layout: {\n        background: { color: 'transparent' },\n        textColor: '#67e8f9',\n      },\n      grid: {\n        vertLines: { color: 'rgba(6, 182, 212, 0.1)' },\n        horzLines: { color: 'rgba(6, 182, 212, 0.1)' },\n      },\n      crosshair: {\n        mode: 0,\n      },\n      rightPriceScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9',\n      },\n      timeScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9',\n        timeVisible: false,\n        secondsVisible: false,\n      },\n    });\n\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#22d3ee',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#22d3ee',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#22d3ee',\n    });\n\n    candlestickSeries.setData(data);\n    chartInstanceRef.current = chart;\n\n    // Auto-fit content\n    chart.timeScale().fitContent();\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const newMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    const messageToSend = inputMessage;\n    setInputMessage('');\n    setIsTyping(true);\n\n    try {\n      // Call the real Holly AI backend\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'atlas_session',\n            interface: 'atlas'\n          }\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      setIsTyping(false);\n\n      // Create Holly's response message\n      const hollyResponse = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: data.response || \"I'm having trouble processing that request right now.\",\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n\n      setMessages(prev => [...prev, hollyResponse]);\n\n      // If Holly provided a stock quote, add it as a separate message\n      if (data.type === 'stock_quote' && data.trading_plan) {\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: data.trading_plan.symbol || 'UNKNOWN',\n          price: data.trading_plan.current_price || 0,\n          change: data.trading_plan.price_change || 0,\n          changePercent: data.trading_plan.price_change_percent || 0,\n          company: data.trading_plan.company_name || 'Unknown Company',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      }\n\n    } catch (error) {\n      console.error('Error calling Holly AI:', error);\n      setIsTyping(false);\n\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: \"I'm having trouble connecting to my AI brain right now. Please try again in a moment.\",\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen space-bg relative overflow-hidden\">\n      <SpaceBackground />\n      \n      {/* Main Container */}\n      <div className=\"relative z-10 flex items-center justify-center min-h-screen p-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"w-full max-w-md\"\n        >\n          {/* Header */}\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"text-center mb-8\"\n          >\n            <div className=\"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 mb-4\">\n              <Sparkles className=\"w-8 h-8 text-cyan-400\" />\n            </div>\n            <h1 className=\"text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-2\">\n              H.O.L.L.Y\n            </h1>\n            <p className=\"text-cyan-300/80 text-sm\">\n              Stock Analysis Chatbot\n            </p>\n          </motion.div>\n\n          {/* Chat Container */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"glass-card p-6 space-y-6\"\n          >\n            {/* Messages */}\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              <AnimatePresence>\n                {messages.map((message) => (\n                  <motion.div\n                    key={message.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    {message.type === 'system' && (\n                      <div className=\"text-cyan-100 text-sm leading-relaxed\">\n                        {message.content}\n                      </div>\n                    )}\n\n                    {message.type === 'user' && (\n                      <div className=\"text-right\">\n                        <div className=\"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm\">\n                          {message.content}\n                        </div>\n                      </div>\n                    )}\n\n                    {message.type === 'stock-quote' && (\n                      <div className=\"bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-2xl p-4 space-y-4\">\n                        {/* Stock Header */}\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"text-2xl font-bold text-white\">\n                              {message.symbol}\n                            </div>\n                            <div className=\"text-cyan-300/80 text-sm\">\n                              {message.company}\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-2xl font-bold text-white\">\n                              {message.price}\n                            </div>\n                            <div className=\"flex items-center text-sm text-green-400\">\n                              <TrendingUp className=\"w-4 h-4 mr-1\" />\n                              +{message.change}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Chart */}\n                        <div className=\"bg-black/20 rounded-xl p-3\">\n                          <div ref={chartRef} className=\"w-full\" />\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex gap-2\">\n                          <button className=\"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\">\n                            Show earnings\n                          </button>\n                          <button className=\"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\">\n                            Analyze trend\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n\n              {/* Typing Indicator */}\n              <AnimatePresence>\n                {isTyping && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"flex items-center space-x-2 text-cyan-400\"\n                  >\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                    </div>\n                    <span className=\"text-sm\">H.O.L.L.Y is typing...</span>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Input Area */}\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={inputMessage}\n                onChange={(e) => setInputMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Send a message...\"\n                className=\"w-full bg-black/20 border border-cyan-500/30 rounded-2xl px-4 py-3 pr-12 text-white placeholder-cyan-300/50 focus:outline-none focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-200\"\n              />\n              <button\n                onClick={handleSendMessage}\n                className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center hover:scale-105 transition-transform duration-200 btn-glow\"\n              >\n                <Send className=\"w-4 h-4 text-white\" />\n              </button>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\n// Generate mock chart data\nfunction generateMockChartData() {\n  const data = [];\n  let basePrice = 148;\n  \n  for (let i = 0; i < 50; i++) {\n    const time = Math.floor(Date.now() / 1000) - (50 - i) * 3600;\n    const volatility = 0.02;\n    const change = (Math.random() - 0.5) * volatility * basePrice;\n    \n    const open = basePrice;\n    const close = basePrice + change;\n    const high = Math.max(open, close) + Math.random() * 0.5;\n    const low = Math.min(open, close) - Math.random() * 0.5;\n    \n    data.push({\n      time,\n      open: parseFloat(open.toFixed(2)),\n      high: parseFloat(high.toFixed(2)),\n      low: parseFloat(low.toFixed(2)),\n      close: parseFloat(close.toFixed(2)),\n    });\n    \n    basePrice = close;\n  }\n  \n  return data;\n}\n\nexport default AtlasInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,cAAc;AACpE,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CACvC;IACEiB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,qDAAqD;IAC9DC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBI,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAEC,qBAAqB,CAAC,CAAC;IAClCR,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMiC,QAAQ,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMgC,gBAAgB,GAAGhC,MAAM,CAAC,IAAI,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkC,YAAY,GAAGpB,QAAQ,CAACqB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnB,IAAI,KAAK,aAAa,CAAC;IACrE,IAAIiB,YAAY,IAAIF,QAAQ,CAACK,OAAO,IAAI,CAACJ,gBAAgB,CAACI,OAAO,EAAE;MACjEC,eAAe,CAACJ,YAAY,CAACR,SAAS,CAAC;IACzC;EACF,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMwB,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIN,gBAAgB,CAACI,OAAO,EAAE;MAC5BJ,gBAAgB,CAACI,OAAO,CAACG,MAAM,CAAC,CAAC;IACnC;IAEA,MAAMC,KAAK,GAAGrC,WAAW,CAAC4B,QAAQ,CAACK,OAAO,EAAE;MAC1CK,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE;QACNC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAc,CAAC;QACpCC,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJC,SAAS,EAAE;UAAEH,KAAK,EAAE;QAAyB,CAAC;QAC9CI,SAAS,EAAE;UAAEJ,KAAK,EAAE;QAAyB;MAC/C,CAAC;MACDK,SAAS,EAAE;QACTC,IAAI,EAAE;MACR,CAAC;MACDC,eAAe,EAAE;QACfC,WAAW,EAAE,wBAAwB;QACrCP,SAAS,EAAE;MACb,CAAC;MACDQ,SAAS,EAAE;QACTD,WAAW,EAAE,wBAAwB;QACrCP,SAAS,EAAE,SAAS;QACpBS,WAAW,EAAE,KAAK;QAClBC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,MAAMC,iBAAiB,GAAGjB,KAAK,CAACkB,oBAAoB,CAAC;MACnDC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,eAAe,EAAE,SAAS;MAC1BC,aAAa,EAAE,SAAS;MACxBC,aAAa,EAAE,SAAS;MACxBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFP,iBAAiB,CAACQ,OAAO,CAAC3B,IAAI,CAAC;IAC/BN,gBAAgB,CAACI,OAAO,GAAGI,KAAK;;IAEhC;IACAA,KAAK,CAACc,SAAS,CAAC,CAAC,CAACY,UAAU,CAAC,CAAC;EAChC,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxC,YAAY,CAACyC,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAMC,UAAU,GAAG;MACjBtD,EAAE,EAAEI,IAAI,CAACmD,GAAG,CAAC,CAAC;MACdtD,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEU,YAAY;MACrBT,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAACyD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,UAAU,CAAC,CAAC;IAC1C,MAAMG,aAAa,GAAG7C,YAAY;IAClCC,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF;MACA,MAAM2C,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,EAAE;QACjDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,OAAO,EAAER,aAAa;UACtBS,YAAY,EAAE;YACZ/D,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC+D,WAAW,CAAC,CAAC;YACnCC,UAAU,EAAE,eAAe;YAC3BC,SAAS,EAAE;UACb;QACF,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACX,QAAQ,CAACY,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBb,QAAQ,CAACc,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMjD,IAAI,GAAG,MAAMmC,QAAQ,CAACe,IAAI,CAAC,CAAC;MAClC1D,WAAW,CAAC,KAAK,CAAC;;MAElB;MACA,MAAM2D,aAAa,GAAG;QACpB1E,EAAE,EAAEI,IAAI,CAACmD,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBtD,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAEqB,IAAI,CAACmC,QAAQ,IAAI,uDAAuD;QACjFvD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBuE,aAAa,EAAEpD,IAAI,CAACtB,IAAI;QACxB2E,eAAe,EAAErD,IAAI,CAACqD,eAAe;QACrCC,YAAY,EAAEtD,IAAI,CAACsD,YAAY;QAC/BC,OAAO,EAAEvD,IAAI,CAACuD,OAAO;QACrBC,eAAe,EAAExD,IAAI,CAACwD;MACxB,CAAC;MAEDhF,WAAW,CAACyD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEkB,aAAa,CAAC,CAAC;;MAE7C;MACA,IAAInD,IAAI,CAACtB,IAAI,KAAK,aAAa,IAAIsB,IAAI,CAACsD,YAAY,EAAE;QACpD,MAAM3D,YAAY,GAAG;UACnBlB,EAAE,EAAEI,IAAI,CAACmD,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBtD,IAAI,EAAE,aAAa;UACnBI,MAAM,EAAEkB,IAAI,CAACsD,YAAY,CAACxE,MAAM,IAAI,SAAS;UAC7CC,KAAK,EAAEiB,IAAI,CAACsD,YAAY,CAACG,aAAa,IAAI,CAAC;UAC3CzE,MAAM,EAAEgB,IAAI,CAACsD,YAAY,CAACI,YAAY,IAAI,CAAC;UAC3CzE,aAAa,EAAEe,IAAI,CAACsD,YAAY,CAACK,oBAAoB,IAAI,CAAC;UAC1DzE,OAAO,EAAEc,IAAI,CAACsD,YAAY,CAACM,YAAY,IAAI,iBAAiB;UAC5DzE,SAAS,EAAEC,qBAAqB,CAAC,CAAC;UAClCR,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDL,WAAW,CAACyD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEtC,YAAY,CAAC,CAAC;MAC9C;IAEF,CAAC,CAAC,OAAOkE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CrE,WAAW,CAAC,KAAK,CAAC;MAElB,MAAMuE,YAAY,GAAG;QACnBtF,EAAE,EAAEI,IAAI,CAACmD,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBtD,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,uFAAuF;QAChGC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDL,WAAW,CAACyD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE8B,YAAY,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBvC,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACEzD,OAAA;IAAKiG,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAC7DlG,OAAA,CAACF,eAAe;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnBtG,OAAA;MAAKiG,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9ElG,OAAA,CAACT,MAAM,CAACgH,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BZ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAG3BlG,OAAA,CAACT,MAAM,CAACgH,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cb,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAE5BlG,OAAA;YAAKiG,SAAS,EAAC,gJAAgJ;YAAAC,QAAA,eAC7JlG,OAAA,CAACH,QAAQ;cAACoG,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNtG,OAAA;YAAIiG,SAAS,EAAC,4GAA4G;YAAAC,QAAA,EAAC;UAE3H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtG,OAAA;YAAGiG,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGbtG,OAAA,CAACT,MAAM,CAACgH,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,KAAK,EAAE;UAAK,CAAE;UACrCJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,KAAK,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cb,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBAGpClG,OAAA;YAAKiG,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjDlG,OAAA,CAACR,eAAe;cAAA0G,QAAA,EACb/F,QAAQ,CAAC6G,GAAG,CAAE1C,OAAO,iBACpBtE,OAAA,CAACT,MAAM,CAACgH,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BO,IAAI,EAAE;kBAAER,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BE,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAX,QAAA,GAE7B5B,OAAO,CAAChE,IAAI,KAAK,QAAQ,iBACxBN,OAAA;kBAAKiG,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EACnD5B,OAAO,CAAC/D;gBAAO;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACN,EAEAhC,OAAO,CAAChE,IAAI,KAAK,MAAM,iBACtBN,OAAA;kBAAKiG,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBlG,OAAA;oBAAKiG,SAAS,EAAC,kGAAkG;oBAAAC,QAAA,EAC9G5B,OAAO,CAAC/D;kBAAO;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEAhC,OAAO,CAAChE,IAAI,KAAK,aAAa,iBAC7BN,OAAA;kBAAKiG,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,gBAEpHlG,OAAA;oBAAKiG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDlG,OAAA;sBAAAkG,QAAA,gBACElG,OAAA;wBAAKiG,SAAS,EAAC,+BAA+B;wBAAAC,QAAA,EAC3C5B,OAAO,CAAC5D;sBAAM;wBAAAyF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNtG,OAAA;wBAAKiG,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EACtC5B,OAAO,CAACxD;sBAAO;wBAAAqF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNtG,OAAA;sBAAKiG,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBlG,OAAA;wBAAKiG,SAAS,EAAC,+BAA+B;wBAAAC,QAAA,EAC3C5B,OAAO,CAAC3D;sBAAK;wBAAAwF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACNtG,OAAA;wBAAKiG,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,gBACvDlG,OAAA,CAACL,UAAU;0BAACsG,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,KACtC,EAAChC,OAAO,CAAC1D,MAAM,EAAC,GACnB;sBAAA;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNtG,OAAA;oBAAKiG,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,eACzClG,OAAA;sBAAKkH,GAAG,EAAE7F,QAAS;sBAAC4E,SAAS,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eAGNtG,OAAA;oBAAKiG,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBlG,OAAA;sBAAQiG,SAAS,EAAC,sJAAsJ;sBAAAC,QAAA,EAAC;oBAEzK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTtG,OAAA;sBAAQiG,SAAS,EAAC,sJAAsJ;sBAAAC,QAAA,EAAC;oBAEzK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GA1DIhC,OAAO,CAACjE,EAAE;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2DL,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC,eAGlBtG,OAAA,CAACR,eAAe;cAAA0G,QAAA,EACb/E,QAAQ,iBACPnB,OAAA,CAACT,MAAM,CAACgH,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BO,IAAI,EAAE;kBAAER,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BT,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBAErDlG,OAAA;kBAAKiG,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BlG,OAAA;oBAAKiG,SAAS,EAAC;kBAAiD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnEtG,OAAA;oBAAKiG,SAAS,EAAC,iDAAiD;oBAACkB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtGtG,OAAA;oBAAKiG,SAAS,EAAC,iDAAiD;oBAACkB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACNtG,OAAA;kBAAMiG,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGNtG,OAAA;YAAKiG,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlG,OAAA;cACEM,IAAI,EAAC,MAAM;cACX+G,KAAK,EAAEpG,YAAa;cACpBqG,QAAQ,EAAGzB,CAAC,IAAK3E,eAAe,CAAC2E,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAE;cACjDG,UAAU,EAAE5B,cAAe;cAC3B6B,WAAW,EAAC,mBAAmB;cAC/BxB,SAAS,EAAC;YAAsN;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjO,CAAC,eACFtG,OAAA;cACE0H,OAAO,EAAEjE,iBAAkB;cAC3BwC,SAAS,EAAC,+MAA+M;cAAAC,QAAA,eAEzNlG,OAAA,CAACN,IAAI;gBAACuG,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAApG,EAAA,CAhUMD,cAAc;AAAA0H,EAAA,GAAd1H,cAAc;AAiUpB,SAASe,qBAAqBA,CAAA,EAAG;EAC/B,MAAMY,IAAI,GAAG,EAAE;EACf,IAAIgG,SAAS,GAAG,GAAG;EAEnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACvH,IAAI,CAACmD,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGiE,CAAC,IAAI,IAAI;IAC5D,MAAMI,UAAU,GAAG,IAAI;IACvB,MAAMrH,MAAM,GAAG,CAACmH,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAID,UAAU,GAAGL,SAAS;IAE7D,MAAMO,IAAI,GAAGP,SAAS;IACtB,MAAMQ,KAAK,GAAGR,SAAS,GAAGhH,MAAM;IAChC,MAAMyH,IAAI,GAAGN,IAAI,CAACO,GAAG,CAACH,IAAI,EAAEC,KAAK,CAAC,GAAGL,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG;IACxD,MAAMK,GAAG,GAAGR,IAAI,CAACS,GAAG,CAACL,IAAI,EAAEC,KAAK,CAAC,GAAGL,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG;IAEvDtG,IAAI,CAAC6G,IAAI,CAAC;MACRX,IAAI;MACJK,IAAI,EAAEO,UAAU,CAACP,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;MACjCN,IAAI,EAAEK,UAAU,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;MACjCJ,GAAG,EAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BP,KAAK,EAAEM,UAAU,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;IAEFf,SAAS,GAAGQ,KAAK;EACnB;EAEA,OAAOxG,IAAI;AACb;AAEA,eAAe3B,cAAc;AAAC,IAAA0H,EAAA;AAAAiB,YAAA,CAAAjB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}