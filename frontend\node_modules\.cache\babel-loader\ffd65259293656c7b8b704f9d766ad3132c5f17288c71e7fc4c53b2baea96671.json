{"ast": null, "code": "'use client';\n\nimport createStyled from '@mui/system/createStyled';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nimport rootShouldForwardProp from './rootShouldForwardProp';\nexport { default as slotShouldForwardProp } from './slotShouldForwardProp';\nexport { default as rootShouldForwardProp } from './rootShouldForwardProp';\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;", "map": {"version": 3, "names": ["createStyled", "defaultTheme", "THEME_ID", "rootShouldForwardProp", "default", "slotShouldForwardProp", "styled", "themeId"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/styles/styled.js"], "sourcesContent": ["'use client';\n\nimport createStyled from '@mui/system/createStyled';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nimport rootShouldForwardProp from './rootShouldForwardProp';\nexport { default as slotShouldForwardProp } from './slotShouldForwardProp';\nexport { default as rootShouldForwardProp } from './rootShouldForwardProp';\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,YAAY,MAAM,0BAA0B;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,OAAO,IAAIC,qBAAqB,QAAQ,yBAAyB;AAC1E,SAASD,OAAO,IAAID,qBAAqB,QAAQ,yBAAyB;AAC1E,MAAMG,MAAM,GAAGN,YAAY,CAAC;EAC1BO,OAAO,EAAEL,QAAQ;EACjBD,YAAY;EACZE;AACF,CAAC,CAAC;AACF,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}