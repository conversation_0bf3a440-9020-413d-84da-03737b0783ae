{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Printer = createLucideIcon(\"Printer\", [[\"polyline\", {\n  points: \"6 9 6 2 18 2 18 9\",\n  key: \"1306q4\"\n}], [\"path\", {\n  d: \"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\",\n  key: \"143wyd\"\n}], [\"rect\", {\n  width: \"12\",\n  height: \"8\",\n  x: \"6\",\n  y: \"14\",\n  key: \"5ipwut\"\n}]]);\nexport { Printer as default };", "map": {"version": 3, "names": ["Printer", "createLucideIcon", "points", "key", "d", "width", "height", "x", "y"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\printer.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Printer\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI2IDkgNiAyIDE4IDIgMTggOSIgLz4KICA8cGF0aCBkPSJNNiAxOEg0YTIgMiAwIDAgMS0yLTJ2LTVhMiAyIDAgMCAxIDItMmgxNmEyIDIgMCAwIDEgMiAydjVhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cmVjdCB3aWR0aD0iMTIiIGhlaWdodD0iOCIgeD0iNiIgeT0iMTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/printer\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Printer = createLucideIcon('Printer', [\n  ['polyline', { points: '6 9 6 2 18 2 18 9', key: '1306q4' }],\n  [\n    'path',\n    {\n      d: 'M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2',\n      key: '143wyd',\n    },\n  ],\n  ['rect', { width: '12', height: '8', x: '6', y: '14', key: '5ipwut' }],\n]);\n\nexport default Printer;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,UAAY;EAAEC,MAAA,EAAQ,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEE,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAL,GAAA,EAAK;AAAA,CAAU,EACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}