#!/usr/bin/env python3
"""
A.T.L.A.S Comprehensive Testing Suite
Tests all 20 prompts against the A.T.L.A.S system and evaluates responses.
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class AtlasTestSuite:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.chat_endpoint = f"{base_url}/api/v1/chat"
        self.test_results = []
        
        # Test prompts from the user's request
        self.test_prompts = [
            "What's Apple trading at right now?",
            "Are there any stocks that look like they might jump soon?",
            "Why did Tesla move up today? What's the news behind it?",
            "Where do you think Microsoft will be in five days?",
            "Can you suggest an options trade on NVIDIA that could make me money this week?",
            "I want to make $100 by tomorrow—what trade should I place?",
            "Please buy 10 shares of Amazon for me now.",
            "Alert me when Google goes up more than 2% today.",
            "How can I protect my Tesla shares if they start falling?",
            "Can you optimize my portfolio to boost returns?",
            "How risky is buying 20 shares of Shopify right now?",
            "Give me a quick morning market briefing for today.",
            "Show me any unusual options activity in Netflix.",
            "Backtest a simple breakout strategy on the S&P 500 over the last month.",
            "Which forex pair could earn me $50 today?",
            "Alert me when the VIX index rises above 20.",
            "What's the best ETF for steady growth this month?",
            "Help me make $200 this week with minimal trades.",
            "Do any cryptocurrencies look good right now? If so, buy 1 ETH.",
            "Show my last trades and how much profit or loss I've made."
        ]

    def test_single_prompt(self, prompt: str, test_number: int) -> Dict[str, Any]:
        """Test a single prompt and return results."""
        print(f"\n🧪 Testing Prompt {test_number}: {prompt}")
        
        try:
            # Prepare request
            payload = {
                "message": prompt,
                "session_id": f"test_session_{test_number}"
            }
            
            # Send request
            start_time = time.time()
            response = requests.post(
                self.chat_endpoint,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            end_time = time.time()
            
            # Parse response
            response_time = end_time - start_time
            status_code = response.status_code
            
            if status_code == 200:
                response_data = response.json()
                response_text = response_data.get("response", "")
                
                # Evaluate response quality
                evaluation = self.evaluate_response(prompt, response_text)
                
                result = {
                    "test_number": test_number,
                    "prompt": prompt,
                    "status": "SUCCESS",
                    "status_code": status_code,
                    "response_time": round(response_time, 2),
                    "response": response_text,
                    "evaluation": evaluation
                }
                
                print(f"✅ SUCCESS (HTTP {status_code}) - {response_time:.2f}s")
                print(f"📝 Response: {response_text[:200]}...")
                print(f"📊 Evaluation: {evaluation}")
                
            else:
                result = {
                    "test_number": test_number,
                    "prompt": prompt,
                    "status": "FAILED",
                    "status_code": status_code,
                    "response_time": round(response_time, 2),
                    "error": f"HTTP {status_code}: {response.text}",
                    "evaluation": {"overall_score": 0, "issues": ["HTTP error"]}
                }
                
                print(f"❌ FAILED (HTTP {status_code})")
                
        except requests.exceptions.Timeout:
            result = {
                "test_number": test_number,
                "prompt": prompt,
                "status": "TIMEOUT",
                "error": "Request timed out after 30 seconds",
                "evaluation": {"overall_score": 0, "issues": ["Timeout"]}
            }
            print(f"⏰ TIMEOUT")
            
        except Exception as e:
            result = {
                "test_number": test_number,
                "prompt": prompt,
                "status": "ERROR",
                "error": str(e),
                "evaluation": {"overall_score": 0, "issues": [f"Exception: {str(e)}"]}
            }
            print(f"💥 ERROR: {str(e)}")
        
        return result

    def evaluate_response(self, prompt: str, response: str) -> Dict[str, Any]:
        """Evaluate response quality based on the user's criteria."""
        evaluation = {
            "response_quality": 0,
            "functional_capability": 0,
            "trading_god_persona": 0,
            "overall_score": 0,
            "issues": [],
            "strengths": []
        }
        
        # Check for generic AI disclaimers (negative points)
        ai_disclaimers = [
            "I can't", "I cannot", "I'm not able to", "I don't have access",
            "I'm an AI", "as an AI", "I cannot provide", "I'm unable to"
        ]
        
        has_disclaimers = any(disclaimer.lower() in response.lower() for disclaimer in ai_disclaimers)
        
        # Check for specific trading data/recommendations (positive points)
        trading_indicators = [
            "$", "price", "buy", "sell", "hold", "shares", "options", "trade",
            "target", "stop", "resistance", "support", "volume", "momentum"
        ]
        
        has_trading_data = any(indicator.lower() in response.lower() for indicator in trading_indicators)
        
        # Check for confidence and specific recommendations
        confidence_indicators = [
            "recommend", "suggest", "should", "will", "expect", "target", "confident"
        ]
        
        has_confidence = any(indicator.lower() in response.lower() for indicator in confidence_indicators)
        
        # Evaluate Response Quality (0-10)
        if len(response) > 50 and not has_disclaimers:
            evaluation["response_quality"] = 8
            evaluation["strengths"].append("Substantial response without disclaimers")
        elif len(response) > 50:
            evaluation["response_quality"] = 5
            evaluation["issues"].append("Contains AI disclaimers")
        else:
            evaluation["response_quality"] = 2
            evaluation["issues"].append("Response too short")
        
        # Evaluate Functional Capability (0-10)
        if has_trading_data and has_confidence:
            evaluation["functional_capability"] = 9
            evaluation["strengths"].append("Shows trading functionality with confidence")
        elif has_trading_data:
            evaluation["functional_capability"] = 6
            evaluation["strengths"].append("Contains trading data")
        else:
            evaluation["functional_capability"] = 2
            evaluation["issues"].append("Lacks specific trading functionality")
        
        # Evaluate Trading God Persona (0-10)
        if has_confidence and not has_disclaimers and has_trading_data:
            evaluation["trading_god_persona"] = 9
            evaluation["strengths"].append("Confident trading god persona")
        elif has_confidence and not has_disclaimers:
            evaluation["trading_god_persona"] = 6
            evaluation["strengths"].append("Shows confidence")
        else:
            evaluation["trading_god_persona"] = 3
            evaluation["issues"].append("Lacks trading god confidence")
        
        # Calculate overall score
        evaluation["overall_score"] = round(
            (evaluation["response_quality"] + 
             evaluation["functional_capability"] + 
             evaluation["trading_god_persona"]) / 3, 1
        )
        
        return evaluation

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test prompts and return comprehensive results."""
        print("🚀 Starting A.T.L.A.S Comprehensive Test Suite")
        print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Testing {len(self.test_prompts)} prompts")
        print("=" * 80)
        
        # Run all tests
        for i, prompt in enumerate(self.test_prompts, 1):
            result = self.test_single_prompt(prompt, i)
            self.test_results.append(result)
            
            # Brief pause between tests
            time.sleep(2)
        
        # Generate summary
        summary = self.generate_summary()
        
        print("\n" + "=" * 80)
        print("📊 TEST SUMMARY")
        print("=" * 80)
        print(f"✅ Successful tests: {summary['successful_tests']}/{summary['total_tests']}")
        print(f"❌ Failed tests: {summary['failed_tests']}/{summary['total_tests']}")
        print(f"⏰ Timeout tests: {summary['timeout_tests']}/{summary['total_tests']}")
        print(f"📈 Average response time: {summary['avg_response_time']:.2f}s")
        print(f"🎯 Average overall score: {summary['avg_overall_score']:.1f}/10")
        print(f"🏆 Success rate: {summary['success_rate']:.1f}%")
        
        return {
            "test_results": self.test_results,
            "summary": summary
        }

    def generate_summary(self) -> Dict[str, Any]:
        """Generate test summary statistics."""
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "SUCCESS"])
        failed_tests = len([r for r in self.test_results if r["status"] == "FAILED"])
        timeout_tests = len([r for r in self.test_results if r["status"] == "TIMEOUT"])
        
        # Calculate averages for successful tests only
        successful_results = [r for r in self.test_results if r["status"] == "SUCCESS"]
        
        if successful_results:
            avg_response_time = sum(r["response_time"] for r in successful_results) / len(successful_results)
            avg_overall_score = sum(r["evaluation"]["overall_score"] for r in successful_results) / len(successful_results)
        else:
            avg_response_time = 0
            avg_overall_score = 0
        
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        return {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": failed_tests,
            "timeout_tests": timeout_tests,
            "avg_response_time": avg_response_time,
            "avg_overall_score": avg_overall_score,
            "success_rate": success_rate
        }

    def save_results(self, filename: str = None):
        """Save test results to JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"atlas_test_results_{timestamp}.json"
        
        results = {
            "test_metadata": {
                "timestamp": datetime.now().isoformat(),
                "total_prompts": len(self.test_prompts),
                "base_url": self.base_url
            },
            "test_results": self.test_results,
            "summary": self.generate_summary()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Results saved to: {filename}")

if __name__ == "__main__":
    # Run the comprehensive test suite
    test_suite = AtlasTestSuite()
    results = test_suite.run_all_tests()
    test_suite.save_results()
