# Holly AI Interface Guide

Holly AI provides multiple interface options to suit different user preferences and use cases. Choose the interface that best fits your workflow.

## 🎯 **Available Interfaces**

### 1. **React Web Interface** (Recommended) ✅
**Location**: `frontend/` directory  
**Best for**: Full-featured trading with visual dashboard

**Features**:
- 🤖 **Holly AI Chat** - Natural language conversation
- 🧠 **Advanced AI Features** - Dedicated tabs for each AI capability
- 📊 **Trading Dashboard** - Account info, positions, signals
- 📈 **Real-time Data** - Live market data and updates
- 🎨 **Modern UI** - Beautiful, responsive design

**How to Start**:
```bash
# Backend
python main.py

# Frontend (in new terminal)
cd frontend
npm install
npm start
```

**Access**: http://localhost:3000

### 2. **FastAPI REST API** ✅
**Location**: `src/api/holly_routes.py`  
**Best for**: Integration with other applications

**Endpoints**:
```
POST /holly/chat                    - Chat with Holly AI
GET  /holly/ai/regime              - Market regime analysis
GET  /holly/ai/sentiment/{symbol}  - Social sentiment analysis
GET  /holly/ai/embeddings/{symbol} - Market embeddings
GET  /holly/ai/ttm-scan            - TTM Squeeze scanning
GET  /holly/ai/news-search         - Web news search
GET  /holly/status                 - System status
```

**How to Start**:
```bash
python main.py
```

**Access**: http://localhost:8000/docs (Swagger UI)

### 3. **Command Line Interface** ✅
**Location**: `holly_cli.py`  
**Best for**: Terminal users and automation

**Features**:
- 💬 **Interactive Chat** - Direct conversation with Holly
- ⚡ **Quick Commands** - Shortcuts for common tasks
- 📜 **History** - Conversation history tracking
- 🔧 **System Commands** - Status, config, help

**How to Start**:
```bash
python holly_cli.py
```

**Example Commands**:
```
> make me $50 today
> what's the market regime?
> sentiment AAPL
> ttm scan AAPL,TSLA,NVDA
> search Apple earnings
```

### 4. **Streamlit Web Interface** ✅
**Location**: `holly_streamlit.py`  
**Best for**: Simple web interface and demos

**Features**:
- 💬 **Chat Interface** - Talk to Holly AI
- 🧠 **AI Feature Tabs** - Dedicated sections for each capability
- 📊 **Real-time Metrics** - System status and results
- ⚡ **Quick Actions** - One-click common tasks

**How to Start**:
```bash
pip install streamlit
streamlit run holly_streamlit.py
```

**Access**: http://localhost:8501

## 🚀 **Interface Comparison**

| Feature | React Web | REST API | CLI | Streamlit |
|---------|-----------|----------|-----|-----------|
| **Ease of Use** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Visual Appeal** | ⭐⭐⭐⭐⭐ | ⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **Feature Access** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Customization** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| **Performance** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Mobile Friendly** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐ | ⭐⭐⭐ |

## 🎨 **React Web Interface Details**

### **Main Features**

#### **Holly AI Chat Tab**
- Natural language conversation with Holly
- Real-time responses with function call indicators
- Trading plan generation and execution
- Conversation history with timestamps

#### **AI Features Tab**
- **🌊 Regime Detection** - Market condition analysis
- **📱 Sentiment Analysis** - Social media mood tracking
- **🧠 Embeddings** - Pattern matching and forecasting
- **📈 TTM Scanner** - Squeeze signal detection
- **🔍 News Search** - Current market news

#### **Signals & Positions Tab**
- Active trading signals with confidence scores
- Current positions with P&L tracking
- Signal execution and management

### **Dashboard Components**
- **Account Info** - Balance, buying power, day trades
- **Quick Stats** - Portfolio value, daily P&L, positions
- **Real-time Updates** - Live data refresh

## 🔧 **API Interface Details**

### **Authentication**
Currently no authentication required (development mode)

### **Request/Response Format**
```json
// Chat Request
POST /holly/chat
{
  "message": "Make me $50 today",
  "conversation_id": "optional-uuid"
}

// Response
{
  "response": "Holly's text response",
  "function_called": "interpret_trading_goal",
  "function_result": {...},
  "trading_plan": {...},
  "conversation_id": "uuid"
}
```

### **Advanced AI Endpoints**
```bash
# Market Regime
GET /holly/ai/regime?symbols=SPY,QQQ,VIX

# Social Sentiment  
GET /holly/ai/sentiment/AAPL?hours_back=24

# Market Embeddings
GET /holly/ai/embeddings/AAPL?context=SPY,QQQ,VIX,TLT

# TTM Squeeze Scan
GET /holly/ai/ttm-scan?symbols=AAPL,TSLA&timeframe=5min

# News Search
GET /holly/ai/news-search?query=Apple%20earnings
```

## 💻 **CLI Interface Details**

### **Available Commands**

#### **Trading Commands**
```bash
make me $50 today              # Create trading plan
find momentum plays            # Scan for opportunities  
what's AAPL looking like?      # Analyze specific stock
hedge my TSLA position         # Create hedging strategy
```

#### **AI Feature Commands**
```bash
regime analysis                # Detect market regime
sentiment AAPL                 # Analyze social sentiment
embeddings TSLA               # Generate market embeddings
ttm scan AAPL,NVDA,MSFT       # Scan TTM Squeeze signals
search Apple earnings          # Search web for news
```

#### **System Commands**
```bash
help                          # Show all commands
history                       # Show conversation history
status                        # Show system status
config                        # Show configuration
clear                         # Clear screen
quit / exit                   # Exit Holly AI
```

### **CLI Features**
- **Rich Formatting** - Colored output and structured responses
- **Function Indicators** - Shows which AI functions were called
- **Error Handling** - Graceful error messages and suggestions
- **History Tracking** - Maintains conversation context

## 🌐 **Streamlit Interface Details**

### **Main Sections**

#### **Chat Interface**
- Text input for natural language queries
- Conversation history with expandable details
- Function call indicators and results

#### **Quick Actions Sidebar**
- One-click market regime analysis
- AAPL sentiment analysis
- Latest market news search
- TTM signal scanning

#### **Advanced Feature Tabs**
Each AI capability has its own tab with:
- Input forms for parameters
- Execute buttons for analysis
- Results display with metrics
- Holly's interpretation

### **Real-time Features**
- Live system status metrics
- Last response details
- Function execution results
- Error handling and feedback

## 🔄 **Choosing the Right Interface**

### **For Beginners**
**Recommended**: React Web Interface or Streamlit
- Visual and intuitive
- Guided workflows
- Rich feedback and explanations

### **For Developers**
**Recommended**: REST API or CLI
- Programmatic access
- Integration capabilities
- Automation friendly

### **For Traders**
**Recommended**: React Web Interface
- Complete trading dashboard
- Real-time data and execution
- Professional trading tools

### **For Demos**
**Recommended**: Streamlit
- Quick setup
- Interactive features
- Easy to share

## 🚀 **Getting Started**

### **Quick Start (React Web)**
```bash
# 1. Start backend
python main.py

# 2. Start frontend (new terminal)
cd frontend
npm install
npm start

# 3. Open browser
# http://localhost:3000
```

### **Quick Start (CLI)**
```bash
# 1. Run CLI
python holly_cli.py

# 2. Try commands
> help
> make me $50 today
> regime analysis
```

### **Quick Start (Streamlit)**
```bash
# 1. Install Streamlit
pip install streamlit

# 2. Run interface
streamlit run holly_streamlit.py

# 3. Open browser
# http://localhost:8501
```

## 🔧 **Configuration**

All interfaces use the same configuration from `.env`:
- API keys for trading and AI services
- Feature toggles for advanced capabilities
- Risk management parameters
- System settings

## 📱 **Mobile Support**

- **React Web**: Full responsive design, works on all devices
- **Streamlit**: Mobile-friendly layout
- **CLI**: Works on mobile terminals
- **API**: Perfect for mobile app integration

## 🎯 **Next Steps**

1. **Choose your preferred interface**
2. **Configure API keys in `.env`**
3. **Start the interface**
4. **Begin trading with Holly AI!**

Each interface provides access to Holly's full AI capabilities including regime detection, sentiment analysis, pattern recognition, and intelligent trading assistance.
