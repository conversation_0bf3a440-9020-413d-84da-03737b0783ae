{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Guitar = createLucideIcon(\"Guitar\", [[\"path\", {\n  d: \"m20 7 1.7-1.7a1 1 0 0 0 0-1.4l-1.6-1.6a1 1 0 0 0-1.4 0L17 4v3Z\",\n  key: \"15ixgv\"\n}], [\"path\", {\n  d: \"m17 7-5.1 5.1\",\n  key: \"l9guh7\"\n}], [\"circle\", {\n  cx: \"11.5\",\n  cy: \"12.5\",\n  r: \".5\",\n  key: \"1evg0a\"\n}], [\"path\", {\n  d: \"M6 12a2 2 0 0 0 1.8-1.2l.4-.9C8.7 8.8 9.8 8 11 8c2.8 0 5 2.2 5 5 0 1.2-.8 2.3-1.9 2.8l-.9.4A2 2 0 0 0 12 18a4 4 0 0 1-4 4c-3.3 0-6-2.7-6-6a4 4 0 0 1 4-4\",\n  key: \"x9fguj\"\n}], [\"path\", {\n  d: \"m6 16 2 2\",\n  key: \"16qmzd\"\n}]]);\nexport { Guitar as default };", "map": {"version": 3, "names": ["Guitar", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\guitar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Guitar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjAgNyAxLjctMS43YTEgMSAwIDAgMCAwLTEuNGwtMS42LTEuNmExIDEgMCAwIDAtMS40IDBMMTcgNHYzWiIgLz4KICA8cGF0aCBkPSJtMTcgNy01LjEgNS4xIiAvPgogIDxjaXJjbGUgY3g9IjExLjUiIGN5PSIxMi41IiByPSIuNSIgLz4KICA8cGF0aCBkPSJNNiAxMmEyIDIgMCAwIDAgMS44LTEuMmwuNC0uOUM4LjcgOC44IDkuOCA4IDExIDhjMi44IDAgNSAyLjIgNSA1IDAgMS4yLS44IDIuMy0xLjkgMi44bC0uOS40QTIgMiAwIDAgMCAxMiAxOGE0IDQgMCAwIDEtNCA0Yy0zLjMgMC02LTIuNy02LTZhNCA0IDAgMCAxIDQtNCIgLz4KICA8cGF0aCBkPSJtNiAxNiAyIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/guitar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Guitar = createLucideIcon('Guitar', [\n  ['path', { d: 'm20 7 1.7-1.7a1 1 0 0 0 0-1.4l-1.6-1.6a1 1 0 0 0-1.4 0L17 4v3Z', key: '15ixgv' }],\n  ['path', { d: 'm17 7-5.1 5.1', key: 'l9guh7' }],\n  ['circle', { cx: '11.5', cy: '12.5', r: '.5', key: '1evg0a' }],\n  [\n    'path',\n    {\n      d: 'M6 12a2 2 0 0 0 1.8-1.2l.4-.9C8.7 8.8 9.8 8 11 8c2.8 0 5 2.2 5 5 0 1.2-.8 2.3-1.9 2.8l-.9.4A2 2 0 0 0 12 18a4 4 0 0 1-4 4c-3.3 0-6-2.7-6-6a4 4 0 0 1 4-4',\n      key: 'x9fguj',\n    },\n  ],\n  ['path', { d: 'm6 16 2 2', key: '16qmzd' }],\n]);\n\nexport default Guitar;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gEAAkE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/F,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAMH,GAAK;AAAA,CAAU,GAC7D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}