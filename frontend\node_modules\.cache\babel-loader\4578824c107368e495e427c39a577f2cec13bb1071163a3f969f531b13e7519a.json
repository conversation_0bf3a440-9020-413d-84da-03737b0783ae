{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Grid,Paper,Typography,Box,AppBar,Toolbar,Card,CardContent,Tabs,Tab,Switch,FormControlLabel}from'@mui/material';import HollyChat from'./components/HollyChat';import Dashboard from'./components/Dashboard';import SignalsList from'./components/SignalsList';import PositionsList from'./components/PositionsList';import AccountInfo from'./components/AccountInfo';import AIFeatures from'./components/AIFeatures';import AtlasInterface from'./components/AtlasInterface';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[accountData,setAccountData]=useState(null);const[positions,setPositions]=useState([]);const[signals,setSignals]=useState([]);const[activeTab,setActiveTab]=useState(0);const[useAtlasInterface,setUseAtlasInterface]=useState(true);useEffect(()=>{// Fetch initial data\nfetchAccountData();fetchPositions();fetchSignals();// Set up periodic updates\nconst interval=setInterval(()=>{fetchAccountData();fetchPositions();fetchSignals();},30000);// Update every 30 seconds\nreturn()=>clearInterval(interval);},[]);const fetchAccountData=async()=>{try{const response=await fetch('/api/v1/account');if(response.ok){const data=await response.json();setAccountData(data);}}catch(error){console.error('Error fetching account data:',error);}};const fetchPositions=async()=>{try{const response=await fetch('/api/v1/positions');if(response.ok){const data=await response.json();setPositions(data);}}catch(error){console.error('Error fetching positions:',error);}};const fetchSignals=async()=>{try{const response=await fetch('/api/v1/signals');if(response.ok){const data=await response.json();setSignals(data);}}catch(error){console.error('Error fetching signals:',error);}};// Show A.T.L.A.S interface if enabled\nif(useAtlasInterface){return/*#__PURE__*/_jsx(AtlasInterface,{});}return/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsx(AppBar,{position:\"static\",sx:{backgroundColor:'#1976d2'},children:/*#__PURE__*/_jsxs(Toolbar,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",component:\"div\",sx:{flexGrow:1},children:\"\\uD83E\\uDD16 Holly AI Trading System\"}),/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Switch,{checked:useAtlasInterface,onChange:e=>setUseAtlasInterface(e.target.checked),color:\"default\"}),label:\"A.T.L.A.S Interface\",sx:{color:'white',mr:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{backgroundColor:'rgba(255,255,255,0.2)',px:1,py:0.5,borderRadius:1},children:\"Paper Trading Mode\"})]})}),/*#__PURE__*/_jsxs(Container,{maxWidth:\"xl\",sx:{mt:2},children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:3},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(AccountInfo,{accountData:accountData})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(Dashboard,{accountData:accountData,positions:positions,signals:signals})})]}),/*#__PURE__*/_jsxs(Paper,{sx:{width:'100%'},children:[/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider'},children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:(event,newValue)=>setActiveTab(newValue),variant:\"fullWidth\",sx:{'& .MuiTab-root':{fontSize:'1rem',fontWeight:600,textTransform:'none'}},children:[/*#__PURE__*/_jsx(Tab,{label:\"\\uD83E\\uDD16 Holly AI Chat\"}),/*#__PURE__*/_jsx(Tab,{label:\"\\uD83E\\uDDE0 AI Features\"}),/*#__PURE__*/_jsx(Tab,{label:\"\\uD83D\\uDCCA Signals & Positions\"})]})}),/*#__PURE__*/_jsxs(Box,{sx:{p:3},children:[activeTab===0&&/*#__PURE__*/_jsx(HollyChat,{onPlanExecuted:()=>{fetchAccountData();fetchPositions();fetchSignals();}}),activeTab===1&&/*#__PURE__*/_jsx(AIFeatures,{}),activeTab===2&&/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(SignalsList,{signals:signals})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(PositionsList,{positions:positions})})]})]})]})]})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Paper", "Typography", "Box", "AppBar", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Tabs", "Tab", "Switch", "FormControlLabel", "HollyChat", "Dashboard", "SignalsList", "PositionsList", "AccountInfo", "AIFeatures", "AtlasInterface", "jsx", "_jsx", "jsxs", "_jsxs", "App", "accountData", "setAccountData", "positions", "setPositions", "signals", "setSignals", "activeTab", "setActiveTab", "useAtlasInterface", "setUseAtlasInterface", "fetchAccountData", "fetchPositions", "fetchSignals", "interval", "setInterval", "clearInterval", "response", "fetch", "ok", "data", "json", "error", "console", "className", "children", "position", "sx", "backgroundColor", "variant", "component", "flexGrow", "control", "checked", "onChange", "e", "target", "color", "label", "mr", "px", "py", "borderRadius", "max<PERSON><PERSON><PERSON>", "mt", "container", "spacing", "mb", "item", "xs", "md", "width", "borderBottom", "borderColor", "value", "event", "newValue", "fontSize", "fontWeight", "textTransform", "p", "onPlanExecuted"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Container,\n  Grid,\n  Paper,\n  Typography,\n  Box,\n  AppBar,\n  Toolbar,\n  Card,\n  CardContent,\n  Tabs,\n  Tab,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport HollyChat from './components/HollyChat';\nimport Dashboard from './components/Dashboard';\nimport SignalsList from './components/SignalsList';\nimport PositionsList from './components/PositionsList';\nimport AccountInfo from './components/AccountInfo';\nimport AIFeatures from './components/AIFeatures';\nimport AtlasInterface from './components/AtlasInterface';\n\nfunction App() {\n  const [accountData, setAccountData] = useState(null);\n  const [positions, setPositions] = useState([]);\n  const [signals, setSignals] = useState([]);\n  const [activeTab, setActiveTab] = useState(0);\n  const [useAtlasInterface, setUseAtlasInterface] = useState(true);\n\n  useEffect(() => {\n    // Fetch initial data\n    fetchAccountData();\n    fetchPositions();\n    fetchSignals();\n\n    // Set up periodic updates\n    const interval = setInterval(() => {\n      fetchAccountData();\n      fetchPositions();\n      fetchSignals();\n    }, 30000); // Update every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchAccountData = async () => {\n    try {\n      const response = await fetch('/api/v1/account');\n      if (response.ok) {\n        const data = await response.json();\n        setAccountData(data);\n      }\n    } catch (error) {\n      console.error('Error fetching account data:', error);\n    }\n  };\n\n  const fetchPositions = async () => {\n    try {\n      const response = await fetch('/api/v1/positions');\n      if (response.ok) {\n        const data = await response.json();\n        setPositions(data);\n      }\n    } catch (error) {\n      console.error('Error fetching positions:', error);\n    }\n  };\n\n  const fetchSignals = async () => {\n    try {\n      const response = await fetch('/api/v1/signals');\n      if (response.ok) {\n        const data = await response.json();\n        setSignals(data);\n      }\n    } catch (error) {\n      console.error('Error fetching signals:', error);\n    }\n  };\n\n  // Show A.T.L.A.S interface if enabled\n  if (useAtlasInterface) {\n    return <AtlasInterface />;\n  }\n\n  return (\n    <div className=\"App\">\n      <AppBar position=\"static\" sx={{ backgroundColor: '#1976d2' }}>\n        <Toolbar>\n          <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n            🤖 Holly AI Trading System\n          </Typography>\n          <FormControlLabel\n            control={\n              <Switch\n                checked={useAtlasInterface}\n                onChange={(e) => setUseAtlasInterface(e.target.checked)}\n                color=\"default\"\n              />\n            }\n            label=\"A.T.L.A.S Interface\"\n            sx={{ color: 'white', mr: 2 }}\n          />\n          <Typography variant=\"body2\" sx={{\n            backgroundColor: 'rgba(255,255,255,0.2)',\n            px: 1,\n            py: 0.5,\n            borderRadius: 1\n          }}>\n            Paper Trading Mode\n          </Typography>\n        </Toolbar>\n      </AppBar>\n\n      <Container maxWidth=\"xl\" sx={{ mt: 2 }}>\n        {/* Top Row - Account Info and Dashboard */}\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid item xs={12} md={6}>\n            <AccountInfo accountData={accountData} />\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <Dashboard\n              accountData={accountData}\n              positions={positions}\n              signals={signals}\n            />\n          </Grid>\n        </Grid>\n\n        {/* Main Tabbed Interface */}\n        <Paper sx={{ width: '100%' }}>\n          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n            <Tabs\n              value={activeTab}\n              onChange={(event, newValue) => setActiveTab(newValue)}\n              variant=\"fullWidth\"\n              sx={{\n                '& .MuiTab-root': {\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  textTransform: 'none'\n                }\n              }}\n            >\n              <Tab label=\"🤖 Holly AI Chat\" />\n              <Tab label=\"🧠 AI Features\" />\n              <Tab label=\"📊 Signals & Positions\" />\n            </Tabs>\n          </Box>\n\n          {/* Tab Content */}\n          <Box sx={{ p: 3 }}>\n            {activeTab === 0 && (\n              <HollyChat onPlanExecuted={() => {\n                fetchAccountData();\n                fetchPositions();\n                fetchSignals();\n              }} />\n            )}\n\n            {activeTab === 1 && (\n              <AIFeatures />\n            )}\n\n            {activeTab === 2 && (\n              <Grid container spacing={3}>\n                <Grid item xs={12} md={6}>\n                  <SignalsList signals={signals} />\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <PositionsList positions={positions} />\n                </Grid>\n              </Grid>\n            )}\n          </Box>\n        </Paper>\n      </Container>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,UAAU,CACVC,GAAG,CACHC,MAAM,CACNC,OAAO,CACPC,IAAI,CACJC,WAAW,CACXC,IAAI,CACJC,GAAG,CACHC,MAAM,CACNC,gBAAgB,KACX,eAAe,CACtB,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+B,OAAO,CAAEC,UAAU,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACiC,SAAS,CAAEC,YAAY,CAAC,CAAGlC,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACmC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAEhEC,SAAS,CAAC,IAAM,CACd;AACAoC,gBAAgB,CAAC,CAAC,CAClBC,cAAc,CAAC,CAAC,CAChBC,YAAY,CAAC,CAAC,CAEd;AACA,KAAM,CAAAC,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCJ,gBAAgB,CAAC,CAAC,CAClBC,cAAc,CAAC,CAAC,CAChBC,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMG,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAH,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,iBAAiB,CAAC,CAC/C,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCnB,cAAc,CAACkB,IAAI,CAAC,CACtB,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAV,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mBAAmB,CAAC,CACjD,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCjB,YAAY,CAACgB,IAAI,CAAC,CACpB,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAT,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,iBAAiB,CAAC,CAC/C,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCf,UAAU,CAACc,IAAI,CAAC,CAClB,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAAC,CAED;AACA,GAAIb,iBAAiB,CAAE,CACrB,mBAAOZ,IAAA,CAACF,cAAc,GAAE,CAAC,CAC3B,CAEA,mBACEI,KAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClB5B,IAAA,CAAChB,MAAM,EAAC6C,QAAQ,CAAC,QAAQ,CAACC,EAAE,CAAE,CAAEC,eAAe,CAAE,SAAU,CAAE,CAAAH,QAAA,cAC3D1B,KAAA,CAACjB,OAAO,EAAA2C,QAAA,eACN5B,IAAA,CAAClB,UAAU,EAACkD,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAACH,EAAE,CAAE,CAAEI,QAAQ,CAAE,CAAE,CAAE,CAAAN,QAAA,CAAC,sCAE9D,CAAY,CAAC,cACb5B,IAAA,CAACT,gBAAgB,EACf4C,OAAO,cACLnC,IAAA,CAACV,MAAM,EACL8C,OAAO,CAAExB,iBAAkB,CAC3ByB,QAAQ,CAAGC,CAAC,EAAKzB,oBAAoB,CAACyB,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE,CACxDI,KAAK,CAAC,SAAS,CAChB,CACF,CACDC,KAAK,CAAC,qBAAqB,CAC3BX,EAAE,CAAE,CAAEU,KAAK,CAAE,OAAO,CAAEE,EAAE,CAAE,CAAE,CAAE,CAC/B,CAAC,cACF1C,IAAA,CAAClB,UAAU,EAACkD,OAAO,CAAC,OAAO,CAACF,EAAE,CAAE,CAC9BC,eAAe,CAAE,uBAAuB,CACxCY,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,CAChB,CAAE,CAAAjB,QAAA,CAAC,oBAEH,CAAY,CAAC,EACN,CAAC,CACJ,CAAC,cAET1B,KAAA,CAACvB,SAAS,EAACmE,QAAQ,CAAC,IAAI,CAAChB,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eAErC1B,KAAA,CAACtB,IAAI,EAACoE,SAAS,MAACC,OAAO,CAAE,CAAE,CAACnB,EAAE,CAAE,CAAEoB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACxC5B,IAAA,CAACpB,IAAI,EAACuE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzB,QAAA,cACvB5B,IAAA,CAACJ,WAAW,EAACQ,WAAW,CAAEA,WAAY,CAAE,CAAC,CACrC,CAAC,cACPJ,IAAA,CAACpB,IAAI,EAACuE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzB,QAAA,cACvB5B,IAAA,CAACP,SAAS,EACRW,WAAW,CAAEA,WAAY,CACzBE,SAAS,CAAEA,SAAU,CACrBE,OAAO,CAAEA,OAAQ,CAClB,CAAC,CACE,CAAC,EACH,CAAC,cAGPN,KAAA,CAACrB,KAAK,EAACiD,EAAE,CAAE,CAAEwB,KAAK,CAAE,MAAO,CAAE,CAAA1B,QAAA,eAC3B5B,IAAA,CAACjB,GAAG,EAAC+C,EAAE,CAAE,CAAEyB,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAU,CAAE,CAAA5B,QAAA,cACnD1B,KAAA,CAACd,IAAI,EACHqE,KAAK,CAAE/C,SAAU,CACjB2B,QAAQ,CAAEA,CAACqB,KAAK,CAAEC,QAAQ,GAAKhD,YAAY,CAACgD,QAAQ,CAAE,CACtD3B,OAAO,CAAC,WAAW,CACnBF,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChB8B,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,MACjB,CACF,CAAE,CAAAlC,QAAA,eAEF5B,IAAA,CAACX,GAAG,EAACoD,KAAK,CAAC,4BAAkB,CAAE,CAAC,cAChCzC,IAAA,CAACX,GAAG,EAACoD,KAAK,CAAC,0BAAgB,CAAE,CAAC,cAC9BzC,IAAA,CAACX,GAAG,EAACoD,KAAK,CAAC,kCAAwB,CAAE,CAAC,EAClC,CAAC,CACJ,CAAC,cAGNvC,KAAA,CAACnB,GAAG,EAAC+C,EAAE,CAAE,CAAEiC,CAAC,CAAE,CAAE,CAAE,CAAAnC,QAAA,EACflB,SAAS,GAAK,CAAC,eACdV,IAAA,CAACR,SAAS,EAACwE,cAAc,CAAEA,CAAA,GAAM,CAC/BlD,gBAAgB,CAAC,CAAC,CAClBC,cAAc,CAAC,CAAC,CAChBC,YAAY,CAAC,CAAC,CAChB,CAAE,CAAE,CACL,CAEAN,SAAS,GAAK,CAAC,eACdV,IAAA,CAACH,UAAU,GAAE,CACd,CAEAa,SAAS,GAAK,CAAC,eACdR,KAAA,CAACtB,IAAI,EAACoE,SAAS,MAACC,OAAO,CAAE,CAAE,CAAArB,QAAA,eACzB5B,IAAA,CAACpB,IAAI,EAACuE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzB,QAAA,cACvB5B,IAAA,CAACN,WAAW,EAACc,OAAO,CAAEA,OAAQ,CAAE,CAAC,CAC7B,CAAC,cACPR,IAAA,CAACpB,IAAI,EAACuE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzB,QAAA,cACvB5B,IAAA,CAACL,aAAa,EAACW,SAAS,CAAEA,SAAU,CAAE,CAAC,CACnC,CAAC,EACH,CACP,EACE,CAAC,EACD,CAAC,EACC,CAAC,EACT,CAAC,CAEV,CAEA,cAAe,CAAAH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}