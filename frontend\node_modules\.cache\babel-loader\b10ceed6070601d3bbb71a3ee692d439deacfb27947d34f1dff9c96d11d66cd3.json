{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CloudLightning = createLucideIcon(\"CloudLightning\", [[\"path\", {\n  d: \"M6 16.326A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 .5 8.973\",\n  key: \"1cez44\"\n}], [\"path\", {\n  d: \"m13 12-3 5h4l-3 5\",\n  key: \"1t22er\"\n}]]);\nexport { CloudLightning as default };", "map": {"version": 3, "names": ["CloudLightning", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\cloud-lightning.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CloudLightning\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAxNi4zMjZBNyA3IDAgMSAxIDE1LjcxIDhoMS43OWE0LjUgNC41IDAgMCAxIC41IDguOTczIiAvPgogIDxwYXRoIGQ9Im0xMyAxMi0zIDVoNGwtMyA1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/cloud-lightning\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CloudLightning = createLucideIcon('CloudLightning', [\n  ['path', { d: 'M6 16.326A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 .5 8.973', key: '1cez44' }],\n  ['path', { d: 'm13 12-3 5h4l-3 5', key: '1t22er' }],\n]);\n\nexport default CloudLightning;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yDAA2D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxF,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}