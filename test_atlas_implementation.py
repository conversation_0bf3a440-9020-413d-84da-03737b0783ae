#!/usr/bin/env python3
"""
A.T.L.A.S. Implementation Test Script
Test all the critical fixes and enhancements we implemented
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# Add streamlined directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlined'))

async def test_atlas_implementation():
    """Test all A.T.L.A.S. implementation features"""
    
    print("🧪 " + "="*60)
    print("🧪 A.T.L.A.S. IMPLEMENTATION TEST SUITE")
    print("🧪 " + "="*60)
    print()
    
    test_results = {
        "api_routing": False,
        "ttm_squeeze_chart": False,
        "system_identity": False,
        "dual_chat_interface": False,
        "full_capabilities": False,
        "backend_integration": False
    }
    
    # Test 1: API Routing and Response Source
    print("📡 TEST 1: API Routing and Response Source")
    try:
        from atlas_server import app
        from atlas_predicto_engine import PredictoConversationalEngine
        
        # Check if Predicto engine is properly integrated
        predicto = PredictoConversationalEngine()
        if hasattr(predicto, 'process_conversation'):
            print("   ✅ Predicto engine properly integrated")
            test_results["api_routing"] = True
        else:
            print("   ❌ Predicto engine missing process_conversation method")
            
    except Exception as e:
        print(f"   ❌ API routing test failed: {e}")
    
    # Test 2: TTM Squeeze Chart Implementation
    print("\n📊 TEST 2: TTM Squeeze Chart Implementation")
    try:
        from atlas_market_engine import AtlasMarketEngine
        
        market_engine = AtlasMarketEngine()
        if hasattr(market_engine, 'get_ttm_squeeze_chart_data'):
            print("   ✅ TTM Squeeze chart data method implemented")
            test_results["ttm_squeeze_chart"] = True
        else:
            print("   ❌ TTM Squeeze chart data method missing")
            
    except Exception as e:
        print(f"   ❌ TTM Squeeze test failed: {e}")
    
    # Test 3: System Identity and Capabilities
    print("\n🤖 TEST 3: System Identity and Capabilities")
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        
        predicto = PredictoConversationalEngine()
        if hasattr(predicto, 'system_prompt'):
            system_prompt = predicto.system_prompt
            if "A.T.L.A.S. powered by Predicto" in system_prompt:
                print("   ✅ Correct system identity in prompt")
                test_results["system_identity"] = True
            else:
                print("   ❌ System identity not properly set")
        else:
            print("   ❌ System prompt not found")
            
    except Exception as e:
        print(f"   ❌ System identity test failed: {e}")
    
    # Test 4: Dual Chat Interface Functionality
    print("\n💬 TEST 4: Dual Chat Interface Functionality")
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        
        predicto = PredictoConversationalEngine()
        if hasattr(predicto, '_process_pattern_scanner_request'):
            print("   ✅ Pattern scanner processing implemented")
            test_results["dual_chat_interface"] = True
        else:
            print("   ❌ Pattern scanner processing missing")
            
    except Exception as e:
        print(f"   ❌ Dual chat interface test failed: {e}")
    
    # Test 5: Full A.T.L.A.S. Capabilities Integration
    print("\n🔧 TEST 5: Full A.T.L.A.S. Capabilities Integration")
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        
        predicto = PredictoConversationalEngine()
        capabilities = [
            '_execute_ttm_squeeze_scan',
            '_execute_trade_request', 
            '_manage_alerts',
            '_execute_market_scan'
        ]
        
        missing_capabilities = []
        for cap in capabilities:
            if not hasattr(predicto, cap):
                missing_capabilities.append(cap)
        
        if not missing_capabilities:
            print("   ✅ All key capabilities implemented")
            test_results["full_capabilities"] = True
        else:
            print(f"   ❌ Missing capabilities: {missing_capabilities}")
            
    except Exception as e:
        print(f"   ❌ Capabilities integration test failed: {e}")
    
    # Test 6: Backend-Frontend Integration
    print("\n🔗 TEST 6: Backend-Frontend Integration")
    try:
        from atlas_server import app
        from fastapi.testclient import TestClient
        
        # Check if test endpoints exist
        client = TestClient(app)
        
        # Test capabilities endpoint
        response = client.get("/api/v1/test/capabilities")
        if response.status_code == 200:
            print("   ✅ Test capabilities endpoint working")
            test_results["backend_integration"] = True
        else:
            print(f"   ❌ Test capabilities endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Backend integration test failed: {e}")
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST RESULTS SUMMARY")
    print("="*60)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Overall Score: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! A.T.L.A.S. implementation is complete!")
    elif passed_tests >= total_tests * 0.8:
        print("✅ Most tests passed! Implementation is mostly complete.")
    else:
        print("⚠️  Some tests failed. Implementation needs attention.")
    
    return test_results

def test_frontend_components():
    """Test frontend component structure"""
    print("\n🌐 FRONTEND COMPONENT TEST")
    print("="*40)
    
    frontend_files = [
        "frontend/src/components/AtlasInterface.js",
        "frontend/src/App.js",
        "frontend/package.json"
    ]
    
    for file_path in frontend_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} exists")
        else:
            print(f"   ❌ {file_path} missing")

if __name__ == "__main__":
    print("🚀 Starting A.T.L.A.S. Implementation Test...")
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run async tests
    asyncio.run(test_atlas_implementation())
    
    # Test frontend components
    test_frontend_components()
    
    print("\n🏁 Test completed!")
