#!/usr/bin/env python3
"""
Quick Test Runner for A.T.L.A.S. Comprehensive Test Suite
"""

import sys
import os
import time
import requests
from datetime import datetime

# Add paths
sys.path.append('5_tests_checks')
sys.path.append('1_main_chat_engine')
sys.path.append('4_helper_tools')

def check_server():
    """Check if A.T.L.A.S. server is running"""
    try:
        response = requests.get("http://localhost:8080/api/v1/health", timeout=5)
        return response.status_code == 200, response.status_code
    except Exception as e:
        return False, str(e)

def run_quick_tests():
    """Run a quick subset of tests to validate the system"""
    print("🔮 A.T.L.A.S. QUICK TEST RUNNER")
    print("=" * 50)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check server status
    print("🔍 Checking A.T.L.A.S. server...")
    server_running, server_status = check_server()
    
    if server_running:
        print(f"✅ Server is running (Status: {server_status})")
    else:
        print(f"❌ Server not accessible: {server_status}")
        print("⚠️  Some tests will be skipped")
    
    print()
    
    # Import test suite
    try:
        from comprehensive_atlas_test_suite import ATLASTestSuite
        print("✅ Test suite imported successfully")
        
        suite = ATLASTestSuite()
        test_methods = [method for method in dir(suite) if method.startswith('test_')]
        print(f"✅ Found {len(test_methods)} test methods")
        
    except Exception as e:
        print(f"❌ Failed to import test suite: {e}")
        return
    
    print()
    
    # Test 6-point format validator
    print("🧪 Testing 6-Point Format Validator...")
    sample_response = """
    **A.T.L.A.S powered by Predicto - Stock Market God**
    
    **1. Why This Trade?**
    Apple just bounced off support with institutional buying
    
    **2. Win/Loss Probabilities**
    78% chance you hit the profit target, 22% chance you hit your stop
    
    **3. Potential Money In or Out**
    If you buy 100 shares at $175.25, you could make $750 or lose $420
    
    **4. Smart Stop Plans**
    We'll exit at $171.00 if price drops 2.4% below entry
    
    **5. Market Context**
    Tech stocks are strong today with semiconductor momentum
    
    **6. Confidence Score**
    Confidence: 87%
    """
    
    try:
        validation = suite._validate_6_point_format(sample_response)
        print(f"   📊 Format Score: {validation['score']}/6 ({validation['percentage']:.1f}%)")
        print(f"   ✅ Found: {', '.join(validation['found_sections'])}")
        if validation['missing_sections']:
            print(f"   ❌ Missing: {', '.join(validation['missing_sections'])}")
        
        if validation['score'] >= 5:
            print("   🎉 6-Point Format Validator: PASSED")
        else:
            print("   ❌ 6-Point Format Validator: FAILED")
            
    except Exception as e:
        print(f"   ❌ Validator Error: {e}")
    
    print()
    
    # Quick server tests if available
    if server_running:
        print("🧪 Running Quick Server Tests...")
        
        # Test 1: Health endpoint
        try:
            response = requests.get("http://localhost:8080/api/v1/health", timeout=10)
            if response.status_code == 200:
                print("   ✅ Health Endpoint: PASSED")
            else:
                print(f"   ❌ Health Endpoint: FAILED ({response.status_code})")
        except Exception as e:
            print(f"   ❌ Health Endpoint: ERROR ({e})")
        
        # Test 2: Chat endpoint with Stock Market God format
        try:
            chat_response = requests.post(
                "http://localhost:8080/api/v1/chat",
                json={
                    "message": "What's Apple's price right now?",
                    "session_id": "quick_test"
                },
                timeout=30
            )
            
            if chat_response.status_code == 200:
                data = chat_response.json()
                response_text = data.get("response", "")
                
                # Validate 6-point format
                validation = suite._validate_6_point_format(response_text)
                
                if validation['score'] >= 5:
                    print("   ✅ Chat + 6-Point Format: PASSED")
                    print(f"      Format Score: {validation['score']}/6")
                else:
                    print("   ⚠️  Chat Endpoint: PARTIAL")
                    print(f"      Format Score: {validation['score']}/6")
                    print(f"      Missing: {validation['missing_sections']}")
            else:
                print(f"   ❌ Chat Endpoint: FAILED ({chat_response.status_code})")
                
        except Exception as e:
            print(f"   ❌ Chat Endpoint: ERROR ({e})")
    
    print()
    
    # Summary
    print("📊 QUICK TEST SUMMARY")
    print("-" * 30)
    print("✅ Test suite structure: READY")
    print("✅ 6-point format validator: WORKING")
    
    if server_running:
        print("✅ Server connectivity: WORKING")
        print("🎯 System appears ready for full testing!")
    else:
        print("⚠️  Server connectivity: NOT AVAILABLE")
        print("📝 Start A.T.L.A.S. server to run full tests")
    
    print()
    print("🚀 To run full 80+ test suite:")
    print("   1. Start A.T.L.A.S. server: python 1_main_chat_engine/atlas_server.py")
    print("   2. Run tests: python 5_tests_checks/run_comprehensive_tests.py")
    print()
    print(f"Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    run_quick_tests()
