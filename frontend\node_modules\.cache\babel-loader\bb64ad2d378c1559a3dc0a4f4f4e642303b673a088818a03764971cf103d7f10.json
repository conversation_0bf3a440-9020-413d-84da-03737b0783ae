{"ast": null, "code": "import { getEasingForSegment } from '../../../easing/utils/get-easing-for-segment.mjs';\nimport { removeItem } from '../../../utils/array.mjs';\nimport { mix } from '../../../utils/mix.mjs';\nfunction eraseKeyframes(sequence, startTime, endTime) {\n  for (let i = 0; i < sequence.length; i++) {\n    const keyframe = sequence[i];\n    if (keyframe.at > startTime && keyframe.at < endTime) {\n      removeItem(sequence, keyframe);\n      // If we remove this item we have to push the pointer back one\n      i--;\n    }\n  }\n}\nfunction addKeyframes(sequence, keyframes, easing, offset, startTime, endTime) {\n  /**\n   * Erase every existing value between currentTime and targetTime,\n   * this will essentially splice this timeline into any currently\n   * defined ones.\n   */\n  eraseKeyframes(sequence, startTime, endTime);\n  for (let i = 0; i < keyframes.length; i++) {\n    sequence.push({\n      value: keyframes[i],\n      at: mix(startTime, endTime, offset[i]),\n      easing: getEasingForSegment(easing, i)\n    });\n  }\n}\nexport { addKeyframes, eraseKeyframes };", "map": {"version": 3, "names": ["getEasingForSegment", "removeItem", "mix", "eraseKeyframes", "sequence", "startTime", "endTime", "i", "length", "keyframe", "at", "addKeyframes", "keyframes", "easing", "offset", "push", "value"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/animation/sequence/utils/edit.mjs"], "sourcesContent": ["import { getEasingForSegment } from '../../../easing/utils/get-easing-for-segment.mjs';\nimport { removeItem } from '../../../utils/array.mjs';\nimport { mix } from '../../../utils/mix.mjs';\n\nfunction eraseKeyframes(sequence, startTime, endTime) {\n    for (let i = 0; i < sequence.length; i++) {\n        const keyframe = sequence[i];\n        if (keyframe.at > startTime && keyframe.at < endTime) {\n            removeItem(sequence, keyframe);\n            // If we remove this item we have to push the pointer back one\n            i--;\n        }\n    }\n}\nfunction addKeyframes(sequence, keyframes, easing, offset, startTime, endTime) {\n    /**\n     * Erase every existing value between currentTime and targetTime,\n     * this will essentially splice this timeline into any currently\n     * defined ones.\n     */\n    eraseKeyframes(sequence, startTime, endTime);\n    for (let i = 0; i < keyframes.length; i++) {\n        sequence.push({\n            value: keyframes[i],\n            at: mix(startTime, endTime, offset[i]),\n            easing: getEasingForSegment(easing, i),\n        });\n    }\n}\n\nexport { addKeyframes, eraseKeyframes };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,GAAG,QAAQ,wBAAwB;AAE5C,SAASC,cAAcA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAME,QAAQ,GAAGL,QAAQ,CAACG,CAAC,CAAC;IAC5B,IAAIE,QAAQ,CAACC,EAAE,GAAGL,SAAS,IAAII,QAAQ,CAACC,EAAE,GAAGJ,OAAO,EAAE;MAClDL,UAAU,CAACG,QAAQ,EAAEK,QAAQ,CAAC;MAC9B;MACAF,CAAC,EAAE;IACP;EACJ;AACJ;AACA,SAASI,YAAYA,CAACP,QAAQ,EAAEQ,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAET,SAAS,EAAEC,OAAO,EAAE;EAC3E;AACJ;AACA;AACA;AACA;EACIH,cAAc,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,SAAS,CAACJ,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCH,QAAQ,CAACW,IAAI,CAAC;MACVC,KAAK,EAAEJ,SAAS,CAACL,CAAC,CAAC;MACnBG,EAAE,EAAER,GAAG,CAACG,SAAS,EAAEC,OAAO,EAAEQ,MAAM,CAACP,CAAC,CAAC,CAAC;MACtCM,MAAM,EAAEb,mBAAmB,CAACa,MAAM,EAAEN,CAAC;IACzC,CAAC,CAAC;EACN;AACJ;AAEA,SAASI,YAAY,EAAER,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}