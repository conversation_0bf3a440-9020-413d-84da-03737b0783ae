{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"m7.6 8.67-2.01.8c-.22.09-.34.31-.31.54l2.4 5.98h1.23l-.62-6.9c-.04-.34-.38-.55-.69-.42M3.07 16.1c-.27.53.29 1.09.82.83l1.68-.84-1.08-2.71zm10.29-9.11h-2.71c-.27 0-.53.23-.5.54l.77 8.45h2.17l.77-8.45c.02-.31-.23-.54-.5-.54m5.05 2.48-2.01-.8c-.31-.12-.65.09-.68.42l-.62 6.9h1.23l2.4-5.98c.02-.23-.1-.45-.32-.54m1.11 3.92-1.08 2.7 1.68.84c.52.26 1.09-.3.82-.83z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M20.5 10.94c.13-.32.1-.23.15-.39.3-1.21-.34-2.47-1.5-2.93l-2.01-.8c-.46-.18-.95-.21-1.41-.12-.11-.33-.29-.63-.52-.89-.48-.52-1.15-.81-1.85-.81h-2.71c-.71 0-1.38.29-1.85.81-.24.26-.42.56-.53.88-.46-.09-.95-.06-1.41.12l-2.01.8c-1.16.46-1.8 1.72-1.5 2.93l.15.38C1.1 15.55 1 15.55 1 16.38c0 .91.46 1.74 1.24 2.22 1.42.88 2.49.14 4-.61h11.53c1.52.76 1.86 1.01 2.63 1.01 1 0 2.61-.77 2.61-2.61-.01-.85-.13-.88-2.51-5.45M3.88 16.93c-.53.26-1.09-.3-.82-.83l1.41-2.72 1.08 2.71zm3.8-.94-2.4-5.98c-.03-.23.09-.45.31-.54l2.01-.8c.31-.12.65.08.68.42l.62 6.9zm5.41 0h-2.17l-.77-8.45c-.03-.31.23-.54.5-.54h2.71c.27 0 .53.23.5.54zm3.23 0h-1.23l.62-6.9c.03-.33.37-.54.68-.42l2.01.8c.22.09.34.31.31.54zm3.8.94-1.68-.84 1.08-2.7 1.41 2.71c.28.53-.29 1.09-.81.83\"\n}, \"1\")], 'BakeryDiningTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/BakeryDiningTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"m7.6 8.67-2.01.8c-.22.09-.34.31-.31.54l2.4 5.98h1.23l-.62-6.9c-.04-.34-.38-.55-.69-.42M3.07 16.1c-.27.53.29 1.09.82.83l1.68-.84-1.08-2.71zm10.29-9.11h-2.71c-.27 0-.53.23-.5.54l.77 8.45h2.17l.77-8.45c.02-.31-.23-.54-.5-.54m5.05 2.48-2.01-.8c-.31-.12-.65.09-.68.42l-.62 6.9h1.23l2.4-5.98c.02-.23-.1-.45-.32-.54m1.11 3.92-1.08 2.7 1.68.84c.52.26 1.09-.3.82-.83z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M20.5 10.94c.13-.32.1-.23.15-.39.3-1.21-.34-2.47-1.5-2.93l-2.01-.8c-.46-.18-.95-.21-1.41-.12-.11-.33-.29-.63-.52-.89-.48-.52-1.15-.81-1.85-.81h-2.71c-.71 0-1.38.29-1.85.81-.24.26-.42.56-.53.88-.46-.09-.95-.06-1.41.12l-2.01.8c-1.16.46-1.8 1.72-1.5 2.93l.15.38C1.1 15.55 1 15.55 1 16.38c0 .91.46 1.74 1.24 2.22 1.42.88 2.49.14 4-.61h11.53c1.52.76 1.86 1.01 2.63 1.01 1 0 2.61-.77 2.61-2.61-.01-.85-.13-.88-2.51-5.45M3.88 16.93c-.53.26-1.09-.3-.82-.83l1.41-2.72 1.08 2.71zm3.8-.94-2.4-5.98c-.03-.23.09-.45.31-.54l2.01-.8c.31-.12.65.08.68.42l.62 6.9zm5.41 0h-2.17l-.77-8.45c-.03-.31.23-.54.5-.54h2.71c.27 0 .53.23.5.54zm3.23 0h-1.23l.62-6.9c.03-.33.37-.54.68-.42l2.01.8c.22.09.34.31.31.54zm3.8.94-1.68-.84 1.08-2.7 1.41 2.71c.28.53-.29 1.09-.81.83\"\n}, \"1\")], 'BakeryDiningTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,wWAAwW;EAC3WC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}