{"ast": null, "code": "'use client';\n\nexport { default } from './Skeleton';\nexport * from './skeletonClasses';\nexport { default as skeletonClasses } from './skeletonClasses';", "map": {"version": 3, "names": ["default", "skeletonClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Skeleton/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Skeleton';\nexport * from './skeletonClasses';\nexport { default as skeletonClasses } from './skeletonClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY;AACpC,cAAc,mBAAmB;AACjC,SAASA,OAAO,IAAIC,eAAe,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}