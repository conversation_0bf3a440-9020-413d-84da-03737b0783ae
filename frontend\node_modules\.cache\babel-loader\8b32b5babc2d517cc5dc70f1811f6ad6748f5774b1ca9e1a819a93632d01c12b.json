{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"duration\", \"easing\", \"delay\"];\n// Follow https://material.google.com/motion/duration-easing.html#duration-easing-natural-easing-curves\n// to learn the context in which each easing should be used.\nexport const easing = {\n  // This is the most common easing curve.\n  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n  // Objects enter the screen at full velocity from off-screen and\n  // slowly decelerate to a resting point.\n  easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',\n  // Objects leave the screen at full velocity. They do not decelerate when off-screen.\n  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n  // The sharp curve is used by objects that may return to the screen at any time.\n  sharp: 'cubic-bezier(0.4, 0, 0.6, 1)'\n};\n\n// Follow https://m2.material.io/guidelines/motion/duration-easing.html#duration-easing-common-durations\n// to learn when use what timing\nexport const duration = {\n  shortest: 150,\n  shorter: 200,\n  short: 250,\n  // most basic recommended timing\n  standard: 300,\n  // this is to be used in complex animations\n  complex: 375,\n  // recommended when something is entering screen\n  enteringScreen: 225,\n  // recommended when something is leaving screen\n  leavingScreen: 195\n};\nfunction formatMs(milliseconds) {\n  return `${Math.round(milliseconds)}ms`;\n}\nfunction getAutoHeightDuration(height) {\n  if (!height) {\n    return 0;\n  }\n  const constant = height / 36;\n\n  // https://www.wolframalpha.com/input/?i=(4+%2B+15+*+(x+%2F+36+)+**+0.25+%2B+(x+%2F+36)+%2F+5)+*+10\n  return Math.round((4 + 15 * constant ** 0.25 + constant / 5) * 10);\n}\nexport default function createTransitions(inputTransitions) {\n  const mergedEasing = _extends({}, easing, inputTransitions.easing);\n  const mergedDuration = _extends({}, duration, inputTransitions.duration);\n  const create = (props = ['all'], options = {}) => {\n    const {\n        duration: durationOption = mergedDuration.standard,\n        easing: easingOption = mergedEasing.easeInOut,\n        delay = 0\n      } = options,\n      other = _objectWithoutPropertiesLoose(options, _excluded);\n    if (process.env.NODE_ENV !== 'production') {\n      const isString = value => typeof value === 'string';\n      // IE11 support, replace with Number.isNaN\n      // eslint-disable-next-line no-restricted-globals\n      const isNumber = value => !isNaN(parseFloat(value));\n      if (!isString(props) && !Array.isArray(props)) {\n        console.error('MUI: Argument \"props\" must be a string or Array.');\n      }\n      if (!isNumber(durationOption) && !isString(durationOption)) {\n        console.error(`MUI: Argument \"duration\" must be a number or a string but found ${durationOption}.`);\n      }\n      if (!isString(easingOption)) {\n        console.error('MUI: Argument \"easing\" must be a string.');\n      }\n      if (!isNumber(delay) && !isString(delay)) {\n        console.error('MUI: Argument \"delay\" must be a number or a string.');\n      }\n      if (typeof options !== 'object') {\n        console.error(['MUI: Secong argument of transition.create must be an object.', \"Arguments should be either `create('prop1', options)` or `create(['prop1', 'prop2'], options)`\"].join('\\n'));\n      }\n      if (Object.keys(other).length !== 0) {\n        console.error(`MUI: Unrecognized argument(s) [${Object.keys(other).join(',')}].`);\n      }\n    }\n    return (Array.isArray(props) ? props : [props]).map(animatedProp => `${animatedProp} ${typeof durationOption === 'string' ? durationOption : formatMs(durationOption)} ${easingOption} ${typeof delay === 'string' ? delay : formatMs(delay)}`).join(',');\n  };\n  return _extends({\n    getAutoHeightDuration,\n    create\n  }, inputTransitions, {\n    easing: mergedEasing,\n    duration: mergedDuration\n  });\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "easing", "easeInOut", "easeOut", "easeIn", "sharp", "duration", "shortest", "shorter", "short", "standard", "complex", "enteringScreen", "leavingScreen", "formatMs", "milliseconds", "Math", "round", "getAutoHeightDuration", "height", "constant", "createTransitions", "inputTransitions", "mergedEasing", "mergedDuration", "create", "props", "options", "durationOption", "easingOption", "delay", "other", "process", "env", "NODE_ENV", "isString", "value", "isNumber", "isNaN", "parseFloat", "Array", "isArray", "console", "error", "join", "Object", "keys", "length", "map", "animatedProp"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/styles/createTransitions.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"duration\", \"easing\", \"delay\"];\n// Follow https://material.google.com/motion/duration-easing.html#duration-easing-natural-easing-curves\n// to learn the context in which each easing should be used.\nexport const easing = {\n  // This is the most common easing curve.\n  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n  // Objects enter the screen at full velocity from off-screen and\n  // slowly decelerate to a resting point.\n  easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',\n  // Objects leave the screen at full velocity. They do not decelerate when off-screen.\n  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n  // The sharp curve is used by objects that may return to the screen at any time.\n  sharp: 'cubic-bezier(0.4, 0, 0.6, 1)'\n};\n\n// Follow https://m2.material.io/guidelines/motion/duration-easing.html#duration-easing-common-durations\n// to learn when use what timing\nexport const duration = {\n  shortest: 150,\n  shorter: 200,\n  short: 250,\n  // most basic recommended timing\n  standard: 300,\n  // this is to be used in complex animations\n  complex: 375,\n  // recommended when something is entering screen\n  enteringScreen: 225,\n  // recommended when something is leaving screen\n  leavingScreen: 195\n};\nfunction formatMs(milliseconds) {\n  return `${Math.round(milliseconds)}ms`;\n}\nfunction getAutoHeightDuration(height) {\n  if (!height) {\n    return 0;\n  }\n  const constant = height / 36;\n\n  // https://www.wolframalpha.com/input/?i=(4+%2B+15+*+(x+%2F+36+)+**+0.25+%2B+(x+%2F+36)+%2F+5)+*+10\n  return Math.round((4 + 15 * constant ** 0.25 + constant / 5) * 10);\n}\nexport default function createTransitions(inputTransitions) {\n  const mergedEasing = _extends({}, easing, inputTransitions.easing);\n  const mergedDuration = _extends({}, duration, inputTransitions.duration);\n  const create = (props = ['all'], options = {}) => {\n    const {\n        duration: durationOption = mergedDuration.standard,\n        easing: easingOption = mergedEasing.easeInOut,\n        delay = 0\n      } = options,\n      other = _objectWithoutPropertiesLoose(options, _excluded);\n    if (process.env.NODE_ENV !== 'production') {\n      const isString = value => typeof value === 'string';\n      // IE11 support, replace with Number.isNaN\n      // eslint-disable-next-line no-restricted-globals\n      const isNumber = value => !isNaN(parseFloat(value));\n      if (!isString(props) && !Array.isArray(props)) {\n        console.error('MUI: Argument \"props\" must be a string or Array.');\n      }\n      if (!isNumber(durationOption) && !isString(durationOption)) {\n        console.error(`MUI: Argument \"duration\" must be a number or a string but found ${durationOption}.`);\n      }\n      if (!isString(easingOption)) {\n        console.error('MUI: Argument \"easing\" must be a string.');\n      }\n      if (!isNumber(delay) && !isString(delay)) {\n        console.error('MUI: Argument \"delay\" must be a number or a string.');\n      }\n      if (typeof options !== 'object') {\n        console.error(['MUI: Secong argument of transition.create must be an object.', \"Arguments should be either `create('prop1', options)` or `create(['prop1', 'prop2'], options)`\"].join('\\n'));\n      }\n      if (Object.keys(other).length !== 0) {\n        console.error(`MUI: Unrecognized argument(s) [${Object.keys(other).join(',')}].`);\n      }\n    }\n    return (Array.isArray(props) ? props : [props]).map(animatedProp => `${animatedProp} ${typeof durationOption === 'string' ? durationOption : formatMs(durationOption)} ${easingOption} ${typeof delay === 'string' ? delay : formatMs(delay)}`).join(',');\n  };\n  return _extends({\n    getAutoHeightDuration,\n    create\n  }, inputTransitions, {\n    easing: mergedEasing,\n    duration: mergedDuration\n  });\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;AACjD;AACA;AACA,OAAO,MAAMC,MAAM,GAAG;EACpB;EACAC,SAAS,EAAE,8BAA8B;EACzC;EACA;EACAC,OAAO,EAAE,8BAA8B;EACvC;EACAC,MAAM,EAAE,4BAA4B;EACpC;EACAC,KAAK,EAAE;AACT,CAAC;;AAED;AACA;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,QAAQ,EAAE,GAAG;EACbC,OAAO,EAAE,GAAG;EACZC,KAAK,EAAE,GAAG;EACV;EACAC,QAAQ,EAAE,GAAG;EACb;EACAC,OAAO,EAAE,GAAG;EACZ;EACAC,cAAc,EAAE,GAAG;EACnB;EACAC,aAAa,EAAE;AACjB,CAAC;AACD,SAASC,QAAQA,CAACC,YAAY,EAAE;EAC9B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,YAAY,CAAC,IAAI;AACxC;AACA,SAASG,qBAAqBA,CAACC,MAAM,EAAE;EACrC,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAAC;EACV;EACA,MAAMC,QAAQ,GAAGD,MAAM,GAAG,EAAE;;EAE5B;EACA,OAAOH,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGG,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;AACpE;AACA,eAAe,SAASC,iBAAiBA,CAACC,gBAAgB,EAAE;EAC1D,MAAMC,YAAY,GAAGxB,QAAQ,CAAC,CAAC,CAAC,EAAEE,MAAM,EAAEqB,gBAAgB,CAACrB,MAAM,CAAC;EAClE,MAAMuB,cAAc,GAAGzB,QAAQ,CAAC,CAAC,CAAC,EAAEO,QAAQ,EAAEgB,gBAAgB,CAAChB,QAAQ,CAAC;EACxE,MAAMmB,MAAM,GAAGA,CAACC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAChD,MAAM;QACFrB,QAAQ,EAAEsB,cAAc,GAAGJ,cAAc,CAACd,QAAQ;QAClDT,MAAM,EAAE4B,YAAY,GAAGN,YAAY,CAACrB,SAAS;QAC7C4B,KAAK,GAAG;MACV,CAAC,GAAGH,OAAO;MACXI,KAAK,GAAGjC,6BAA6B,CAAC6B,OAAO,EAAE3B,SAAS,CAAC;IAC3D,IAAIgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMC,QAAQ,GAAGC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;MACnD;MACA;MACA,MAAMC,QAAQ,GAAGD,KAAK,IAAI,CAACE,KAAK,CAACC,UAAU,CAACH,KAAK,CAAC,CAAC;MACnD,IAAI,CAACD,QAAQ,CAACT,KAAK,CAAC,IAAI,CAACc,KAAK,CAACC,OAAO,CAACf,KAAK,CAAC,EAAE;QAC7CgB,OAAO,CAACC,KAAK,CAAC,kDAAkD,CAAC;MACnE;MACA,IAAI,CAACN,QAAQ,CAACT,cAAc,CAAC,IAAI,CAACO,QAAQ,CAACP,cAAc,CAAC,EAAE;QAC1Dc,OAAO,CAACC,KAAK,CAAC,mEAAmEf,cAAc,GAAG,CAAC;MACrG;MACA,IAAI,CAACO,QAAQ,CAACN,YAAY,CAAC,EAAE;QAC3Ba,OAAO,CAACC,KAAK,CAAC,0CAA0C,CAAC;MAC3D;MACA,IAAI,CAACN,QAAQ,CAACP,KAAK,CAAC,IAAI,CAACK,QAAQ,CAACL,KAAK,CAAC,EAAE;QACxCY,OAAO,CAACC,KAAK,CAAC,qDAAqD,CAAC;MACtE;MACA,IAAI,OAAOhB,OAAO,KAAK,QAAQ,EAAE;QAC/Be,OAAO,CAACC,KAAK,CAAC,CAAC,8DAA8D,EAAE,gGAAgG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC9L;MACA,IAAIC,MAAM,CAACC,IAAI,CAACf,KAAK,CAAC,CAACgB,MAAM,KAAK,CAAC,EAAE;QACnCL,OAAO,CAACC,KAAK,CAAC,kCAAkCE,MAAM,CAACC,IAAI,CAACf,KAAK,CAAC,CAACa,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;MACnF;IACF;IACA,OAAO,CAACJ,KAAK,CAACC,OAAO,CAACf,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEsB,GAAG,CAACC,YAAY,IAAI,GAAGA,YAAY,IAAI,OAAOrB,cAAc,KAAK,QAAQ,GAAGA,cAAc,GAAGd,QAAQ,CAACc,cAAc,CAAC,IAAIC,YAAY,IAAI,OAAOC,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGhB,QAAQ,CAACgB,KAAK,CAAC,EAAE,CAAC,CAACc,IAAI,CAAC,GAAG,CAAC;EAC3P,CAAC;EACD,OAAO7C,QAAQ,CAAC;IACdmB,qBAAqB;IACrBO;EACF,CAAC,EAAEH,gBAAgB,EAAE;IACnBrB,MAAM,EAAEsB,YAAY;IACpBjB,QAAQ,EAAEkB;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}