{"ast": null, "code": "'use client';\n\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nimport * as React from 'react';\nexport default function useControlled(_ref) {\n  let {\n    controlled,\n    default: defaultProp,\n    name,\n    state = 'value'\n  } = _ref;\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([\"MUI: A component is changing the \".concat(isControlled ? '' : 'un', \"controlled \").concat(state, \" state of \").concat(name, \" to be \").concat(isControlled ? 'un' : '', \"controlled.\"), 'Elements should not switch from uncontrolled to controlled (or vice versa).', \"Decide between using a controlled or uncontrolled \".concat(name, \" \") + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([\"MUI: A component is changing the default \".concat(state, \" state of an uncontrolled \").concat(name, \" after being initialized. \") + \"To suppress this warning opt to use a controlled \".concat(name, \".\")].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n  return [value, setValueIfUncontrolled];\n}", "map": {"version": 3, "names": ["React", "useControlled", "_ref", "controlled", "default", "defaultProp", "name", "state", "current", "isControlled", "useRef", "undefined", "valueState", "setValue", "useState", "value", "process", "env", "NODE_ENV", "useEffect", "console", "error", "concat", "join", "defaultValue", "Object", "is", "JSON", "stringify", "setValueIfUncontrolled", "useCallback", "newValue"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/utils/esm/useControlled/useControlled.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nimport * as React from 'react';\nexport default function useControlled({\n  controlled,\n  default: defaultProp,\n  name,\n  state = 'value'\n}) {\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n  return [value, setValueIfUncontrolled];\n}"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,aAAaA,CAAAC,IAAA,EAKlC;EAAA,IALmC;IACpCC,UAAU;IACVC,OAAO,EAAEC,WAAW;IACpBC,IAAI;IACJC,KAAK,GAAG;EACV,CAAC,GAAAL,IAAA;EACC;EACA,MAAM;IACJM,OAAO,EAAEC;EACX,CAAC,GAAGT,KAAK,CAACU,MAAM,CAACP,UAAU,KAAKQ,SAAS,CAAC;EAC1C,MAAM,CAACC,UAAU,EAAEC,QAAQ,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAACT,WAAW,CAAC;EAC1D,MAAMU,KAAK,GAAGN,YAAY,GAAGN,UAAU,GAAGS,UAAU;EACpD,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzClB,KAAK,CAACmB,SAAS,CAAC,MAAM;MACpB,IAAIV,YAAY,MAAMN,UAAU,KAAKQ,SAAS,CAAC,EAAE;QAC/CS,OAAO,CAACC,KAAK,CAAC,qCAAAC,MAAA,CAAqCb,YAAY,GAAG,EAAE,GAAG,IAAI,iBAAAa,MAAA,CAAcf,KAAK,gBAAAe,MAAA,CAAahB,IAAI,aAAAgB,MAAA,CAAUb,YAAY,GAAG,IAAI,GAAG,EAAE,kBAAe,6EAA6E,EAAE,qDAAAa,MAAA,CAAqDhB,IAAI,SAAM,4CAA4C,EAAE,4HAA4H,EAAE,sDAAsD,CAAC,CAACiB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/hB;IACF,CAAC,EAAE,CAAChB,KAAK,EAAED,IAAI,EAAEH,UAAU,CAAC,CAAC;IAC7B,MAAM;MACJK,OAAO,EAAEgB;IACX,CAAC,GAAGxB,KAAK,CAACU,MAAM,CAACL,WAAW,CAAC;IAC7BL,KAAK,CAACmB,SAAS,CAAC,MAAM;MACpB,IAAI,CAACV,YAAY,IAAI,CAACgB,MAAM,CAACC,EAAE,CAACF,YAAY,EAAEnB,WAAW,CAAC,EAAE;QAC1De,OAAO,CAACC,KAAK,CAAC,CAAC,4CAAAC,MAAA,CAA4Cf,KAAK,gCAAAe,MAAA,CAA6BhB,IAAI,sFAAAgB,MAAA,CAAmFhB,IAAI,MAAG,CAAC,CAACiB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1M;IACF,CAAC,EAAE,CAACI,IAAI,CAACC,SAAS,CAACvB,WAAW,CAAC,CAAC,CAAC;EACnC;EACA,MAAMwB,sBAAsB,GAAG7B,KAAK,CAAC8B,WAAW,CAACC,QAAQ,IAAI;IAC3D,IAAI,CAACtB,YAAY,EAAE;MACjBI,QAAQ,CAACkB,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAAChB,KAAK,EAAEc,sBAAsB,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}