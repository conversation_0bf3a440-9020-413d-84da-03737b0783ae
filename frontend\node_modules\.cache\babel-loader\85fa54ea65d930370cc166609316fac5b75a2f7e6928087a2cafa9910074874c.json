{"ast": null, "code": "import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst RadioGroupContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  RadioGroupContext.displayName = 'RadioGroupContext';\n}\nexport default RadioGroupContext;", "map": {"version": 3, "names": ["React", "RadioGroupContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/RadioGroup/RadioGroupContext.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst RadioGroupContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  RadioGroupContext.displayName = 'RadioGroupContext';\n}\nexport default RadioGroupContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACrE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,iBAAiB,CAACM,WAAW,GAAG,mBAAmB;AACrD;AACA,eAAeN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}