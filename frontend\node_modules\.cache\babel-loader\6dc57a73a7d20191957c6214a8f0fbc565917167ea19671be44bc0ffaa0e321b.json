{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCircularProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiCircularProgress', slot);\n}\nconst circularProgressClasses = generateUtilityClasses('MuiCircularProgress', ['root', 'determinate', 'indeterminate', 'colorPrimary', 'colorSecondary', 'svg', 'circle', 'circleDeterminate', 'circleIndeterminate', 'circleDisableShrink']);\nexport default circularProgressClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getCircularProgressUtilityClass", "slot", "circularProgressClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/CircularProgress/circularProgressClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCircularProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiCircularProgress', slot);\n}\nconst circularProgressClasses = generateUtilityClasses('MuiCircularProgress', ['root', 'determinate', 'indeterminate', 'colorPrimary', 'colorSecondary', 'svg', 'circle', 'circleDeterminate', 'circleIndeterminate', 'circleDisableShrink']);\nexport default circularProgressClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOF,oBAAoB,CAAC,qBAAqB,EAAEE,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGJ,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,qBAAqB,CAAC,CAAC;AAC7O,eAAeI,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}