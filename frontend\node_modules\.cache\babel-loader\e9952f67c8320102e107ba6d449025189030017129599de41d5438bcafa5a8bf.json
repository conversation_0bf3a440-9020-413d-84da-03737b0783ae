{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaginationUtilityClass(slot) {\n  return generateUtilityClass('MuiPagination', slot);\n}\nconst paginationClasses = generateUtilityClasses('MuiPagination', ['root', 'ul', 'outlined', 'text']);\nexport default paginationClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getPaginationUtilityClass", "slot", "paginationClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Pagination/paginationClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaginationUtilityClass(slot) {\n  return generateUtilityClass('MuiPagination', slot);\n}\nconst paginationClasses = generateUtilityClasses('MuiPagination', ['root', 'ul', 'outlined', 'text']);\nexport default paginationClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOF,oBAAoB,CAAC,eAAe,EAAEE,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AACrG,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}