{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Minimize2 = createLucideIcon(\"Minimize2\", [[\"polyline\", {\n  points: \"4 14 10 14 10 20\",\n  key: \"11kfnr\"\n}], [\"polyline\", {\n  points: \"20 10 14 10 14 4\",\n  key: \"rlmsce\"\n}], [\"line\", {\n  x1: \"14\",\n  x2: \"21\",\n  y1: \"10\",\n  y2: \"3\",\n  key: \"o5lafz\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"10\",\n  y1: \"21\",\n  y2: \"14\",\n  key: \"1atl0r\"\n}]]);\nexport { Minimize2 as default };", "map": {"version": 3, "names": ["Minimize2", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\minimize-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Minimize2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI0IDE0IDEwIDE0IDEwIDIwIiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjIwIDEwIDE0IDEwIDE0IDQiIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMjEiIHkxPSIxMCIgeTI9IjMiIC8+CiAgPGxpbmUgeDE9IjMiIHgyPSIxMCIgeTE9IjIxIiB5Mj0iMTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/minimize-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Minimize2 = createLucideIcon('Minimize2', [\n  ['polyline', { points: '4 14 10 14 10 20', key: '11kfnr' }],\n  ['polyline', { points: '20 10 14 10 14 4', key: 'rlmsce' }],\n  ['line', { x1: '14', x2: '21', y1: '10', y2: '3', key: 'o5lafz' }],\n  ['line', { x1: '3', x2: '10', y1: '21', y2: '14', key: '1atl0r' }],\n]);\n\nexport default Minimize2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,UAAY;EAAEC,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,UAAY;EAAED,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}