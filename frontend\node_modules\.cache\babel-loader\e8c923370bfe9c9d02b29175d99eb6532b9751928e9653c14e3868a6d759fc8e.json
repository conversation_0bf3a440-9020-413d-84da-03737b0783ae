{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\AtlasInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { createChart } from 'lightweight-charts';\nimport { Send, TrendingUp, BarChart3, Sparkles } from 'lucide-react';\nimport SpaceBackground from './SpaceBackground';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AtlasInterface = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: 'system',\n    content: \"Certainly! Here is the latest stock quote for AAPL.\",\n    timestamp: new Date()\n  }, {\n    id: 2,\n    type: 'stock-quote',\n    symbol: 'AAPL',\n    price: 149.36,\n    change: 1.32,\n    changePercent: 0.89,\n    company: 'Apple Inc.',\n    chartData: generateMockChartData(),\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const chartRef = useRef(null);\n  const chartInstanceRef = useRef(null);\n\n  // Initialize chart when stock quote message is rendered\n  useEffect(() => {\n    const stockMessage = messages.find(msg => msg.type === 'stock-quote');\n    if (stockMessage && chartRef.current && !chartInstanceRef.current) {\n      initializeChart(stockMessage.chartData);\n    }\n  }, [messages]);\n  const initializeChart = data => {\n    if (chartInstanceRef.current) {\n      chartInstanceRef.current.remove();\n    }\n    const chart = createChart(chartRef.current, {\n      width: 280,\n      height: 120,\n      layout: {\n        background: {\n          color: 'transparent'\n        },\n        textColor: '#67e8f9'\n      },\n      grid: {\n        vertLines: {\n          color: 'rgba(6, 182, 212, 0.1)'\n        },\n        horzLines: {\n          color: 'rgba(6, 182, 212, 0.1)'\n        }\n      },\n      crosshair: {\n        mode: 0\n      },\n      rightPriceScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9'\n      },\n      timeScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9',\n        timeVisible: false,\n        secondsVisible: false\n      }\n    });\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#22d3ee',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#22d3ee',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#22d3ee'\n    });\n    candlestickSeries.setData(data);\n    chartInstanceRef.current = chart;\n\n    // Auto-fit content\n    chart.timeScale().fitContent();\n  };\n  const handleSendMessage = () => {\n    if (!inputMessage.trim()) return;\n    const newMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, newMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI response\n    setTimeout(() => {\n      setIsTyping(false);\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: \"I'm analyzing that for you. Let me pull up the latest data...\",\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, aiResponse]);\n    }, 1500);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen space-bg relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(SpaceBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex items-center justify-center min-h-screen p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-8 h-8 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-2\",\n            children: \"H.O.L.L.Y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-cyan-300/80 text-sm\",\n            children: \"Stock Analysis Chatbot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.4\n          },\n          className: \"glass-card p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 max-h-96 overflow-y-auto\",\n            children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -10\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: [message.type === 'system' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-cyan-100 text-sm leading-relaxed\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 23\n                }, this), message.type === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm\",\n                    children: message.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 23\n                }, this), message.type === 'stock-quote' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-2xl p-4 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: message.symbol\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-cyan-300/80 text-sm\",\n                        children: message.company\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: message.price\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 196,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center text-sm text-green-400\",\n                        children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                          className: \"w-4 h-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 200,\n                          columnNumber: 31\n                        }, this), \"+\", message.change, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 199,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-black/20 rounded-xl p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      ref: chartRef,\n                      className: \"w-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\",\n                      children: \"Show earnings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\",\n                      children: \"Analyze trend\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 23\n                }, this)]\n              }, message.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -10\n                },\n                className: \"flex items-center space-x-2 text-cyan-400\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.1s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"H.O.L.L.Y is typing...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: inputMessage,\n              onChange: e => setInputMessage(e.target.value),\n              onKeyPress: handleKeyPress,\n              placeholder: \"Send a message...\",\n              className: \"w-full bg-black/20 border border-cyan-500/30 rounded-2xl px-4 py-3 pr-12 text-white placeholder-cyan-300/50 focus:outline-none focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSendMessage,\n              className: \"absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center hover:scale-105 transition-transform duration-200 btn-glow\",\n              children: /*#__PURE__*/_jsxDEV(Send, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n\n// Generate mock chart data\n_s(AtlasInterface, \"yppJNJt70J5qrbHD3s+ZVL/gCJU=\");\n_c = AtlasInterface;\nfunction generateMockChartData() {\n  const data = [];\n  let basePrice = 148;\n  for (let i = 0; i < 50; i++) {\n    const time = Math.floor(Date.now() / 1000) - (50 - i) * 3600;\n    const volatility = 0.02;\n    const change = (Math.random() - 0.5) * volatility * basePrice;\n    const open = basePrice;\n    const close = basePrice + change;\n    const high = Math.max(open, close) + Math.random() * 0.5;\n    const low = Math.min(open, close) - Math.random() * 0.5;\n    data.push({\n      time,\n      open: parseFloat(open.toFixed(2)),\n      high: parseFloat(high.toFixed(2)),\n      low: parseFloat(low.toFixed(2)),\n      close: parseFloat(close.toFixed(2))\n    });\n    basePrice = close;\n  }\n  return data;\n}\nexport default AtlasInterface;\nvar _c;\n$RefreshReg$(_c, \"AtlasInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "createChart", "Send", "TrendingUp", "BarChart3", "<PERSON><PERSON><PERSON>", "SpaceBackground", "jsxDEV", "_jsxDEV", "AtlasInterface", "_s", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "symbol", "price", "change", "changePercent", "company", "chartData", "generateMockChartData", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "chartRef", "chartInstanceRef", "stockMessage", "find", "msg", "current", "initializeChart", "data", "remove", "chart", "width", "height", "layout", "background", "color", "textColor", "grid", "vertLines", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "candlestickSeries", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "setData", "<PERSON><PERSON><PERSON><PERSON>", "handleSendMessage", "trim", "newMessage", "now", "prev", "setTimeout", "aiResponse", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "scale", "map", "message", "exit", "ref", "style", "animationDelay", "value", "onChange", "target", "onKeyPress", "placeholder", "onClick", "_c", "basePrice", "i", "time", "Math", "floor", "volatility", "random", "open", "close", "high", "max", "low", "min", "push", "parseFloat", "toFixed", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AtlasInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { createChart } from 'lightweight-charts';\nimport { Send, TrendingUp, BarChart3, Sparkles } from 'lucide-react';\nimport SpaceBackground from './SpaceBackground';\n\nconst AtlasInterface = () => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: 'system',\n      content: \"Certainly! Here is the latest stock quote for AAPL.\",\n      timestamp: new Date()\n    },\n    {\n      id: 2,\n      type: 'stock-quote',\n      symbol: 'AAPL',\n      price: 149.36,\n      change: 1.32,\n      changePercent: 0.89,\n      company: 'Apple Inc.',\n      chartData: generateMockChartData(),\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const chartRef = useRef(null);\n  const chartInstanceRef = useRef(null);\n\n  // Initialize chart when stock quote message is rendered\n  useEffect(() => {\n    const stockMessage = messages.find(msg => msg.type === 'stock-quote');\n    if (stockMessage && chartRef.current && !chartInstanceRef.current) {\n      initializeChart(stockMessage.chartData);\n    }\n  }, [messages]);\n\n  const initializeChart = (data) => {\n    if (chartInstanceRef.current) {\n      chartInstanceRef.current.remove();\n    }\n\n    const chart = createChart(chartRef.current, {\n      width: 280,\n      height: 120,\n      layout: {\n        background: { color: 'transparent' },\n        textColor: '#67e8f9',\n      },\n      grid: {\n        vertLines: { color: 'rgba(6, 182, 212, 0.1)' },\n        horzLines: { color: 'rgba(6, 182, 212, 0.1)' },\n      },\n      crosshair: {\n        mode: 0,\n      },\n      rightPriceScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9',\n      },\n      timeScale: {\n        borderColor: 'rgba(6, 182, 212, 0.3)',\n        textColor: '#67e8f9',\n        timeVisible: false,\n        secondsVisible: false,\n      },\n    });\n\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#22d3ee',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#22d3ee',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#22d3ee',\n    });\n\n    candlestickSeries.setData(data);\n    chartInstanceRef.current = chart;\n\n    // Auto-fit content\n    chart.timeScale().fitContent();\n  };\n\n  const handleSendMessage = () => {\n    if (!inputMessage.trim()) return;\n\n    const newMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI response\n    setTimeout(() => {\n      setIsTyping(false);\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: \"I'm analyzing that for you. Let me pull up the latest data...\",\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, aiResponse]);\n    }, 1500);\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen space-bg relative overflow-hidden\">\n      <SpaceBackground />\n      \n      {/* Main Container */}\n      <div className=\"relative z-10 flex items-center justify-center min-h-screen p-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"w-full max-w-md\"\n        >\n          {/* Header */}\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"text-center mb-8\"\n          >\n            <div className=\"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 mb-4\">\n              <Sparkles className=\"w-8 h-8 text-cyan-400\" />\n            </div>\n            <h1 className=\"text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-2\">\n              H.O.L.L.Y\n            </h1>\n            <p className=\"text-cyan-300/80 text-sm\">\n              Stock Analysis Chatbot\n            </p>\n          </motion.div>\n\n          {/* Chat Container */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"glass-card p-6 space-y-6\"\n          >\n            {/* Messages */}\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              <AnimatePresence>\n                {messages.map((message) => (\n                  <motion.div\n                    key={message.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    {message.type === 'system' && (\n                      <div className=\"text-cyan-100 text-sm leading-relaxed\">\n                        {message.content}\n                      </div>\n                    )}\n\n                    {message.type === 'user' && (\n                      <div className=\"text-right\">\n                        <div className=\"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm\">\n                          {message.content}\n                        </div>\n                      </div>\n                    )}\n\n                    {message.type === 'stock-quote' && (\n                      <div className=\"bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-2xl p-4 space-y-4\">\n                        {/* Stock Header */}\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"text-2xl font-bold text-white\">\n                              {message.symbol}\n                            </div>\n                            <div className=\"text-cyan-300/80 text-sm\">\n                              {message.company}\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-2xl font-bold text-white\">\n                              {message.price}\n                            </div>\n                            <div className=\"flex items-center text-sm text-green-400\">\n                              <TrendingUp className=\"w-4 h-4 mr-1\" />\n                              +{message.change}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Chart */}\n                        <div className=\"bg-black/20 rounded-xl p-3\">\n                          <div ref={chartRef} className=\"w-full\" />\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex gap-2\">\n                          <button className=\"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\">\n                            Show earnings\n                          </button>\n                          <button className=\"flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow\">\n                            Analyze trend\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n\n              {/* Typing Indicator */}\n              <AnimatePresence>\n                {isTyping && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"flex items-center space-x-2 text-cyan-400\"\n                  >\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                    </div>\n                    <span className=\"text-sm\">H.O.L.L.Y is typing...</span>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Input Area */}\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={inputMessage}\n                onChange={(e) => setInputMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Send a message...\"\n                className=\"w-full bg-black/20 border border-cyan-500/30 rounded-2xl px-4 py-3 pr-12 text-white placeholder-cyan-300/50 focus:outline-none focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-200\"\n              />\n              <button\n                onClick={handleSendMessage}\n                className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center hover:scale-105 transition-transform duration-200 btn-glow\"\n              >\n                <Send className=\"w-4 h-4 text-white\" />\n              </button>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\n// Generate mock chart data\nfunction generateMockChartData() {\n  const data = [];\n  let basePrice = 148;\n  \n  for (let i = 0; i < 50; i++) {\n    const time = Math.floor(Date.now() / 1000) - (50 - i) * 3600;\n    const volatility = 0.02;\n    const change = (Math.random() - 0.5) * volatility * basePrice;\n    \n    const open = basePrice;\n    const close = basePrice + change;\n    const high = Math.max(open, close) + Math.random() * 0.5;\n    const low = Math.min(open, close) - Math.random() * 0.5;\n    \n    data.push({\n      time,\n      open: parseFloat(open.toFixed(2)),\n      high: parseFloat(high.toFixed(2)),\n      low: parseFloat(low.toFixed(2)),\n      close: parseFloat(close.toFixed(2)),\n    });\n    \n    basePrice = close;\n  }\n  \n  return data;\n}\n\nexport default AtlasInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,cAAc;AACpE,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CACvC;IACEiB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,qDAAqD;IAC9DC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBI,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAEC,qBAAqB,CAAC,CAAC;IAClCR,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMiC,QAAQ,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMgC,gBAAgB,GAAGhC,MAAM,CAAC,IAAI,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkC,YAAY,GAAGpB,QAAQ,CAACqB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnB,IAAI,KAAK,aAAa,CAAC;IACrE,IAAIiB,YAAY,IAAIF,QAAQ,CAACK,OAAO,IAAI,CAACJ,gBAAgB,CAACI,OAAO,EAAE;MACjEC,eAAe,CAACJ,YAAY,CAACR,SAAS,CAAC;IACzC;EACF,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMwB,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIN,gBAAgB,CAACI,OAAO,EAAE;MAC5BJ,gBAAgB,CAACI,OAAO,CAACG,MAAM,CAAC,CAAC;IACnC;IAEA,MAAMC,KAAK,GAAGrC,WAAW,CAAC4B,QAAQ,CAACK,OAAO,EAAE;MAC1CK,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE;QACNC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAc,CAAC;QACpCC,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJC,SAAS,EAAE;UAAEH,KAAK,EAAE;QAAyB,CAAC;QAC9CI,SAAS,EAAE;UAAEJ,KAAK,EAAE;QAAyB;MAC/C,CAAC;MACDK,SAAS,EAAE;QACTC,IAAI,EAAE;MACR,CAAC;MACDC,eAAe,EAAE;QACfC,WAAW,EAAE,wBAAwB;QACrCP,SAAS,EAAE;MACb,CAAC;MACDQ,SAAS,EAAE;QACTD,WAAW,EAAE,wBAAwB;QACrCP,SAAS,EAAE,SAAS;QACpBS,WAAW,EAAE,KAAK;QAClBC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,MAAMC,iBAAiB,GAAGjB,KAAK,CAACkB,oBAAoB,CAAC;MACnDC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,eAAe,EAAE,SAAS;MAC1BC,aAAa,EAAE,SAAS;MACxBC,aAAa,EAAE,SAAS;MACxBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFP,iBAAiB,CAACQ,OAAO,CAAC3B,IAAI,CAAC;IAC/BN,gBAAgB,CAACI,OAAO,GAAGI,KAAK;;IAEhC;IACAA,KAAK,CAACc,SAAS,CAAC,CAAC,CAACY,UAAU,CAAC,CAAC;EAChC,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACxC,YAAY,CAACyC,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAMC,UAAU,GAAG;MACjBtD,EAAE,EAAEI,IAAI,CAACmD,GAAG,CAAC,CAAC;MACdtD,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEU,YAAY;MACrBT,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAACyD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,UAAU,CAAC,CAAC;IAC1CzC,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACA0C,UAAU,CAAC,MAAM;MACf1C,WAAW,CAAC,KAAK,CAAC;MAClB,MAAM2C,UAAU,GAAG;QACjB1D,EAAE,EAAEI,IAAI,CAACmD,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBtD,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,+DAA+D;QACxEC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDL,WAAW,CAACyD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,UAAU,CAAC,CAAC;IAC5C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBX,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACEzD,OAAA;IAAKqE,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAC7DtE,OAAA,CAACF,eAAe;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnB1E,OAAA;MAAKqE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EtE,OAAA,CAACT,MAAM,CAACoF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BZ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAG3BtE,OAAA,CAACT,MAAM,CAACoF,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cb,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAE5BtE,OAAA;YAAKqE,SAAS,EAAC,gJAAgJ;YAAAC,QAAA,eAC7JtE,OAAA,CAACH,QAAQ;cAACwE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACN1E,OAAA;YAAIqE,SAAS,EAAC,4GAA4G;YAAAC,QAAA,EAAC;UAE3H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1E,OAAA;YAAGqE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGb1E,OAAA,CAACT,MAAM,CAACoF,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,KAAK,EAAE;UAAK,CAAE;UACrCJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,KAAK,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cb,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBAGpCtE,OAAA;YAAKqE,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjDtE,OAAA,CAACR,eAAe;cAAA8E,QAAA,EACbnE,QAAQ,CAACiF,GAAG,CAAEC,OAAO,iBACpBrF,OAAA,CAACT,MAAM,CAACoF,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BQ,IAAI,EAAE;kBAAET,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BE,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAX,QAAA,GAE7Be,OAAO,CAAC/E,IAAI,KAAK,QAAQ,iBACxBN,OAAA;kBAAKqE,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EACnDe,OAAO,CAAC9E;gBAAO;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACN,EAEAW,OAAO,CAAC/E,IAAI,KAAK,MAAM,iBACtBN,OAAA;kBAAKqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBtE,OAAA;oBAAKqE,SAAS,EAAC,kGAAkG;oBAAAC,QAAA,EAC9Ge,OAAO,CAAC9E;kBAAO;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEAW,OAAO,CAAC/E,IAAI,KAAK,aAAa,iBAC7BN,OAAA;kBAAKqE,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,gBAEpHtE,OAAA;oBAAKqE,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDtE,OAAA;sBAAAsE,QAAA,gBACEtE,OAAA;wBAAKqE,SAAS,EAAC,+BAA+B;wBAAAC,QAAA,EAC3Ce,OAAO,CAAC3E;sBAAM;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACN1E,OAAA;wBAAKqE,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EACtCe,OAAO,CAACvE;sBAAO;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN1E,OAAA;sBAAKqE,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBtE,OAAA;wBAAKqE,SAAS,EAAC,+BAA+B;wBAAAC,QAAA,EAC3Ce,OAAO,CAAC1E;sBAAK;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACN1E,OAAA;wBAAKqE,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,gBACvDtE,OAAA,CAACL,UAAU;0BAAC0E,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,KACtC,EAACW,OAAO,CAACzE,MAAM,EAAC,GACnB;sBAAA;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN1E,OAAA;oBAAKqE,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,eACzCtE,OAAA;sBAAKuF,GAAG,EAAElE,QAAS;sBAACgD,SAAS,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eAGN1E,OAAA;oBAAKqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBtE,OAAA;sBAAQqE,SAAS,EAAC,sJAAsJ;sBAAAC,QAAA,EAAC;oBAEzK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT1E,OAAA;sBAAQqE,SAAS,EAAC,sJAAsJ;sBAAAC,QAAA,EAAC;oBAEzK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GA1DIW,OAAO,CAAChF,EAAE;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2DL,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC,eAGlB1E,OAAA,CAACR,eAAe;cAAA8E,QAAA,EACbnD,QAAQ,iBACPnB,OAAA,CAACT,MAAM,CAACoF,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BQ,IAAI,EAAE;kBAAET,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BT,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBAErDtE,OAAA;kBAAKqE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BtE,OAAA;oBAAKqE,SAAS,EAAC;kBAAiD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnE1E,OAAA;oBAAKqE,SAAS,EAAC,iDAAiD;oBAACmB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtG1E,OAAA;oBAAKqE,SAAS,EAAC,iDAAiD;oBAACmB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACN1E,OAAA;kBAAMqE,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGN1E,OAAA;YAAKqE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBtE,OAAA;cACEM,IAAI,EAAC,MAAM;cACXoF,KAAK,EAAEzE,YAAa;cACpB0E,QAAQ,EAAG1B,CAAC,IAAK/C,eAAe,CAAC+C,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;cACjDG,UAAU,EAAE7B,cAAe;cAC3B8B,WAAW,EAAC,mBAAmB;cAC/BzB,SAAS,EAAC;YAAsN;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjO,CAAC,eACF1E,OAAA;cACE+F,OAAO,EAAEtC,iBAAkB;cAC3BY,SAAS,EAAC,+MAA+M;cAAAC,QAAA,eAEzNtE,OAAA,CAACN,IAAI;gBAAC2E,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAxE,EAAA,CAvQMD,cAAc;AAAA+F,EAAA,GAAd/F,cAAc;AAwQpB,SAASe,qBAAqBA,CAAA,EAAG;EAC/B,MAAMY,IAAI,GAAG,EAAE;EACf,IAAIqE,SAAS,GAAG,GAAG;EAEnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC5F,IAAI,CAACmD,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGsC,CAAC,IAAI,IAAI;IAC5D,MAAMI,UAAU,GAAG,IAAI;IACvB,MAAM1F,MAAM,GAAG,CAACwF,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAID,UAAU,GAAGL,SAAS;IAE7D,MAAMO,IAAI,GAAGP,SAAS;IACtB,MAAMQ,KAAK,GAAGR,SAAS,GAAGrF,MAAM;IAChC,MAAM8F,IAAI,GAAGN,IAAI,CAACO,GAAG,CAACH,IAAI,EAAEC,KAAK,CAAC,GAAGL,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG;IACxD,MAAMK,GAAG,GAAGR,IAAI,CAACS,GAAG,CAACL,IAAI,EAAEC,KAAK,CAAC,GAAGL,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG;IAEvD3E,IAAI,CAACkF,IAAI,CAAC;MACRX,IAAI;MACJK,IAAI,EAAEO,UAAU,CAACP,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;MACjCN,IAAI,EAAEK,UAAU,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;MACjCJ,GAAG,EAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BP,KAAK,EAAEM,UAAU,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;IAEFf,SAAS,GAAGQ,KAAK;EACnB;EAEA,OAAO7E,IAAI;AACb;AAEA,eAAe3B,cAAc;AAAC,IAAA+F,EAAA;AAAAiB,YAAA,CAAAjB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}