{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useVisualElement } from './utils/use-visual-element.mjs';\nimport { useMotionRef } from './utils/use-motion-ref.mjs';\nimport { useCreateMotionContext } from '../context/MotionContext/create.mjs';\nimport { loadFeatures } from './features/load-features.mjs';\nimport { isBrowser } from '../utils/is-browser.mjs';\nimport { LayoutGroupContext } from '../context/LayoutGroupContext.mjs';\nimport { LazyContext } from '../context/LazyContext.mjs';\nimport { SwitchLayoutGroupContext } from '../context/SwitchLayoutGroupContext.mjs';\nimport { motionComponentSymbol } from './utils/symbol.mjs';\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createMotionComponent(_ref) {\n  let {\n    preloadedFeatures,\n    createVisualElement,\n    useRender,\n    useVisualState,\n    Component\n  } = _ref;\n  preloadedFeatures && loadFeatures(preloadedFeatures);\n  function MotionComponent(props, externalRef) {\n    /**\n     * If we need to measure the element we load this functionality in a\n     * separate class component in order to gain access to getSnapshotBeforeUpdate.\n     */\n    let MeasureLayout;\n    const configAndProps = _objectSpread(_objectSpread(_objectSpread({}, useContext(MotionConfigContext)), props), {}, {\n      layoutId: useLayoutId(props)\n    });\n    const {\n      isStatic\n    } = configAndProps;\n    const context = useCreateMotionContext(props);\n    const visualState = useVisualState(props, isStatic);\n    if (!isStatic && isBrowser) {\n      /**\n       * Create a VisualElement for this component. A VisualElement provides a common\n       * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n       * providing a way of rendering to these APIs outside of the React render loop\n       * for more performant animations and interactions\n       */\n      context.visualElement = useVisualElement(Component, visualState, configAndProps, createVisualElement);\n      /**\n       * Load Motion gesture and animation features. These are rendered as renderless\n       * components so each feature can optionally make use of React lifecycle methods.\n       */\n      const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n      const isStrict = useContext(LazyContext).strict;\n      if (context.visualElement) {\n        MeasureLayout = context.visualElement.loadFeatures(\n        // Note: Pass the full new combined props to correctly re-render dynamic feature components.\n        configAndProps, isStrict, preloadedFeatures, initialLayoutGroupConfig);\n      }\n    }\n    /**\n     * The mount order and hierarchy is specific to ensure our element ref\n     * is hydrated by the time features fire their effects.\n     */\n    return React.createElement(MotionContext.Provider, {\n      value: context\n    }, MeasureLayout && context.visualElement ? React.createElement(MeasureLayout, _objectSpread({\n      visualElement: context.visualElement\n    }, configAndProps)) : null, useRender(Component, props, useMotionRef(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement));\n  }\n  const ForwardRefComponent = forwardRef(MotionComponent);\n  ForwardRefComponent[motionComponentSymbol] = Component;\n  return ForwardRefComponent;\n}\nfunction useLayoutId(_ref2) {\n  let {\n    layoutId\n  } = _ref2;\n  const layoutGroupId = useContext(LayoutGroupContext).id;\n  return layoutGroupId && layoutId !== undefined ? layoutGroupId + \"-\" + layoutId : layoutId;\n}\nexport { createMotionComponent };", "map": {"version": 3, "names": ["React", "forwardRef", "useContext", "MotionConfigContext", "MotionContext", "useVisualElement", "useMotionRef", "useCreateMotionContext", "loadFeatures", "<PERSON><PERSON><PERSON><PERSON>", "LayoutGroupContext", "LazyContext", "SwitchLayoutGroupContext", "motionComponentSymbol", "createMotionComponent", "_ref", "preloadedFeatures", "createVisualElement", "useRender", "useVisualState", "Component", "MotionComponent", "props", "externalRef", "MeasureLayout", "configAndProps", "_objectSpread", "layoutId", "useLayoutId", "isStatic", "context", "visualState", "visualElement", "initialLayoutGroupConfig", "isStrict", "strict", "createElement", "Provider", "value", "ForwardRefComponent", "_ref2", "layoutGroupId", "id", "undefined"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/motion/index.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useVisualElement } from './utils/use-visual-element.mjs';\nimport { useMotionRef } from './utils/use-motion-ref.mjs';\nimport { useCreateMotionContext } from '../context/MotionContext/create.mjs';\nimport { loadFeatures } from './features/load-features.mjs';\nimport { isBrowser } from '../utils/is-browser.mjs';\nimport { LayoutGroupContext } from '../context/LayoutGroupContext.mjs';\nimport { LazyContext } from '../context/LazyContext.mjs';\nimport { SwitchLayoutGroupContext } from '../context/SwitchLayoutGroupContext.mjs';\nimport { motionComponentSymbol } from './utils/symbol.mjs';\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createMotionComponent({ preloadedFeatures, createVisualElement, useRender, useVisualState, Component, }) {\n    preloadedFeatures && loadFeatures(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */\n        let MeasureLayout;\n        const configAndProps = {\n            ...useContext(MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props),\n        };\n        const { isStatic } = configAndProps;\n        const context = useCreateMotionContext(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && isBrowser) {\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */\n            context.visualElement = useVisualElement(Component, visualState, configAndProps, createVisualElement);\n            /**\n             * Load Motion gesture and animation features. These are rendered as renderless\n             * components so each feature can optionally make use of React lifecycle methods.\n             */\n            const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n            const isStrict = useContext(LazyContext).strict;\n            if (context.visualElement) {\n                MeasureLayout = context.visualElement.loadFeatures(\n                // Note: Pass the full new combined props to correctly re-render dynamic feature components.\n                configAndProps, isStrict, preloadedFeatures, initialLayoutGroupConfig);\n            }\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */\n        return (React.createElement(MotionContext.Provider, { value: context },\n            MeasureLayout && context.visualElement ? (React.createElement(MeasureLayout, { visualElement: context.visualElement, ...configAndProps })) : null,\n            useRender(Component, props, useMotionRef(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)));\n    }\n    const ForwardRefComponent = forwardRef(MotionComponent);\n    ForwardRefComponent[motionComponentSymbol] = Component;\n    return ForwardRefComponent;\n}\nfunction useLayoutId({ layoutId }) {\n    const layoutGroupId = useContext(LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined\n        ? layoutGroupId + \"-\" + layoutId\n        : layoutId;\n}\n\nexport { createMotionComponent };\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,oBAAoB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAAAC,IAAA,EAAoF;EAAA,IAAnF;IAAEC,iBAAiB;IAAEC,mBAAmB;IAAEC,SAAS;IAAEC,cAAc;IAAEC;EAAW,CAAC,GAAAL,IAAA;EAC5GC,iBAAiB,IAAIR,YAAY,CAACQ,iBAAiB,CAAC;EACpD,SAASK,eAAeA,CAACC,KAAK,EAAEC,WAAW,EAAE;IACzC;AACR;AACA;AACA;IACQ,IAAIC,aAAa;IACjB,MAAMC,cAAc,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACbxB,UAAU,CAACC,mBAAmB,CAAC,GAC/BmB,KAAK;MACRK,QAAQ,EAAEC,WAAW,CAACN,KAAK;IAAC,EAC/B;IACD,MAAM;MAAEO;IAAS,CAAC,GAAGJ,cAAc;IACnC,MAAMK,OAAO,GAAGvB,sBAAsB,CAACe,KAAK,CAAC;IAC7C,MAAMS,WAAW,GAAGZ,cAAc,CAACG,KAAK,EAAEO,QAAQ,CAAC;IACnD,IAAI,CAACA,QAAQ,IAAIpB,SAAS,EAAE;MACxB;AACZ;AACA;AACA;AACA;AACA;MACYqB,OAAO,CAACE,aAAa,GAAG3B,gBAAgB,CAACe,SAAS,EAAEW,WAAW,EAAEN,cAAc,EAAER,mBAAmB,CAAC;MACrG;AACZ;AACA;AACA;MACY,MAAMgB,wBAAwB,GAAG/B,UAAU,CAACU,wBAAwB,CAAC;MACrE,MAAMsB,QAAQ,GAAGhC,UAAU,CAACS,WAAW,CAAC,CAACwB,MAAM;MAC/C,IAAIL,OAAO,CAACE,aAAa,EAAE;QACvBR,aAAa,GAAGM,OAAO,CAACE,aAAa,CAACxB,YAAY;QAClD;QACAiB,cAAc,EAAES,QAAQ,EAAElB,iBAAiB,EAAEiB,wBAAwB,CAAC;MAC1E;IACJ;IACA;AACR;AACA;AACA;IACQ,OAAQjC,KAAK,CAACoC,aAAa,CAAChC,aAAa,CAACiC,QAAQ,EAAE;MAAEC,KAAK,EAAER;IAAQ,CAAC,EAClEN,aAAa,IAAIM,OAAO,CAACE,aAAa,GAAIhC,KAAK,CAACoC,aAAa,CAACZ,aAAa,EAAAE,aAAA;MAAIM,aAAa,EAAEF,OAAO,CAACE;IAAa,GAAKP,cAAc,CAAE,CAAC,GAAI,IAAI,EACjJP,SAAS,CAACE,SAAS,EAAEE,KAAK,EAAEhB,YAAY,CAACyB,WAAW,EAAED,OAAO,CAACE,aAAa,EAAET,WAAW,CAAC,EAAEQ,WAAW,EAAEF,QAAQ,EAAEC,OAAO,CAACE,aAAa,CAAC,CAAC;EACjJ;EACA,MAAMO,mBAAmB,GAAGtC,UAAU,CAACoB,eAAe,CAAC;EACvDkB,mBAAmB,CAAC1B,qBAAqB,CAAC,GAAGO,SAAS;EACtD,OAAOmB,mBAAmB;AAC9B;AACA,SAASX,WAAWA,CAAAY,KAAA,EAAe;EAAA,IAAd;IAAEb;EAAS,CAAC,GAAAa,KAAA;EAC7B,MAAMC,aAAa,GAAGvC,UAAU,CAACQ,kBAAkB,CAAC,CAACgC,EAAE;EACvD,OAAOD,aAAa,IAAId,QAAQ,KAAKgB,SAAS,GACxCF,aAAa,GAAG,GAAG,GAAGd,QAAQ,GAC9BA,QAAQ;AAClB;AAEA,SAASb,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}