#!/usr/bin/env python3
"""
Test the enhanced A.T.L.A.S Trading God format
"""

import requests
import json
import time

def test_trading_god_format():
    """Test the new mandatory Trading God response format."""
    
    print("🧪 Testing Enhanced A.T.L.A.S Trading God Format")
    print("=" * 60)
    
    # Test prompts that should trigger the Trading God format
    test_prompts = [
        "What's Microsoft trading at right now?",
        "Should I buy Amazon stock today?",
        "Give me a quick analysis of Google stock"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n📊 Test {i}: {prompt}")
        print("-" * 50)
        
        try:
            # Send request to A.T.L.A.S
            response = requests.post(
                'http://localhost:8080/api/v1/chat',
                json={
                    'message': prompt,
                    'session_id': f'trading_god_test_{i}'
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', 'No response')
                
                print("✅ SUCCESS - Response received:")
                print(response_text)
                
                # Check for mandatory format components
                format_check = check_trading_god_format(response_text)
                print(f"\n📋 Format Check: {format_check}")
                
            else:
                print(f"❌ HTTP {response.status_code}: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("\n" + "="*60)
        time.sleep(2)  # Brief pause between tests

def check_trading_god_format(response_text):
    """Check if response follows the mandatory Trading God format."""
    checks = {
        "A.T.L.A.S branding": "A.T.L.A.S powered by Predicto" in response_text,
        "Trade header": any(x in response_text for x in ["ACTION:", "CONFIDENCE:", "Entry:", "Target:", "Stop:"]),
        "Reasoning bullets": "• Reason:" in response_text,
        "Strategy section": "• Strategy:" in response_text,
        "Performance metrics": "• Performance:" in response_text and "• Risk:" in response_text,
        "No AI disclaimers": not any(x in response_text.lower() for x in ["i can't", "i cannot", "i'm not able", "as an ai"])
    }
    
    passed = sum(checks.values())
    total = len(checks)
    
    result = f"{passed}/{total} checks passed"
    
    for check, status in checks.items():
        status_icon = "✅" if status else "❌"
        result += f"\n  {status_icon} {check}"
    
    return result

if __name__ == "__main__":
    test_trading_god_format()
