{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m18.77 8.55-1.17-.47c-.62-.25-1.31.17-1.37.84L15.49 17H17l2.6-6.5c.31-.77-.06-1.65-.83-1.95M6.4 8.08l-1.17.47c-.77.3-1.14 1.18-.83 1.95L7 17h1.5l-.74-8.08c-.06-.67-.74-1.09-1.36-.84M13.36 6h-2.71c-.89 0-1.58.76-1.5 1.64l.85 9.35h4l.85-9.36c.08-.87-.61-1.63-1.49-1.63M3.18 13.72l-1 1.93c-.19.36-.23.78-.12 1.19.29 1.01 1.43 1.41 2.38.94l1.05-.52-1.4-3.49c-.16-.4-.71-.43-.91-.05m18.64 1.93-1-1.93c-.2-.38-.75-.35-.91.04l-1.4 3.49 1.05.52c.94.47 2.09.07 2.38-.94.11-.4.07-.82-.12-1.18\"\n}), '<PERSON>yDining<PERSON>ounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/BakeryDiningRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m18.77 8.55-1.17-.47c-.62-.25-1.31.17-1.37.84L15.49 17H17l2.6-6.5c.31-.77-.06-1.65-.83-1.95M6.4 8.08l-1.17.47c-.77.3-1.14 1.18-.83 1.95L7 17h1.5l-.74-8.08c-.06-.67-.74-1.09-1.36-.84M13.36 6h-2.71c-.89 0-1.58.76-1.5 1.64l.85 9.35h4l.85-9.36c.08-.87-.61-1.63-1.49-1.63M3.18 13.72l-1 1.93c-.19.36-.23.78-.12 1.19.29 1.01 1.43 1.41 2.38.94l1.05-.52-1.4-3.49c-.16-.4-.71-.43-.91-.05m18.64 1.93-1-1.93c-.2-.38-.75-.35-.91.04l-1.4 3.49 1.05.52c.94.47 2.09.07 2.38-.94.11-.4.07-.82-.12-1.18\"\n}), '<PERSON>yDining<PERSON>ounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}