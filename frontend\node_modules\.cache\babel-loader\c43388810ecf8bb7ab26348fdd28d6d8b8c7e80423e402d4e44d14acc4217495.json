{"ast": null, "code": "import { warning } from '../../../utils/errors.mjs';\nimport { clamp } from '../../../utils/clamp.mjs';\nimport { secondsToMilliseconds, millisecondsToSeconds } from '../../../utils/time-conversion.mjs';\nconst safeMin = 0.001;\nconst minDuration = 0.01;\nconst maxDuration = 10.0;\nconst minDamping = 0.05;\nconst maxDamping = 1;\nfunction findSpring(_ref) {\n  let {\n    duration = 800,\n    bounce = 0.25,\n    velocity = 0,\n    mass = 1\n  } = _ref;\n  let envelope;\n  let derivative;\n  warning(duration <= secondsToMilliseconds(maxDuration), \"Spring duration must be 10 seconds or less\");\n  let dampingRatio = 1 - bounce;\n  /**\n   * Restrict dampingRatio and duration to within acceptable ranges.\n   */\n  dampingRatio = clamp(minDamping, maxDamping, dampingRatio);\n  duration = clamp(minDuration, maxDuration, millisecondsToSeconds(duration));\n  if (dampingRatio < 1) {\n    /**\n     * Underdamped spring\n     */\n    envelope = undampedFreq => {\n      const exponentialDecay = undampedFreq * dampingRatio;\n      const delta = exponentialDecay * duration;\n      const a = exponentialDecay - velocity;\n      const b = calcAngularFreq(undampedFreq, dampingRatio);\n      const c = Math.exp(-delta);\n      return safeMin - a / b * c;\n    };\n    derivative = undampedFreq => {\n      const exponentialDecay = undampedFreq * dampingRatio;\n      const delta = exponentialDecay * duration;\n      const d = delta * velocity + velocity;\n      const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n      const f = Math.exp(-delta);\n      const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n      const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n      return factor * ((d - e) * f) / g;\n    };\n  } else {\n    /**\n     * Critically-damped spring\n     */\n    envelope = undampedFreq => {\n      const a = Math.exp(-undampedFreq * duration);\n      const b = (undampedFreq - velocity) * duration + 1;\n      return -safeMin + a * b;\n    };\n    derivative = undampedFreq => {\n      const a = Math.exp(-undampedFreq * duration);\n      const b = (velocity - undampedFreq) * (duration * duration);\n      return a * b;\n    };\n  }\n  const initialGuess = 5 / duration;\n  const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n  duration = secondsToMilliseconds(duration);\n  if (isNaN(undampedFreq)) {\n    return {\n      stiffness: 100,\n      damping: 10,\n      duration\n    };\n  } else {\n    const stiffness = Math.pow(undampedFreq, 2) * mass;\n    return {\n      stiffness,\n      damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n      duration\n    };\n  }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n  let result = initialGuess;\n  for (let i = 1; i < rootIterations; i++) {\n    result = result - envelope(result) / derivative(result);\n  }\n  return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n  return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\nexport { calcAngularFreq, findSpring, maxDamping, maxDuration, minDamping, minDuration };", "map": {"version": 3, "names": ["warning", "clamp", "secondsToMilliseconds", "millisecondsToSeconds", "safeMin", "minDuration", "maxDuration", "minDamping", "maxDamping", "findSpring", "_ref", "duration", "bounce", "velocity", "mass", "envelope", "derivative", "dampingRatio", "undampedFreq", "exponentialDecay", "delta", "a", "b", "calcAngularFreq", "c", "Math", "exp", "d", "e", "pow", "f", "g", "factor", "initialGuess", "approximateRoot", "isNaN", "stiffness", "damping", "sqrt", "rootIterations", "result", "i"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/animation/generators/spring/find.mjs"], "sourcesContent": ["import { warning } from '../../../utils/errors.mjs';\nimport { clamp } from '../../../utils/clamp.mjs';\nimport { secondsToMilliseconds, millisecondsToSeconds } from '../../../utils/time-conversion.mjs';\n\nconst safeMin = 0.001;\nconst minDuration = 0.01;\nconst maxDuration = 10.0;\nconst minDamping = 0.05;\nconst maxDamping = 1;\nfunction findSpring({ duration = 800, bounce = 0.25, velocity = 0, mass = 1, }) {\n    let envelope;\n    let derivative;\n    warning(duration <= secondsToMilliseconds(maxDuration), \"Spring duration must be 10 seconds or less\");\n    let dampingRatio = 1 - bounce;\n    /**\n     * Restrict dampingRatio and duration to within acceptable ranges.\n     */\n    dampingRatio = clamp(minDamping, maxDamping, dampingRatio);\n    duration = clamp(minDuration, maxDuration, millisecondsToSeconds(duration));\n    if (dampingRatio < 1) {\n        /**\n         * Underdamped spring\n         */\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        /**\n         * Critically-damped spring\n         */\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = secondsToMilliseconds(duration);\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: 100,\n            damping: 10,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\nexport { calcAngularFreq, findSpring, maxDamping, maxDuration, minDamping, minDuration };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,oCAAoC;AAEjG,MAAMC,OAAO,GAAG,KAAK;AACrB,MAAMC,WAAW,GAAG,IAAI;AACxB,MAAMC,WAAW,GAAG,IAAI;AACxB,MAAMC,UAAU,GAAG,IAAI;AACvB,MAAMC,UAAU,GAAG,CAAC;AACpB,SAASC,UAAUA,CAAAC,IAAA,EAA6D;EAAA,IAA5D;IAAEC,QAAQ,GAAG,GAAG;IAAEC,MAAM,GAAG,IAAI;IAAEC,QAAQ,GAAG,CAAC;IAAEC,IAAI,GAAG;EAAG,CAAC,GAAAJ,IAAA;EAC1E,IAAIK,QAAQ;EACZ,IAAIC,UAAU;EACdhB,OAAO,CAACW,QAAQ,IAAIT,qBAAqB,CAACI,WAAW,CAAC,EAAE,4CAA4C,CAAC;EACrG,IAAIW,YAAY,GAAG,CAAC,GAAGL,MAAM;EAC7B;AACJ;AACA;EACIK,YAAY,GAAGhB,KAAK,CAACM,UAAU,EAAEC,UAAU,EAAES,YAAY,CAAC;EAC1DN,QAAQ,GAAGV,KAAK,CAACI,WAAW,EAAEC,WAAW,EAAEH,qBAAqB,CAACQ,QAAQ,CAAC,CAAC;EAC3E,IAAIM,YAAY,GAAG,CAAC,EAAE;IAClB;AACR;AACA;IACQF,QAAQ,GAAIG,YAAY,IAAK;MACzB,MAAMC,gBAAgB,GAAGD,YAAY,GAAGD,YAAY;MACpD,MAAMG,KAAK,GAAGD,gBAAgB,GAAGR,QAAQ;MACzC,MAAMU,CAAC,GAAGF,gBAAgB,GAAGN,QAAQ;MACrC,MAAMS,CAAC,GAAGC,eAAe,CAACL,YAAY,EAAED,YAAY,CAAC;MACrD,MAAMO,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACN,KAAK,CAAC;MAC1B,OAAOhB,OAAO,GAAIiB,CAAC,GAAGC,CAAC,GAAIE,CAAC;IAChC,CAAC;IACDR,UAAU,GAAIE,YAAY,IAAK;MAC3B,MAAMC,gBAAgB,GAAGD,YAAY,GAAGD,YAAY;MACpD,MAAMG,KAAK,GAAGD,gBAAgB,GAAGR,QAAQ;MACzC,MAAMgB,CAAC,GAAGP,KAAK,GAAGP,QAAQ,GAAGA,QAAQ;MACrC,MAAMe,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACZ,YAAY,EAAE,CAAC,CAAC,GAAGQ,IAAI,CAACI,GAAG,CAACX,YAAY,EAAE,CAAC,CAAC,GAAGP,QAAQ;MAC1E,MAAMmB,CAAC,GAAGL,IAAI,CAACC,GAAG,CAAC,CAACN,KAAK,CAAC;MAC1B,MAAMW,CAAC,GAAGR,eAAe,CAACE,IAAI,CAACI,GAAG,CAACX,YAAY,EAAE,CAAC,CAAC,EAAED,YAAY,CAAC;MAClE,MAAMe,MAAM,GAAG,CAACjB,QAAQ,CAACG,YAAY,CAAC,GAAGd,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC7D,OAAQ4B,MAAM,IAAI,CAACL,CAAC,GAAGC,CAAC,IAAIE,CAAC,CAAC,GAAIC,CAAC;IACvC,CAAC;EACL,CAAC,MACI;IACD;AACR;AACA;IACQhB,QAAQ,GAAIG,YAAY,IAAK;MACzB,MAAMG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,CAACR,YAAY,GAAGP,QAAQ,CAAC;MAC5C,MAAMW,CAAC,GAAG,CAACJ,YAAY,GAAGL,QAAQ,IAAIF,QAAQ,GAAG,CAAC;MAClD,OAAO,CAACP,OAAO,GAAGiB,CAAC,GAAGC,CAAC;IAC3B,CAAC;IACDN,UAAU,GAAIE,YAAY,IAAK;MAC3B,MAAMG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,CAACR,YAAY,GAAGP,QAAQ,CAAC;MAC5C,MAAMW,CAAC,GAAG,CAACT,QAAQ,GAAGK,YAAY,KAAKP,QAAQ,GAAGA,QAAQ,CAAC;MAC3D,OAAOU,CAAC,GAAGC,CAAC;IAChB,CAAC;EACL;EACA,MAAMW,YAAY,GAAG,CAAC,GAAGtB,QAAQ;EACjC,MAAMO,YAAY,GAAGgB,eAAe,CAACnB,QAAQ,EAAEC,UAAU,EAAEiB,YAAY,CAAC;EACxEtB,QAAQ,GAAGT,qBAAqB,CAACS,QAAQ,CAAC;EAC1C,IAAIwB,KAAK,CAACjB,YAAY,CAAC,EAAE;IACrB,OAAO;MACHkB,SAAS,EAAE,GAAG;MACdC,OAAO,EAAE,EAAE;MACX1B;IACJ,CAAC;EACL,CAAC,MACI;IACD,MAAMyB,SAAS,GAAGX,IAAI,CAACI,GAAG,CAACX,YAAY,EAAE,CAAC,CAAC,GAAGJ,IAAI;IAClD,OAAO;MACHsB,SAAS;MACTC,OAAO,EAAEpB,YAAY,GAAG,CAAC,GAAGQ,IAAI,CAACa,IAAI,CAACxB,IAAI,GAAGsB,SAAS,CAAC;MACvDzB;IACJ,CAAC;EACL;AACJ;AACA,MAAM4B,cAAc,GAAG,EAAE;AACzB,SAASL,eAAeA,CAACnB,QAAQ,EAAEC,UAAU,EAAEiB,YAAY,EAAE;EACzD,IAAIO,MAAM,GAAGP,YAAY;EACzB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,EAAE,EAAE;IACrCD,MAAM,GAAGA,MAAM,GAAGzB,QAAQ,CAACyB,MAAM,CAAC,GAAGxB,UAAU,CAACwB,MAAM,CAAC;EAC3D;EACA,OAAOA,MAAM;AACjB;AACA,SAASjB,eAAeA,CAACL,YAAY,EAAED,YAAY,EAAE;EACjD,OAAOC,YAAY,GAAGO,IAAI,CAACa,IAAI,CAAC,CAAC,GAAGrB,YAAY,GAAGA,YAAY,CAAC;AACpE;AAEA,SAASM,eAAe,EAAEd,UAAU,EAAED,UAAU,EAAEF,WAAW,EAAEC,UAAU,EAAEF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}