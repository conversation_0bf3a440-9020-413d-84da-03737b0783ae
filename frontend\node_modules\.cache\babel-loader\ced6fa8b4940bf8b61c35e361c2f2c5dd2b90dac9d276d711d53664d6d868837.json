{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Typography from '../Typography';\nimport { getAlertTitleUtilityClass } from './alertTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAlertTitleUtilityClass, classes);\n};\nconst AlertTitleRoot = styled(Typography, {\n  name: 'MuiAlertTitle',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    fontWeight: theme.typography.fontWeightMedium,\n    marginTop: -2\n  };\n});\nconst AlertTitle = /*#__PURE__*/React.forwardRef(function AlertTitle(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAlertTitle'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AlertTitleRoot, _extends({\n    gutterBottom: true,\n    component: \"div\",\n    ownerState: ownerState,\n    ref: ref,\n    className: clsx(classes.root, className)\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AlertTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AlertTitle;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "Typography", "getAlertTitleUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "AlertTitleRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "fontWeight", "typography", "fontWeightMedium", "marginTop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "inProps", "ref", "className", "other", "gutterBottom", "component", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/AlertTitle/AlertTitle.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Typography from '../Typography';\nimport { getAlertTitleUtilityClass } from './alertTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAlertTitleUtilityClass, classes);\n};\nconst AlertTitleRoot = styled(Typography, {\n  name: 'MuiAlertTitle',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => {\n  return {\n    fontWeight: theme.typography.fontWeightMedium,\n    marginTop: -2\n  };\n});\nconst AlertTitle = /*#__PURE__*/React.forwardRef(function AlertTitle(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAlertTitle'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AlertTitleRoot, _extends({\n    gutterBottom: true,\n    component: \"div\",\n    ownerState: ownerState,\n    ref: ref,\n    className: clsx(classes.root, className)\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AlertTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AlertTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOX,cAAc,CAACU,KAAK,EAAEN,yBAAyB,EAAEK,OAAO,CAAC;AAClE,CAAC;AACD,MAAMG,cAAc,GAAGX,MAAM,CAACE,UAAU,EAAE;EACxCU,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA,IAEG;EAAA,IAFF;IACFC;EACF,CAAC,GAAAD,IAAA;EACC,OAAO;IACLE,UAAU,EAAED,KAAK,CAACE,UAAU,CAACC,gBAAgB;IAC7CC,SAAS,EAAE,CAAC;EACd,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,UAAU,GAAG,aAAa3B,KAAK,CAAC4B,UAAU,CAAC,SAASD,UAAUA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACjF,MAAMX,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAEU,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFe;IACF,CAAC,GAAGZ,KAAK;IACTa,KAAK,GAAGlC,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAMY,UAAU,GAAGQ,KAAK;EACxB,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,cAAc,EAAElB,QAAQ,CAAC;IAChDoC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,KAAK;IAChBvB,UAAU,EAAEA,UAAU;IACtBmB,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAE7B,IAAI,CAACU,OAAO,CAACE,IAAI,EAAEiB,SAAS;EACzC,CAAC,EAAEC,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,UAAU,CAACW,SAAS,CAAC,yBAAyB;EACpF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEtC,SAAS,CAACuC,IAAI;EACxB;AACF;AACA;EACE5B,OAAO,EAAEX,SAAS,CAACwC,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAE9B,SAAS,CAACyC,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAE1C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC4C,OAAO,CAAC5C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACwC,MAAM,EAAExC,SAAS,CAAC8C,IAAI,CAAC,CAAC,CAAC,EAAE9C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACwC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}