{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rowHeight\", \"gap\", \"style\", \"variant\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getImageListUtilityClass } from './imageListClasses';\nimport ImageListContext from './ImageListContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant]\n  };\n  return composeClasses(slots, getImageListUtilityClass, classes);\n};\nconst ImageListRoot = styled('ul', {\n  name: 'MuiImageList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant]];\n  }\n})(({\n  ownerState\n}) => {\n  return _extends({\n    display: 'grid',\n    overflowY: 'auto',\n    listStyle: 'none',\n    padding: 0,\n    // Add iOS momentum scrolling for iOS < 13.0\n    WebkitOverflowScrolling: 'touch'\n  }, ownerState.variant === 'masonry' && {\n    display: 'block'\n  });\n});\nconst ImageList = /*#__PURE__*/React.forwardRef(function ImageList(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiImageList'\n  });\n  const {\n      children,\n      className,\n      cols = 2,\n      component = 'ul',\n      rowHeight = 'auto',\n      gap = 4,\n      style: styleProp,\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const contextValue = React.useMemo(() => ({\n    rowHeight,\n    gap,\n    variant\n  }), [rowHeight, gap, variant]);\n  React.useEffect(() => {\n    if (process.env.NODE_ENV !== 'production') {\n      // Detect Internet Explorer 8+\n      if (document !== undefined && 'objectFit' in document.documentElement.style === false) {\n        console.error(['MUI: ImageList v5+ no longer natively supports Internet Explorer.', 'Use v4 of this component instead, or polyfill CSS object-fit.'].join('\\n'));\n      }\n    }\n  }, []);\n  const style = variant === 'masonry' ? _extends({\n    columnCount: cols,\n    columnGap: gap\n  }, styleProp) : _extends({\n    gridTemplateColumns: `repeat(${cols}, 1fr)`,\n    gap\n  }, styleProp);\n  const ownerState = _extends({}, props, {\n    component,\n    gap,\n    rowHeight,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: style,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(ImageListContext.Provider, {\n      value: contextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `ImageListItem`s.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.node.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Number of columns.\n   * @default 2\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The gap between items in px.\n   * @default 4\n   */\n  gap: PropTypes.number,\n  /**\n   * The height of one row in px.\n   * @default 'auto'\n   */\n  rowHeight: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['masonry', 'quilted', 'standard', 'woven']), PropTypes.string])\n} : void 0;\nexport default ImageList;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "composeClasses", "integerPropType", "clsx", "PropTypes", "React", "styled", "useDefaultProps", "getImageListUtilityClass", "ImageListContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "variant", "slots", "root", "ImageListRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "overflowY", "listStyle", "padding", "WebkitOverflowScrolling", "ImageList", "forwardRef", "inProps", "ref", "children", "className", "cols", "component", "rowHeight", "gap", "style", "styleProp", "other", "contextValue", "useMemo", "useEffect", "process", "env", "NODE_ENV", "document", "undefined", "documentElement", "console", "error", "join", "columnCount", "columnGap", "gridTemplateColumns", "as", "Provider", "value", "propTypes", "node", "isRequired", "object", "string", "elementType", "number", "oneOfType", "oneOf", "sx", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/ImageList/ImageList.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rowHeight\", \"gap\", \"style\", \"variant\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getImageListUtilityClass } from './imageListClasses';\nimport ImageListContext from './ImageListContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant]\n  };\n  return composeClasses(slots, getImageListUtilityClass, classes);\n};\nconst ImageListRoot = styled('ul', {\n  name: 'MuiImageList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant]];\n  }\n})(({\n  ownerState\n}) => {\n  return _extends({\n    display: 'grid',\n    overflowY: 'auto',\n    listStyle: 'none',\n    padding: 0,\n    // Add iOS momentum scrolling for iOS < 13.0\n    WebkitOverflowScrolling: 'touch'\n  }, ownerState.variant === 'masonry' && {\n    display: 'block'\n  });\n});\nconst ImageList = /*#__PURE__*/React.forwardRef(function ImageList(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiImageList'\n  });\n  const {\n      children,\n      className,\n      cols = 2,\n      component = 'ul',\n      rowHeight = 'auto',\n      gap = 4,\n      style: styleProp,\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const contextValue = React.useMemo(() => ({\n    rowHeight,\n    gap,\n    variant\n  }), [rowHeight, gap, variant]);\n  React.useEffect(() => {\n    if (process.env.NODE_ENV !== 'production') {\n      // Detect Internet Explorer 8+\n      if (document !== undefined && 'objectFit' in document.documentElement.style === false) {\n        console.error(['MUI: ImageList v5+ no longer natively supports Internet Explorer.', 'Use v4 of this component instead, or polyfill CSS object-fit.'].join('\\n'));\n      }\n    }\n  }, []);\n  const style = variant === 'masonry' ? _extends({\n    columnCount: cols,\n    columnGap: gap\n  }, styleProp) : _extends({\n    gridTemplateColumns: `repeat(${cols}, 1fr)`,\n    gap\n  }, styleProp);\n  const ownerState = _extends({}, props, {\n    component,\n    gap,\n    rowHeight,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: style,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(ImageListContext.Provider, {\n      value: contextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `ImageListItem`s.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.node.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Number of columns.\n   * @default 2\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The gap between items in px.\n   * @default 4\n   */\n  gap: PropTypes.number,\n  /**\n   * The height of one row in px.\n   * @default 'auto'\n   */\n  rowHeight: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['masonry', 'quilted', 'standard', 'woven']), PropTypes.string])\n} : void 0;\nexport default ImageList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;AACxG,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,OAAO;EACxB,CAAC;EACD,OAAOd,cAAc,CAACe,KAAK,EAAER,wBAAwB,EAAEM,OAAO,CAAC;AACjE,CAAC;AACD,MAAMI,aAAa,GAAGZ,MAAM,CAAC,IAAI,EAAE;EACjCa,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACV,UAAU,CAACE,OAAO,CAAC,CAAC;EAClD;AACF,CAAC,CAAC,CAAC,CAAC;EACFF;AACF,CAAC,KAAK;EACJ,OAAOd,QAAQ,CAAC;IACdyB,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,CAAC;IACV;IACAC,uBAAuB,EAAE;EAC3B,CAAC,EAAEf,UAAU,CAACE,OAAO,KAAK,SAAS,IAAI;IACrCS,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAMK,SAAS,GAAG,aAAaxB,KAAK,CAACyB,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAMV,KAAK,GAAGf,eAAe,CAAC;IAC5Be,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFc,QAAQ;MACRC,SAAS;MACTC,IAAI,GAAG,CAAC;MACRC,SAAS,GAAG,IAAI;MAChBC,SAAS,GAAG,MAAM;MAClBC,GAAG,GAAG,CAAC;MACPC,KAAK,EAAEC,SAAS;MAChBzB,OAAO,GAAG;IACZ,CAAC,GAAGO,KAAK;IACTmB,KAAK,GAAG3C,6BAA6B,CAACwB,KAAK,EAAEtB,SAAS,CAAC;EACzD,MAAM0C,YAAY,GAAGrC,KAAK,CAACsC,OAAO,CAAC,OAAO;IACxCN,SAAS;IACTC,GAAG;IACHvB;EACF,CAAC,CAAC,EAAE,CAACsB,SAAS,EAAEC,GAAG,EAAEvB,OAAO,CAAC,CAAC;EAC9BV,KAAK,CAACuC,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA,IAAIC,QAAQ,KAAKC,SAAS,IAAI,WAAW,IAAID,QAAQ,CAACE,eAAe,CAACX,KAAK,KAAK,KAAK,EAAE;QACrFY,OAAO,CAACC,KAAK,CAAC,CAAC,mEAAmE,EAAE,+DAA+D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAClK;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMd,KAAK,GAAGxB,OAAO,KAAK,SAAS,GAAGhB,QAAQ,CAAC;IAC7CuD,WAAW,EAAEnB,IAAI;IACjBoB,SAAS,EAAEjB;EACb,CAAC,EAAEE,SAAS,CAAC,GAAGzC,QAAQ,CAAC;IACvByD,mBAAmB,EAAE,UAAUrB,IAAI,QAAQ;IAC3CG;EACF,CAAC,EAAEE,SAAS,CAAC;EACb,MAAM3B,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACrCc,SAAS;IACTE,GAAG;IACHD,SAAS;IACTtB;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,aAAa,EAAEnB,QAAQ,CAAC;IAC/C0D,EAAE,EAAErB,SAAS;IACbF,SAAS,EAAE/B,IAAI,CAACW,OAAO,CAACG,IAAI,EAAEH,OAAO,CAACC,OAAO,CAAC,EAAEmB,SAAS,CAAC;IAC1DF,GAAG,EAAEA,GAAG;IACRO,KAAK,EAAEA,KAAK;IACZ1B,UAAU,EAAEA;EACd,CAAC,EAAE4B,KAAK,EAAE;IACRR,QAAQ,EAAE,aAAatB,IAAI,CAACF,gBAAgB,CAACiD,QAAQ,EAAE;MACrDC,KAAK,EAAEjB,YAAY;MACnBT,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlB,SAAS,CAAC+B,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACE3B,QAAQ,EAAE7B,SAAS,CAAC,sCAAsCyD,IAAI,CAACC,UAAU;EACzE;AACF;AACA;EACEhD,OAAO,EAAEV,SAAS,CAAC2D,MAAM;EACzB;AACF;AACA;EACE7B,SAAS,EAAE9B,SAAS,CAAC4D,MAAM;EAC3B;AACF;AACA;AACA;EACE7B,IAAI,EAAEjC,eAAe;EACrB;AACF;AACA;AACA;EACEkC,SAAS,EAAEhC,SAAS,CAAC6D,WAAW;EAChC;AACF;AACA;AACA;EACE3B,GAAG,EAAElC,SAAS,CAAC8D,MAAM;EACrB;AACF;AACA;AACA;EACE7B,SAAS,EAAEjC,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACgE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEhE,SAAS,CAAC8D,MAAM,CAAC,CAAC;EAC7E;AACF;AACA;EACE3B,KAAK,EAAEnC,SAAS,CAAC2D,MAAM;EACvB;AACF;AACA;EACEM,EAAE,EAAEjE,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACkE,OAAO,CAAClE,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACmE,IAAI,EAAEnE,SAAS,CAAC2D,MAAM,EAAE3D,SAAS,CAACoE,IAAI,CAAC,CAAC,CAAC,EAAEpE,SAAS,CAACmE,IAAI,EAAEnE,SAAS,CAAC2D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEhD,OAAO,EAAEX,SAAS,CAAC,sCAAsC+D,SAAS,CAAC,CAAC/D,SAAS,CAACgE,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,EAAEhE,SAAS,CAAC4D,MAAM,CAAC;AACrJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAenC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}