{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"9\",\n  cy: \"8\",\n  r: \"2\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M14.48 16.34C13.29 15.73 11.37 15 9 15c-2.37 0-4.29.73-5.48 1.34-.32.16-.52.5-.52.88V18h12v-.78c0-.38-.2-.72-.52-.88\",\n  opacity: \".3\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0-6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m11 3V6h-2v3h-3v2h3v3h2v-3h3V9zm-4.61 5.56C13.71 13.7 11.53 13 9 13s-4.71.7-6.39 1.56C1.61 15.07 1 16.1 1 17.22V20h16v-2.78c0-1.12-.61-2.15-1.61-2.66M15 18H3v-.78c0-.38.2-.72.52-.88C4.71 15.73 6.63 15 9 15c2.37 0 4.29.73 5.48 1.34.32.16.52.5.52.88z\"\n}, \"2\")], 'PersonAddAltTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "cx", "cy", "r", "opacity", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/PersonAddAltTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"9\",\n  cy: \"8\",\n  r: \"2\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M14.48 16.34C13.29 15.73 11.37 15 9 15c-2.37 0-4.29.73-5.48 1.34-.32.16-.52.5-.52.88V18h12v-.78c0-.38-.2-.72-.52-.88\",\n  opacity: \".3\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0-6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m11 3V6h-2v3h-3v2h3v3h2v-3h3V9zm-4.61 5.56C13.71 13.7 11.53 13 9 13s-4.71.7-6.39 1.56C1.61 15.07 1 16.1 1 17.22V20h16v-2.78c0-1.12-.61-2.15-1.61-2.66M15 18H3v-.78c0-.38.2-.72.52-.88C4.71 15.73 6.63 15 9 15c2.37 0 4.29.73 5.48 1.34.32.16.52.5.52.88z\"\n}, \"2\")], 'PersonAddAltTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,QAAQ,EAAE;EACxDC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,CAAC,EAAE,GAAG;EACNC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCK,CAAC,EAAE,sHAAsH;EACzHD,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCK,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}