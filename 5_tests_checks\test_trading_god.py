#!/usr/bin/env python3
"""
Quick test of Trading God Engine functionality
"""

import requests
import json

def test_trading_god():
    """Test Trading God Engine responses"""
    
    base_url = "http://localhost:8080"
    
    test_questions = [
        "What's Apple trading at right now?",
        "I want to make $100 by tomorrow—what trade should I place?",
        "Can you suggest an options trade on NVIDIA?",
        "Are there any stocks that look like they might jump soon?"
    ]
    
    print("🚀 Testing A.T.L.A.S. Trading God Engine")
    print("=" * 50)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. Testing: {question}")
        
        data = {
            'message': question,
            'session_id': f'test_{i}',
            'context': {
                'panel': 'left',
                'interface_type': 'general_trading'
            }
        }
        
        try:
            response = requests.post(f'{base_url}/api/v1/chat', json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # Check for Trading God characteristics
                has_atlas_branding = 'a.t.l.a.s' in response_text.lower() or 'atlas' in response_text.lower()
                has_trading_data = '$' in response_text or '%' in response_text
                has_confidence = not any(phrase in response_text.lower() for phrase in [
                    "i can't", "i don't have", "not available", "cannot provide"
                ])
                
                print(f"   ✅ Response received ({len(response_text)} chars)")
                print(f"   🏷️  A.T.L.A.S. Branding: {'✅' if has_atlas_branding else '❌'}")
                print(f"   💰 Trading Data: {'✅' if has_trading_data else '❌'}")
                print(f"   🎯 Confident: {'✅' if has_confidence else '❌'}")
                
                # Show first 150 chars of response
                preview = response_text[:150] + "..." if len(response_text) > 150 else response_text
                print(f"   📝 Preview: {preview}")
                
                if has_atlas_branding and has_trading_data and has_confidence:
                    print(f"   🎉 TRADING GOD SUCCESS!")
                else:
                    print(f"   ⚠️  Needs improvement")
                    
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Trading God Engine Test Complete")

if __name__ == "__main__":
    try:
        test_trading_god()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
