{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport SystemDefaultPropsProvider, { useDefaultProps as useSystemDefaultProps } from '@mui/system/DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction DefaultPropsProvider(props) {\n  return /*#__PURE__*/_jsx(SystemDefaultPropsProvider, _extends({}, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? DefaultPropsProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  value: PropTypes.object.isRequired\n} : void 0;\nexport default DefaultPropsProvider;\nexport function useDefaultProps(params) {\n  return useSystemDefaultProps(params);\n}", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "SystemDefaultPropsProvider", "useDefaultProps", "useSystemDefaultProps", "jsx", "_jsx", "DefaultPropsProvider", "props", "process", "env", "NODE_ENV", "propTypes", "children", "node", "value", "object", "isRequired", "params"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/DefaultPropsProvider/DefaultPropsProvider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport SystemDefaultPropsProvider, { useDefaultProps as useSystemDefaultProps } from '@mui/system/DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction DefaultPropsProvider(props) {\n  return /*#__PURE__*/_jsx(SystemDefaultPropsProvider, _extends({}, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? DefaultPropsProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  value: PropTypes.object.isRequired\n} : void 0;\nexport default DefaultPropsProvider;\nexport function useDefaultProps(params) {\n  return useSystemDefaultProps(params);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,0BAA0B,IAAIC,eAAe,IAAIC,qBAAqB,QAAQ,kCAAkC;AACvH,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EACnC,OAAO,aAAaF,IAAI,CAACJ,0BAA0B,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,CAAC,CAAC;AAC3E;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,oBAAoB,CAACK,SAAS,CAAC,yBAAyB;EAC9F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEZ,SAAS,CAACa,IAAI;EACxB;AACF;AACA;EACEC,KAAK,EAAEd,SAAS,CAACe,MAAM,CAACC;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,eAAeV,oBAAoB;AACnC,OAAO,SAASJ,eAAeA,CAACe,MAAM,EAAE;EACtC,OAAOd,qBAAqB,CAACc,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}