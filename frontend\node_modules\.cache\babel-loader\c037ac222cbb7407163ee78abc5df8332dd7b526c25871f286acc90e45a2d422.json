{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M15 9H9c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V10c0-.55-.45-1-1-1m-3 5.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25M7.82 6.82c.35.35.9.38 1.3.1C9.93 6.34 10.93 6 12 6c1.07 0 2.07.34 2.88.91.4.28.95.26 1.3-.09.43-.43.39-1.15-.09-1.5C14.94 4.49 13.53 4 12 4c-1.53 0-2.94.49-4.09 1.32-.49.35-.52 1.07-.09 1.5\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 0C9.36 0 6.94.93 5.05 2.47c-.46.38-.5 1.07-.08 1.49.36.36.93.39 1.32.07C7.84 2.77 9.83 2 12 2s4.16.77 5.7 2.04c.39.32.96.29 1.32-.07.42-.42.38-1.11-.08-1.49C17.06.93 14.64 0 12 0\"\n}, \"1\")], 'SettingsRemoteRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/SettingsRemoteRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M15 9H9c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V10c0-.55-.45-1-1-1m-3 5.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25M7.82 6.82c.35.35.9.38 1.3.1C9.93 6.34 10.93 6 12 6c1.07 0 2.07.34 2.88.91.4.28.95.26 1.3-.09.43-.43.39-1.15-.09-1.5C14.94 4.49 13.53 4 12 4c-1.53 0-2.94.49-4.09 1.32-.49.35-.52 1.07-.09 1.5\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 0C9.36 0 6.94.93 5.05 2.47c-.46.38-.5 1.07-.08 1.49.36.36.93.39 1.32.07C7.84 2.77 9.83 2 12 2s4.16.77 5.7 2.04c.39.32.96.29 1.32-.07.42-.42.38-1.11-.08-1.49C17.06.93 14.64 0 12 0\"\n}, \"1\")], 'SettingsRemoteRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}