{"ast": null, "code": "'use client';\n\nexport { default } from './Avatar';\nexport { default as avatarClasses } from './avatarClasses';\nexport * from './avatarClasses';", "map": {"version": 3, "names": ["default", "avatarClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Avatar/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Avatar';\nexport { default as avatarClasses } from './avatarClasses';\nexport * from './avatarClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}