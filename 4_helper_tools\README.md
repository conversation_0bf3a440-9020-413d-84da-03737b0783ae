# 4. Helper Tools

This folder contains utility components, configuration, and support systems.

## Files:
- `config.py` - System configuration and settings
- `models.py` - Data models and schemas
- `atlas_database_manager.py` - Database operations and management
- `atlas_performance_optimizer.py` - System performance optimization
- `atlas_education_engine.py` - Educational content and resources
- `atlas_proactive_assistant.py` - Proactive user assistance
- `atlas_ultimate_100_percent_enforcer.py` - Quality assurance system
- `atlas_guru_scoring_metrics.py` - Performance scoring metrics
- `atlas_compliance_engine.py` - Regulatory compliance
- `atlas_security_manager.py` - Security and authentication
- `atlas_ai_enhanced_risk_management.py` - AI-enhanced risk controls
- `capture_responses.py` - Response capture utility

## Purpose:
These files provide essential support functions - configuration, data management, security, compliance, and system utilities that keep A.T.L.A.S. running smoothly.
