{"ast": null, "code": "'use client';\n\nexport { default } from './Tab';\nexport { default as tabClasses } from './tabClasses';\nexport * from './tabClasses';", "map": {"version": 3, "names": ["default", "tabClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Tab/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Tab';\nexport { default as tabClasses } from './tabClasses';\nexport * from './tabClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASA,OAAO,IAAIC,UAAU,QAAQ,cAAc;AACpD,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}