{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ScatterChart = createLucideIcon(\"Scatter<PERSON>hart\", [[\"circle\", {\n  cx: \"7.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  key: \"1x97lo\"\n}], [\"circle\", {\n  cx: \"18.5\",\n  cy: \"5.5\",\n  r: \".5\",\n  key: \"56iowl\"\n}], [\"circle\", {\n  cx: \"11.5\",\n  cy: \"11.5\",\n  r: \".5\",\n  key: \"m9xkw9\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"16.5\",\n  r: \".5\",\n  key: \"14ln9z\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"14.5\",\n  r: \".5\",\n  key: \"14qxqt\"\n}], [\"path\", {\n  d: \"M3 3v18h18\",\n  key: \"1s2lah\"\n}]]);\nexport { <PERSON><PERSON><PERSON><PERSON><PERSON> as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\scatter-chart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ScatterChart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI3LjUiIGN5PSI3LjUiIHI9Ii41IiAvPgogIDxjaXJjbGUgY3g9IjE4LjUiIGN5PSI1LjUiIHI9Ii41IiAvPgogIDxjaXJjbGUgY3g9IjExLjUiIGN5PSIxMS41IiByPSIuNSIgLz4KICA8Y2lyY2xlIGN4PSI3LjUiIGN5PSIxNi41IiByPSIuNSIgLz4KICA8Y2lyY2xlIGN4PSIxNy41IiBjeT0iMTQuNSIgcj0iLjUiIC8+CiAgPHBhdGggZD0iTTMgM3YxOGgxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/scatter-chart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ScatterChart = createLucideIcon('ScatterChart', [\n  ['circle', { cx: '7.5', cy: '7.5', r: '.5', key: '1x97lo' }],\n  ['circle', { cx: '18.5', cy: '5.5', r: '.5', key: '56iowl' }],\n  ['circle', { cx: '11.5', cy: '11.5', r: '.5', key: 'm9xkw9' }],\n  ['circle', { cx: '7.5', cy: '16.5', r: '.5', key: '14ln9z' }],\n  ['circle', { cx: '17.5', cy: '14.5', r: '.5', key: '14qxqt' }],\n  ['path', { d: 'M3 3v18h18', key: '1s2lah' }],\n]);\n\nexport default ScatterChart;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,QAAU;EAAEC,EAAI;EAAOC,EAAI;EAAOC,CAAG;EAAMC,GAAK;AAAA,CAAU,GAC3D,CAAC,QAAU;EAAEH,EAAI;EAAQC,EAAI;EAAOC,CAAG;EAAMC,GAAK;AAAA,CAAU,GAC5D,CAAC,QAAU;EAAEH,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAMC,GAAK;AAAA,CAAU,GAC7D,CAAC,QAAU;EAAEH,EAAI;EAAOC,EAAI;EAAQC,CAAG;EAAMC,GAAK;AAAA,CAAU,GAC5D,CAAC,QAAU;EAAEH,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAMC,GAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}