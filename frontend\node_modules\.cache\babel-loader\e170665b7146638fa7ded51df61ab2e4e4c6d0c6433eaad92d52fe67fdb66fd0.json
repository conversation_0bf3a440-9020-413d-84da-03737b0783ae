{"ast": null, "code": "'use client';\n\nexport { default } from './CardHeader';\nexport { default as cardHeaderClasses } from './cardHeaderClasses';\nexport * from './cardHeaderClasses';", "map": {"version": 3, "names": ["default", "cardHeaderClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/CardHeader/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './CardHeader';\nexport { default as cardHeaderClasses } from './cardHeaderClasses';\nexport * from './cardHeaderClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,qBAAqB;AAClE,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}