{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinearProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiLinearProgress', slot);\n}\nconst linearProgressClasses = generateUtilityClasses('MuiLinearProgress', ['root', 'colorPrimary', 'colorSecondary', 'determinate', 'indeterminate', 'buffer', 'query', 'dashed', 'dashedColorPrimary', 'dashedColorSecondary', 'bar', 'barColorPrimary', 'barColorSecondary', 'bar1Indeterminate', 'bar1Determinate', 'bar1Buffer', 'bar2Indeterminate', 'bar2Buffer']);\nexport default linearProgressClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getLinearProgressUtilityClass", "slot", "linearProgressClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/LinearProgress/linearProgressClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinearProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiLinearProgress', slot);\n}\nconst linearProgressClasses = generateUtilityClasses('MuiLinearProgress', ['root', 'colorPrimary', 'colorSecondary', 'determinate', 'indeterminate', 'buffer', 'query', 'dashed', 'dashedColorPrimary', 'dashedColorSecondary', 'bar', 'barColorPrimary', 'barColorSecondary', 'bar1Indeterminate', 'bar1Determinate', 'bar1Buffer', 'bar2Indeterminate', 'bar2Buffer']);\nexport default linearProgressClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,KAAK,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,YAAY,EAAE,mBAAmB,EAAE,YAAY,CAAC,CAAC;AACxW,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}