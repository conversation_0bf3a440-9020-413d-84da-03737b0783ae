{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ChevronsUp = createLucideIcon(\"ChevronsUp\", [[\"path\", {\n  d: \"m17 11-5-5-5 5\",\n  key: \"e8nh98\"\n}], [\"path\", {\n  d: \"m17 18-5-5-5 5\",\n  key: \"2avn1x\"\n}]]);\nexport { ChevronsUp as default };", "map": {"version": 3, "names": ["ChevronsUp", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\chevrons-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronsUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTcgMTEtNS01LTUgNSIgLz4KICA8cGF0aCBkPSJtMTcgMTgtNS01LTUgNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevrons-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronsUp = createLucideIcon('ChevronsUp', [\n  ['path', { d: 'm17 11-5-5-5 5', key: 'e8nh98' }],\n  ['path', { d: 'm17 18-5-5-5 5', key: '2avn1x' }],\n]);\n\nexport default ChevronsUp;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}