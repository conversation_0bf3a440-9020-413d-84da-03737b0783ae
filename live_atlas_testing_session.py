#!/usr/bin/env python3
"""
A.T.L.A.S. Stock Market God - Comprehensive Live Testing Session
Real-time testing with detailed response analysis and 6-point format validation
"""

import requests
import json
import time
from datetime import datetime
import re

class ATLASLiveTester:
    def __init__(self):
        self.base_url = "http://localhost:8080"
        self.session = requests.Session()
        self.test_results = []
        
    def validate_6_point_format(self, response_text):
        """Validate the mandatory 6-point Stock Market God format"""
        sections = {
            "1. Why This Trade?": [
                r"(?i)why\s+this\s+trade",
                r"(?i)trade\s+rationale",
                r"(?i)reason\s+for\s+trade"
            ],
            "2. Win/Loss Probabilities": [
                r"(?i)win.*loss.*probabilit",
                r"(?i)chance.*hit",
                r"\d+%.*chance",
                r"(?i)probabilit.*win"
            ],
            "3. Potential Money In/Out": [
                r"(?i)potential\s+money",
                r"(?i)make.*\$\d+",
                r"(?i)lose.*\$\d+",
                r"(?i)profit.*\$\d+"
            ],
            "4. Smart Stop Plans": [
                r"(?i)smart\s+stop",
                r"(?i)stop.*plan",
                r"(?i)exit.*at",
                r"(?i)risk.*management"
            ],
            "5. Market Context": [
                r"(?i)market\s+context",
                r"(?i)market.*today",
                r"(?i)current.*market",
                r"(?i)market.*condition"
            ],
            "6. Confidence Score": [
                r"(?i)confidence.*\d+%",
                r"(?i)confidence.*score",
                r"(?i)confidence.*\d+"
            ]
        }
        
        found_sections = []
        missing_sections = []
        
        for section_name, patterns in sections.items():
            found = False
            for pattern in patterns:
                if re.search(pattern, response_text):
                    found = True
                    break
            
            if found:
                found_sections.append(section_name)
            else:
                missing_sections.append(section_name)
        
        score = len(found_sections)
        percentage = (score / 6) * 100
        
        return {
            "score": score,
            "percentage": percentage,
            "found_sections": found_sections,
            "missing_sections": missing_sections
        }
    
    def rate_response(self, response_text, format_validation):
        """Rate the response on 1-10 scale across 5 criteria"""
        ratings = {}
        
        # 1. Format Compliance (6-point structure)
        format_score = format_validation["score"]
        if format_score >= 6:
            ratings["Format Compliance"] = 10
        elif format_score >= 5:
            ratings["Format Compliance"] = 8
        elif format_score >= 4:
            ratings["Format Compliance"] = 6
        elif format_score >= 3:
            ratings["Format Compliance"] = 4
        else:
            ratings["Format Compliance"] = 2
        
        # 2. Trading Accuracy (realistic data)
        has_specific_data = bool(re.search(r'\$\d+', response_text))
        has_percentages = bool(re.search(r'\d+%', response_text))
        has_prices = bool(re.search(r'\$\d+\.\d+', response_text))
        
        accuracy_score = 0
        if has_specific_data: accuracy_score += 3
        if has_percentages: accuracy_score += 3
        if has_prices: accuracy_score += 4
        ratings["Trading Accuracy"] = min(10, accuracy_score)
        
        # 3. Confidence Level (Trading God persona)
        ai_disclaimers = [
            "i can't", "i cannot", "i'm not able", "i don't have access",
            "as an ai", "i'm an ai", "disclaimer", "not financial advice"
        ]
        has_disclaimers = any(disclaimer in response_text.lower() for disclaimer in ai_disclaimers)
        
        confident_language = [
            "will", "going to", "expect", "target", "confident", "certain"
        ]
        has_confidence = any(word in response_text.lower() for word in confident_language)
        
        if has_disclaimers:
            ratings["Confidence Level"] = 2
        elif has_confidence:
            ratings["Confidence Level"] = 9
        else:
            ratings["Confidence Level"] = 6
        
        # 4. Actionability (specific executable advice)
        actionable_terms = [
            "buy", "sell", "exit at", "enter at", "stop loss", "take profit",
            "shares", "contracts", "strike", "expiration"
        ]
        action_count = sum(1 for term in actionable_terms if term in response_text.lower())
        ratings["Actionability"] = min(10, action_count * 2)
        
        # 5. Professional Quality (institutional level)
        professional_terms = [
            "analysis", "technical", "fundamental", "volatility", "liquidity",
            "risk management", "portfolio", "allocation", "diversification"
        ]
        prof_count = sum(1 for term in professional_terms if term in response_text.lower())
        ratings["Professional Quality"] = min(10, prof_count * 2)
        
        return ratings
    
    def send_test_question(self, question, category):
        """Send a test question to A.T.L.A.S. and analyze the response"""
        print(f"\n{'='*80}")
        print(f"🧪 TESTING: {category}")
        print(f"{'='*80}")
        print(f"📝 QUESTION: {question}")
        print(f"⏰ Timestamp: {datetime.now().strftime('%H:%M:%S')}")
        print(f"🔄 Sending request to {self.base_url}/api/v1/chat...")
        
        try:
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/v1/chat",
                json={
                    "message": question,
                    "session_id": f"live_test_{int(time.time())}"
                },
                timeout=60
            )
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"📊 HTTP Status: {response.status_code}")
            print(f"⚡ Response Time: {response_time:.2f} seconds")
            
            if response.status_code == 200:
                data = response.json()
                atlas_response = data.get("response", "No response field found")
                
                print(f"\n🔮 A.T.L.A.S. RESPONSE:")
                print("-" * 60)
                print(atlas_response)
                print("-" * 60)
                
                # Validate 6-point format
                format_validation = self.validate_6_point_format(atlas_response)
                print(f"\n📋 FORMAT VALIDATION:")
                print(f"   Score: {format_validation['score']}/6 ({format_validation['percentage']:.1f}%)")
                print(f"   Found: {', '.join(format_validation['found_sections'])}")
                if format_validation['missing_sections']:
                    print(f"   Missing: {', '.join(format_validation['missing_sections'])}")
                
                # Rate the response
                ratings = self.rate_response(atlas_response, format_validation)
                print(f"\n⭐ QUALITY RATINGS (1-10 scale):")
                total_score = 0
                for criterion, score in ratings.items():
                    print(f"   {criterion}: {score}/10")
                    total_score += score
                
                average_score = total_score / len(ratings)
                print(f"   📊 OVERALL AVERAGE: {average_score:.1f}/10")
                
                # Store results
                test_result = {
                    "category": category,
                    "question": question,
                    "response": atlas_response,
                    "http_status": response.status_code,
                    "response_time": response_time,
                    "format_validation": format_validation,
                    "ratings": ratings,
                    "average_score": average_score,
                    "timestamp": datetime.now().isoformat()
                }
                self.test_results.append(test_result)
                
                return test_result
                
            else:
                print(f"❌ ERROR: HTTP {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"💥 EXCEPTION: {e}")
            return None
    
    def run_comprehensive_test_suite(self):
        """Execute the complete test suite"""
        print("🚀 A.T.L.A.S. STOCK MARKET GOD - COMPREHENSIVE LIVE TESTING")
        print("=" * 80)
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Target: {self.base_url}")
        
        # Test questions by category
        test_questions = [
            # Real-time Stock Prices
            ("What's Apple's current stock price?", "Real-time Stock Prices"),
            ("Give me Tesla's live price and trading recommendation", "Real-time Stock Prices"),
            
            # Goal-based Trading
            ("Help me make $500 this week with stock trades", "Goal-based Trading"),
            ("I want to make $200 by Friday - what trades should I make?", "Goal-based Trading"),
            
            # Technical Analysis
            ("Show me TTM Squeeze signals for SPY", "Technical Analysis"),
            ("What stocks have bullish momentum right now?", "Technical Analysis"),
            
            # Options Trading
            ("What are the best call options for NVDA this week?", "Options Trading"),
            ("Should I buy puts on QQQ? Give me specific strikes and expirations", "Options Trading"),
            
            # Risk Management
            ("What's my current portfolio risk level?", "Risk Management"),
            ("How should I protect my positions if the market crashes?", "Risk Management")
        ]
        
        # Execute each test
        for question, category in test_questions:
            result = self.send_test_question(question, category)
            if result:
                time.sleep(2)  # Brief pause between tests
        
        # Generate summary
        self.generate_test_summary()
    
    def generate_test_summary(self):
        """Generate comprehensive test summary"""
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print(f"{'='*80}")
        
        if not self.test_results:
            print("❌ No test results to summarize")
            return
        
        # Overall statistics
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["http_status"] == 200])
        avg_response_time = sum(r["response_time"] for r in self.test_results) / total_tests
        avg_format_score = sum(r["format_validation"]["score"] for r in self.test_results) / total_tests
        avg_overall_score = sum(r["average_score"] for r in self.test_results) / total_tests
        
        print(f"📈 OVERALL STATISTICS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {successful_tests}/{total_tests} ({(successful_tests/total_tests)*100:.1f}%)")
        print(f"   Avg Response Time: {avg_response_time:.2f} seconds")
        print(f"   Avg Format Score: {avg_format_score:.1f}/6 ({(avg_format_score/6)*100:.1f}%)")
        print(f"   Avg Overall Quality: {avg_overall_score:.1f}/10")
        
        # Category breakdown
        categories = {}
        for result in self.test_results:
            cat = result["category"]
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(result)
        
        print(f"\n📋 CATEGORY BREAKDOWN:")
        for category, results in categories.items():
            cat_avg = sum(r["average_score"] for r in results) / len(results)
            cat_format = sum(r["format_validation"]["score"] for r in results) / len(results)
            print(f"   {category}: {cat_avg:.1f}/10 avg quality, {cat_format:.1f}/6 avg format")
        
        # Best and worst performing tests
        best_test = max(self.test_results, key=lambda x: x["average_score"])
        worst_test = min(self.test_results, key=lambda x: x["average_score"])
        
        print(f"\n🏆 BEST PERFORMING TEST:")
        print(f"   Category: {best_test['category']}")
        print(f"   Score: {best_test['average_score']:.1f}/10")
        print(f"   Question: {best_test['question'][:60]}...")
        
        print(f"\n⚠️  LOWEST PERFORMING TEST:")
        print(f"   Category: {worst_test['category']}")
        print(f"   Score: {worst_test['average_score']:.1f}/10")
        print(f"   Question: {worst_test['question'][:60]}...")
        
        print(f"\n✅ Testing completed at {datetime.now().strftime('%H:%M:%S')}")

def main():
    """Main execution function"""
    tester = ATLASLiveTester()
    tester.run_comprehensive_test_suite()

if __name__ == "__main__":
    main()
