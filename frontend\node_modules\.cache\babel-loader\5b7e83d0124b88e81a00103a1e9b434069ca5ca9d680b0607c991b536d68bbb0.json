{"ast": null, "code": "'use client';\n\nexport { default } from './CardActionArea';\nexport { default as cardActionAreaClasses } from './cardActionAreaClasses';\nexport * from './cardActionAreaClasses';", "map": {"version": 3, "names": ["default", "cardActionAreaClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/CardActionArea/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './CardActionArea';\nexport { default as cardActionAreaClasses } from './cardActionAreaClasses';\nexport * from './cardActionAreaClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,yBAAyB;AAC1E,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}