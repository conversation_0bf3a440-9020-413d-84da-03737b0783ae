{"ast": null, "code": "'use client';\n\nexport { default } from './IconButton';\nexport { default as iconButtonClasses } from './iconButtonClasses';\nexport * from './iconButtonClasses';", "map": {"version": 3, "names": ["default", "iconButtonClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/IconButton/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './IconButton';\nexport { default as iconButtonClasses } from './iconButtonClasses';\nexport * from './iconButtonClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,qBAAqB;AAClE,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}