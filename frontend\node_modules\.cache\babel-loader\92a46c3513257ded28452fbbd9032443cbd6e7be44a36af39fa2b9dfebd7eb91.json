{"ast": null, "code": "'use client';\n\nexport { ModalManager } from './ModalManager';\nexport { default } from './Modal';\nexport { default as modalClasses } from './modalClasses';\nexport * from './modalClasses';", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "default", "modalClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Modal/index.js"], "sourcesContent": ["'use client';\n\nexport { ModalManager } from './ModalManager';\nexport { default } from './Modal';\nexport { default as modalClasses } from './modalClasses';\nexport * from './modalClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}