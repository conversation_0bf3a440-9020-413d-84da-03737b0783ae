{"ast": null, "code": "/**\n * @internal These variables should not appear in the :root stylesheet when the `defaultMode=\"dark\"`\n */\nconst excludeVariablesFromRoot = cssVarPrefix => [...[...Array(24)].map((_, index) => \"--\".concat(cssVarPrefix ? \"\".concat(cssVarPrefix, \"-\") : '', \"overlays-\").concat(index + 1)), \"--\".concat(cssVarPrefix ? \"\".concat(cssVarPrefix, \"-\") : '', \"palette-AppBar-darkBg\"), \"--\".concat(cssVarPrefix ? \"\".concat(cssVarPrefix, \"-\") : '', \"palette-AppBar-darkColor\")];\nexport default excludeVariablesFromRoot;", "map": {"version": 3, "names": ["excludeVariablesFromRoot", "cssVarPrefix", "Array", "map", "_", "index", "concat"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/styles/excludeVariablesFromRoot.js"], "sourcesContent": ["/**\n * @internal These variables should not appear in the :root stylesheet when the `defaultMode=\"dark\"`\n */\nconst excludeVariablesFromRoot = cssVarPrefix => [...[...Array(24)].map((_, index) => `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}overlays-${index + 1}`), `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkBg`, `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkColor`];\nexport default excludeVariablesFromRoot;"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,wBAAwB,GAAGC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,UAAAC,MAAA,CAAUL,YAAY,MAAAK,MAAA,CAAML,YAAY,SAAM,EAAE,eAAAK,MAAA,CAAYD,KAAK,GAAG,CAAC,CAAE,CAAC,OAAAC,MAAA,CAAOL,YAAY,MAAAK,MAAA,CAAML,YAAY,SAAM,EAAE,iCAAAK,MAAA,CAA8BL,YAAY,MAAAK,MAAA,CAAML,YAAY,SAAM,EAAE,8BAA2B;AACrS,eAAeD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}