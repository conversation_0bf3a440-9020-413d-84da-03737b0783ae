{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M15 9.86v4.28L18.03 12zm-9 0v4.28L9.03 12z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m4 18 8.5-6L4 6zm2-8.14L9.03 12 6 14.14zM21.5 12 13 6v12zM15 9.86 18.03 12 15 14.14z\"\n}, \"1\")], 'FastForwardTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/FastForwardTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M15 9.86v4.28L18.03 12zm-9 0v4.28L9.03 12z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m4 18 8.5-6L4 6zm2-8.14L9.03 12 6 14.14zM21.5 12 13 6v12zM15 9.86 18.03 12 15 14.14z\"\n}, \"1\")], 'FastForwardTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,4CAA4C;EAC/CC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}