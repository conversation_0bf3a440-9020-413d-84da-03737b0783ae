{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsxs(\"g\", {\n  opacity: \".3\",\n  children: [/*#__PURE__*/_jsx(\"defs\", {\n    children: /*#__PURE__*/_jsx(\"path\", {\n      id: \"a\",\n      d: \"M4 8h10v11H4z\",\n      opacity: \".3\"\n    })\n  }), /*#__PURE__*/_jsx(\"use\", {\n    xlinkHref: \"#a\",\n    overflow: \"visible\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    d: \"M4 19h10V8H4zm1-7h2.5V9.5h3V12H13v3h-2.5v2.5h-3V15H5z\"\n  })]\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 3h12v2H3zm11 3H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2m0 13H4V8h10z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.5 17.5h3V15H13v-3h-2.5V9.5h-3V12H5v3h2.5z\"\n}, \"2\"), /*#__PURE__*/_jsx(\"ellipse\", {\n  cx: \"20\",\n  cy: \"10\",\n  opacity: \".3\",\n  rx: \"1\",\n  ry: \"2\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 6c-1.68 0-3 1.76-3 4 0 1.77.83 3.22 2 3.76V20c0 .55.45 1 1 1s1-.45 1-1v-6.24c1.17-.54 2-1.99 2-3.76 0-2.24-1.32-4-3-4m0 6c-.41 0-1-.78-1-2s.59-2 1-2 1 .78 1 2-.59 2-1 2\"\n}, \"4\")], 'MedicationLiquidTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "jsxs", "_jsxs", "opacity", "children", "id", "d", "xlinkHref", "overflow", "cx", "cy", "rx", "ry"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/MedicationLiquidTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsxs(\"g\", {\n  opacity: \".3\",\n  children: [/*#__PURE__*/_jsx(\"defs\", {\n    children: /*#__PURE__*/_jsx(\"path\", {\n      id: \"a\",\n      d: \"M4 8h10v11H4z\",\n      opacity: \".3\"\n    })\n  }), /*#__PURE__*/_jsx(\"use\", {\n    xlinkHref: \"#a\",\n    overflow: \"visible\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    d: \"M4 19h10V8H4zm1-7h2.5V9.5h3V12H13v3h-2.5v2.5h-3V15H5z\"\n  })]\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 3h12v2H3zm11 3H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2m0 13H4V8h10z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.5 17.5h3V15H13v-3h-2.5V9.5h-3V12H5v3h2.5z\"\n}, \"2\"), /*#__PURE__*/_jsx(\"ellipse\", {\n  cx: \"20\",\n  cy: \"10\",\n  opacity: \".3\",\n  rx: \"1\",\n  ry: \"2\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 6c-1.68 0-3 1.76-3 4 0 1.77.83 3.22 2 3.76V20c0 .55.45 1 1 1s1-.45 1-1v-6.24c1.17-.54 2-1.99 2-3.76 0-2.24-1.32-4-3-4m0 6c-.41 0-1-.78-1-2s.59-2 1-2 1 .78 1 2-.59 2-1 2\"\n}, \"4\")], 'MedicationLiquidTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,eAAeJ,aAAa,CAAC,CAAC,aAAaI,KAAK,CAAC,GAAG,EAAE;EACpDC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,CAAC,aAAaJ,IAAI,CAAC,MAAM,EAAE;IACnCI,QAAQ,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;MAClCK,EAAE,EAAE,GAAG;MACPC,CAAC,EAAE,eAAe;MAClBH,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,EAAE,aAAaH,IAAI,CAAC,KAAK,EAAE;IAC3BO,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC,EAAE,aAAaR,IAAI,CAAC,MAAM,EAAE;IAC5BM,CAAC,EAAE;EACL,CAAC,CAAC;AACJ,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaN,IAAI,CAAC,MAAM,EAAE;EACjCM,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaN,IAAI,CAAC,MAAM,EAAE;EACjCM,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaN,IAAI,CAAC,SAAS,EAAE;EACpCS,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRP,OAAO,EAAE,IAAI;EACbQ,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE;AACN,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaZ,IAAI,CAAC,MAAM,EAAE;EACjCM,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}