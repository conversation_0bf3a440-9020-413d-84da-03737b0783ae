{"ast": null, "code": "/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\"animate\", \"exit\", \"variants\", \"initial\", \"style\", \"values\", \"variants\", \"transition\", \"transformTemplate\", \"transformValues\", \"custom\", \"inherit\", \"onBeforeLayoutMeasure\", \"onAnimationStart\", \"onAnimationComplete\", \"onUpdate\", \"onDragStart\", \"onDrag\", \"onDragEnd\", \"onMeasureDragConstraints\", \"onDirectionLock\", \"onDragTransitionEnd\", \"_dragX\", \"_dragY\", \"onHoverStart\", \"onHoverEnd\", \"onViewportEnter\", \"onViewportLeave\", \"globalTapTarget\", \"ignoreStrict\", \"viewport\"]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n  return key.startsWith(\"while\") || key.startsWith(\"drag\") && key !== \"draggable\" || key.startsWith(\"layout\") || key.startsWith(\"onTap\") || key.startsWith(\"onPan\") || key.startsWith(\"onLayout\") || validMotionProps.has(key);\n}\nexport { isValidMotionProp };", "map": {"version": 3, "names": ["validMotionProps", "Set", "isValidMotionProp", "key", "startsWith", "has"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs"], "sourcesContent": ["/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\n    \"animate\",\n    \"exit\",\n    \"variants\",\n    \"initial\",\n    \"style\",\n    \"values\",\n    \"variants\",\n    \"transition\",\n    \"transformTemplate\",\n    \"transformValues\",\n    \"custom\",\n    \"inherit\",\n    \"onBeforeLayoutMeasure\",\n    \"onAnimationStart\",\n    \"onAnimationComplete\",\n    \"onUpdate\",\n    \"onDragStart\",\n    \"onDrag\",\n    \"onDragEnd\",\n    \"onMeasureDragConstraints\",\n    \"onDirectionLock\",\n    \"onDragTransitionEnd\",\n    \"_dragX\",\n    \"_dragY\",\n    \"onHoverStart\",\n    \"onHoverEnd\",\n    \"onViewportEnter\",\n    \"onViewportLeave\",\n    \"globalTapTarget\",\n    \"ignoreStrict\",\n    \"viewport\",\n]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n    return (key.startsWith(\"while\") ||\n        (key.startsWith(\"drag\") && key !== \"draggable\") ||\n        key.startsWith(\"layout\") ||\n        key.startsWith(\"onTap\") ||\n        key.startsWith(\"onPan\") ||\n        key.startsWith(\"onLayout\") ||\n        validMotionProps.has(key));\n}\n\nexport { isValidMotionProp };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAC7B,SAAS,EACT,MAAM,EACN,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,mBAAmB,EACnB,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,uBAAuB,EACvB,kBAAkB,EAClB,qBAAqB,EACrB,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,0BAA0B,EAC1B,iBAAiB,EACjB,qBAAqB,EACrB,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,cAAc,EACd,UAAU,CACb,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,OAAQA,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAC1BD,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,IAAID,GAAG,KAAK,WAAY,IAC/CA,GAAG,CAACC,UAAU,CAAC,QAAQ,CAAC,IACxBD,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IACvBD,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IACvBD,GAAG,CAACC,UAAU,CAAC,UAAU,CAAC,IAC1BJ,gBAAgB,CAACK,GAAG,CAACF,GAAG,CAAC;AACjC;AAEA,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}