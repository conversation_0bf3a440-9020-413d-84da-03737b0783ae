{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ClipboardEdit = createLucideIcon(\"ClipboardEdit\", [[\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"8\",\n  y: \"2\",\n  rx: \"1\",\n  ry: \"1\",\n  key: \"tgr4d6\"\n}], [\"path\", {\n  d: \"M10.42 12.61a2.1 2.1 0 1 1 2.97 2.97L7.95 21 4 22l.99-3.95 5.43-5.44Z\",\n  key: \"1rgxu8\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5\",\n  key: \"cereej\"\n}], [\"path\", {\n  d: \"M4 13.5V6a2 2 0 0 1 2-2h2\",\n  key: \"5ua5vh\"\n}]]);\nexport { ClipboardEdit as default };", "map": {"version": 3, "names": ["ClipboardEdit", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\clipboard-edit.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ClipboardEdit\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSI0IiB4PSI4IiB5PSIyIiByeD0iMSIgcnk9IjEiIC8+CiAgPHBhdGggZD0iTTEwLjQyIDEyLjYxYTIuMSAyLjEgMCAxIDEgMi45NyAyLjk3TDcuOTUgMjEgNCAyMmwuOTktMy45NSA1LjQzLTUuNDRaIiAvPgogIDxwYXRoIGQ9Ik0xNiA0aDJhMiAyIDAgMCAxIDIgMnYxNGEyIDIgMCAwIDEtMiAyaC01LjUiIC8+CiAgPHBhdGggZD0iTTQgMTMuNVY2YTIgMiAwIDAgMSAyLTJoMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clipboard-edit\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ClipboardEdit = createLucideIcon('ClipboardEdit', [\n  ['rect', { width: '8', height: '4', x: '8', y: '2', rx: '1', ry: '1', key: 'tgr4d6' }],\n  [\n    'path',\n    { d: 'M10.42 12.61a2.1 2.1 0 1 1 2.97 2.97L7.95 21 4 22l.99-3.95 5.43-5.44Z', key: '1rgxu8' },\n  ],\n  ['path', { d: 'M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5', key: 'cereej' }],\n  ['path', { d: 'M4 13.5V6a2 2 0 0 1 2-2h2', key: '5ua5vh' }],\n]);\n\nexport default ClipboardEdit;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,MAAQ;EAAEC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrF,CACE,QACA;EAAEC,CAAA,EAAG,uEAAyE;EAAAD,GAAA,EAAK;AAAS,EAC9F,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,6CAA+C;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}