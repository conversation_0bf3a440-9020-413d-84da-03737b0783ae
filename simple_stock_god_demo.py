#!/usr/bin/env python3
"""
Simple Stock Market God Demo
Shows the new 6-point response format in action
"""

def demonstrate_stock_market_god_format():
    """Demonstrate the new 6-point Stock Market God response format"""
    
    print("🔮 A.T.L.A.S. Stock Market God - 6-Point Response Format Demo")
    print("=" * 60)
    print()
    
    # Example response for "Give me a trade idea for Apple"
    sample_response = """**A.T.L.A.S powered by Predicto - Stock Market God**

**1. Why This Trade?**
Apple just bounced off a key support level at $170 while institutional investors quietly added 2.3 million shares yesterday, signaling strong underlying demand

**2. Win/Loss Probabilities**
78% chance you hit the profit target, 22% chance you hit your stop

**3. Potential Money In or Out**
If you buy 100 shares at $175.25, you could make $750 or lose $420

**4. Smart Stop Plans**
We'll exit at $171.00 if price drops 2.4% below entry, protecting your capital with a trailing stop that adjusts as price moves up

**5. Market Context**
Tech stocks are strong today with semiconductor momentum driving the entire sector higher on AI optimism

**6. Confidence Score**
Confidence: 87%

⚡ **EXECUTION READY - Plan #A7B2C9D4**
Reply "confirm A7B2C9D4" to place live order"""

    print("📋 Sample Question: 'Give me a trade idea for Apple'")
    print()
    print("🎯 A.T.L.A.S. Response:")
    print(sample_response)
    print()
    print("✅ Format Validation:")
    print("  ✓ 1. Why This Trade? - Clear story about support level and institutional buying")
    print("  ✓ 2. Win/Loss Probabilities - Exact percentages (78%/22%)")
    print("  ✓ 3. Potential Money - Exact dollar amounts ($750 profit / $420 loss)")
    print("  ✓ 4. Smart Stop Plans - Specific protection strategy at $171.00")
    print("  ✓ 5. Market Context - One-sentence market snapshot")
    print("  ✓ 6. Confidence Score - Clear 87% confidence rating")
    print()
    print("🏆 PERFECT 6/6 - Stock Market God Format Complete!")
    print()
    
    # Show the test questions
    print("📝 Test Questions to Validate A.T.L.A.S.:")
    print("-" * 40)
    
    test_questions = [
        "What's Apple's price right now?",
        "Give me a trade idea for Tesla",
        "Help me make $200 this week",
        "Show me an options play for Netflix",
        "Find stocks with TTM Squeeze patterns",
        "What should I buy to make $500 by Friday?",
        "Analyze Amazon for trading opportunities",
        "Give me a quick market briefing",
        "Buy 100 shares of Microsoft for me",
        "What's the best cryptocurrency trade right now?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"{i:2d}. {question}")
    
    print()
    print("🎯 Success Criteria:")
    print("  • Each response must have all 6 sections")
    print("  • No 'I can't' or AI limitation language")
    print("  • Specific dollar amounts and percentages")
    print("  • Confident, data-driven tone")
    print("  • 'A.T.L.A.S powered by Predicto' branding")
    print()
    print("🚀 Ready to test! Copy these questions into A.T.L.A.S. chat.")

if __name__ == "__main__":
    demonstrate_stock_market_god_format()
