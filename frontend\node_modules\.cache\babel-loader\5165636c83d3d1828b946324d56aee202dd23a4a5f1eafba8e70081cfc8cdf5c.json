{"ast": null, "code": "export default function unsupportedProp(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propFullNameSafe = propFullName || propName;\n  if (typeof props[propName] !== 'undefined') {\n    return new Error(`The prop \\`${propFullNameSafe}\\` is not supported. Please remove it.`);\n  }\n  return null;\n}", "map": {"version": 3, "names": ["unsupportedProp", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "propFullNameSafe", "Error"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/utils/esm/unsupportedProp/unsupportedProp.js"], "sourcesContent": ["export default function unsupportedProp(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propFullNameSafe = propFullName || propName;\n  if (typeof props[propName] !== 'undefined') {\n    return new Error(`The prop \\`${propFullNameSafe}\\` is not supported. Please remove it.`);\n  }\n  return null;\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EAC9F,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,IAAI;EACb;EACA,MAAMC,gBAAgB,GAAGJ,YAAY,IAAIH,QAAQ;EACjD,IAAI,OAAOD,KAAK,CAACC,QAAQ,CAAC,KAAK,WAAW,EAAE;IAC1C,OAAO,IAAIQ,KAAK,CAAC,cAAcD,gBAAgB,wCAAwC,CAAC;EAC1F;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}