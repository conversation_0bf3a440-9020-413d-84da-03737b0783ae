#!/usr/bin/env python3
"""
A.T.L.A.S. Stock Market God Test Suite
20 beginner-style questions that validate all system capabilities
Each question expects the new 6-point response format
"""

import asyncio
import sys
import os
from datetime import datetime

# Add paths to find modules in organized folders
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '1_main_chat_engine'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '4_helper_tools'))

# Test questions covering all A.T.L.A.S. capabilities
STOCK_MARKET_GOD_TEST_QUESTIONS = [
    # Real-time Market Data (Questions 1-3)
    {
        "id": 1,
        "question": "What's Apple's price right now?",
        "category": "Real-time Data",
        "expected_format": "6-point Stock Market God format with current AAPL price"
    },
    {
        "id": 2,
        "question": "How is Tesla doing today?",
        "category": "Real-time Data", 
        "expected_format": "6-point format with TSLA analysis and trade recommendation"
    },
    {
        "id": 3,
        "question": "Show me Microsoft's current situation",
        "category": "Real-time Data",
        "expected_format": "6-point format with MSFT market context and confidence score"
    },

    # Trade Suggestions (Questions 4-6)
    {
        "id": 4,
        "question": "Give me a trade idea for Nvidia",
        "category": "Trade Suggestions",
        "expected_format": "6-point format with NVDA trade rationale, probabilities, dollar amounts"
    },
    {
        "id": 5,
        "question": "What should I buy to make $200 this week?",
        "category": "Goal-based Trading",
        "expected_format": "6-point format with specific stock, exact dollar calculations for $200 profit"
    },
    {
        "id": 6,
        "question": "Suggest a good stock trade for today",
        "category": "Trade Suggestions",
        "expected_format": "6-point format with best opportunity, stop plans, market context"
    },

    # Pattern Detection & Analysis (Questions 7-9)
    {
        "id": 7,
        "question": "Find stocks with TTM Squeeze patterns",
        "category": "Pattern Detection",
        "expected_format": "6-point format with TTM Squeeze stock, technical reasoning, confidence"
    },
    {
        "id": 8,
        "question": "Analyze Amazon for trading opportunities",
        "category": "Technical Analysis",
        "expected_format": "6-point format with AMZN analysis, win/loss probabilities, smart stops"
    },
    {
        "id": 9,
        "question": "What patterns do you see in Google stock?",
        "category": "Pattern Detection",
        "expected_format": "6-point format with GOOGL pattern analysis and trade setup"
    },

    # Options Trading (Questions 10-11)
    {
        "id": 10,
        "question": "Show me an options play for Netflix",
        "category": "Options Trading",
        "expected_format": "6-point format with NFLX options strategy, exact dollar amounts"
    },
    {
        "id": 11,
        "question": "What's a good call option to buy this week?",
        "category": "Options Trading", 
        "expected_format": "6-point format with specific call option, probabilities, protection plan"
    },

    # Portfolio Management (Questions 12-13)
    {
        "id": 12,
        "question": "How should I optimize my portfolio?",
        "category": "Portfolio Management",
        "expected_format": "6-point format with optimization strategy, dollar impact, confidence"
    },
    {
        "id": 13,
        "question": "What's my portfolio risk level?",
        "category": "Risk Management",
        "expected_format": "6-point format with risk assessment, protection strategies, market context"
    },

    # Market Analysis & News (Questions 14-16)
    {
        "id": 14,
        "question": "Why is the market moving today?",
        "category": "Market Analysis",
        "expected_format": "6-point format with market drivers, trading opportunity, confidence score"
    },
    {
        "id": 15,
        "question": "Give me a quick market briefing",
        "category": "Market Briefing",
        "expected_format": "6-point format with market overview, best trade idea, exact dollar amounts"
    },
    {
        "id": 16,
        "question": "What news is affecting Shopify?",
        "category": "News Analysis",
        "expected_format": "6-point format with SHOP news impact, trade rationale, probabilities"
    },

    # Trade Execution & Alerts (Questions 17-18)
    {
        "id": 17,
        "question": "Buy 100 shares of AMD for me",
        "category": "Trade Execution",
        "expected_format": "6-point format with AMD trade plan, confirmation required, exact costs"
    },
    {
        "id": 18,
        "question": "Alert me when SPY hits $450",
        "category": "Alerts & Monitoring",
        "expected_format": "6-point format with alert setup, trading strategy at $450, confidence"
    },

    # Advanced Features (Questions 19-20)
    {
        "id": 19,
        "question": "Help me make $500 by Friday with minimal risk",
        "category": "Goal-based Strategy",
        "expected_format": "6-point format with low-risk strategy, exact $500 calculation, smart stops"
    },
    {
        "id": 20,
        "question": "What's the best cryptocurrency trade right now?",
        "category": "Crypto Trading",
        "expected_format": "6-point format with crypto recommendation, dollar amounts, market context"
    }
]

def validate_6_point_format(response_text):
    """Validate that response follows the 6-point Stock Market God format"""
    required_sections = [
        "Why This Trade",
        "Win/Loss Probabilities", 
        "Potential Money",
        "Smart Stop Plans",
        "Market Context",
        "Confidence"
    ]
    
    score = 0
    missing_sections = []
    
    for section in required_sections:
        if section.lower() in response_text.lower():
            score += 1
        else:
            missing_sections.append(section)
    
    return {
        "score": score,
        "max_score": len(required_sections),
        "percentage": (score / len(required_sections)) * 100,
        "missing_sections": missing_sections,
        "is_valid": score >= 5  # Allow 5/6 for flexibility
    }

async def run_stock_market_god_tests():
    """Run all 20 test questions and validate responses"""
    print("🔮 A.T.L.A.S. Stock Market God Test Suite")
    print("=" * 50)
    print(f"Testing {len(STOCK_MARKET_GOD_TEST_QUESTIONS)} capabilities with 6-point format validation")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Import the main engine
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        from atlas_orchestrator import ATLASOrchestrator
        
        # Initialize engines
        predicto = PredictoConversationalEngine()
        orchestrator = ATLASOrchestrator()
        
        await predicto.initialize()
        await orchestrator.initialize()
        
        # Switch to guru mode for Stock Market God responses
        predicto.switch_persona("guru")
        
        print("✅ A.T.L.A.S. engines initialized successfully")
        print()
        
    except Exception as e:
        print(f"❌ Failed to initialize A.T.L.A.S. engines: {e}")
        return
    
    # Run tests
    passed_tests = 0
    total_format_score = 0
    
    for test in STOCK_MARKET_GOD_TEST_QUESTIONS:
        print(f"🧪 Test {test['id']}: {test['category']}")
        print(f"❓ Question: {test['question']}")
        
        try:
            # Get response from A.T.L.A.S.
            response = await predicto.process_conversation(
                test['question'], 
                session_id=f"test_{test['id']}", 
                orchestrator=orchestrator
            )
            
            # Validate 6-point format
            format_validation = validate_6_point_format(response.response)
            total_format_score += format_validation['score']
            
            print(f"📊 Format Score: {format_validation['score']}/6 ({format_validation['percentage']:.1f}%)")
            
            if format_validation['missing_sections']:
                print(f"⚠️  Missing: {', '.join(format_validation['missing_sections'])}")
            
            if format_validation['is_valid']:
                print("✅ PASSED - Valid Stock Market God format")
                passed_tests += 1
            else:
                print("❌ FAILED - Invalid format")
            
            print(f"🎯 Response Preview: {response.response[:100]}...")
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
            print("-" * 50)
    
    # Final Results
    print("\n🏆 FINAL RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {passed_tests}/{len(STOCK_MARKET_GOD_TEST_QUESTIONS)} ({(passed_tests/len(STOCK_MARKET_GOD_TEST_QUESTIONS)*100):.1f}%)")
    print(f"Average Format Score: {total_format_score/(len(STOCK_MARKET_GOD_TEST_QUESTIONS)*6)*100:.1f}%")
    
    if passed_tests == len(STOCK_MARKET_GOD_TEST_QUESTIONS):
        print("🎉 ALL TESTS PASSED - A.T.L.A.S. is ready as Stock Market God!")
    else:
        print(f"⚠️  {len(STOCK_MARKET_GOD_TEST_QUESTIONS) - passed_tests} tests need attention")
    
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(run_stock_market_god_tests())
