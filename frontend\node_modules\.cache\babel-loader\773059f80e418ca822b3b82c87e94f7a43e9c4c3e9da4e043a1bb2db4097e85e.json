{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"disableSpacing\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getAccordionActionsUtilityClass } from './accordionActionsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getAccordionActionsUtilityClass, classes);\n};\nconst AccordionActionsRoot = styled('div', {\n  name: 'MuiAccordionActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  justifyContent: 'flex-end',\n  variants: [{\n    props: props => !props.disableSpacing,\n    style: {\n      '& > :not(style) ~ :not(style)': {\n        marginLeft: 8\n      }\n    }\n  }]\n});\nconst AccordionActions = /*#__PURE__*/React.forwardRef(function AccordionActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionActions'\n  });\n  const {\n      className,\n      disableSpacing = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableSpacing\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AccordionActionsRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionActions;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getAccordionActionsUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "disableSpacing", "slots", "root", "AccordionActionsRoot", "name", "slot", "overridesResolver", "props", "styles", "spacing", "display", "alignItems", "padding", "justifyContent", "variants", "style", "marginLeft", "AccordionActions", "forwardRef", "inProps", "ref", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/AccordionActions/AccordionActions.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"disableSpacing\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getAccordionActionsUtilityClass } from './accordionActionsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getAccordionActionsUtilityClass, classes);\n};\nconst AccordionActionsRoot = styled('div', {\n  name: 'MuiAccordionActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  justifyContent: 'flex-end',\n  variants: [{\n    props: props => !props.disableSpacing,\n    style: {\n      '& > :not(style) ~ :not(style)': {\n        marginLeft: 8\n      }\n    }\n  }]\n});\nconst AccordionActions = /*#__PURE__*/React.forwardRef(function AccordionActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionActions'\n  });\n  const {\n      className,\n      disableSpacing = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableSpacing\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AccordionActionsRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionActions;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,+BAA+B,QAAQ,2BAA2B;AAC3E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACF,cAAc,IAAI,SAAS;EAC7C,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAEP,+BAA+B,EAAEK,OAAO,CAAC;AACxE,CAAC;AACD,MAAMI,oBAAoB,GAAGX,MAAM,CAAC,KAAK,EAAE;EACzCY,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAE,CAACJ,UAAU,CAACE,cAAc,IAAIQ,MAAM,CAACC,OAAO,CAAC;EACpE;AACF,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,CAAC;EACVC,cAAc,EAAE,UAAU;EAC1BC,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAEA,KAAK,IAAI,CAACA,KAAK,CAACP,cAAc;IACrCe,KAAK,EAAE;MACL,+BAA+B,EAAE;QAC/BC,UAAU,EAAE;MACd;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAG,aAAa7B,KAAK,CAAC8B,UAAU,CAAC,SAASD,gBAAgBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7F,MAAMb,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAEY,OAAO;IACdf,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFiB,SAAS;MACTrB,cAAc,GAAG;IACnB,CAAC,GAAGO,KAAK;IACTe,KAAK,GAAGpC,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAMW,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCP;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,oBAAoB,EAAElB,QAAQ,CAAC;IACtDoC,SAAS,EAAE/B,IAAI,CAACS,OAAO,CAACG,IAAI,EAAEmB,SAAS,CAAC;IACxCD,GAAG,EAAEA,GAAG;IACRtB,UAAU,EAAEA;EACd,CAAC,EAAEwB,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,gBAAgB,CAACS,SAAS,CAAC,yBAAyB;EAC1F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEtC,SAAS,CAACuC,IAAI;EACxB;AACF;AACA;EACE7B,OAAO,EAAEV,SAAS,CAACwC,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAEhC,SAAS,CAACyC,MAAM;EAC3B;AACF;AACA;AACA;EACE9B,cAAc,EAAEX,SAAS,CAAC0C,IAAI;EAC9B;AACF;AACA;EACEC,EAAE,EAAE3C,SAAS,CAAC4C,SAAS,CAAC,CAAC5C,SAAS,CAAC6C,OAAO,CAAC7C,SAAS,CAAC4C,SAAS,CAAC,CAAC5C,SAAS,CAAC8C,IAAI,EAAE9C,SAAS,CAACwC,MAAM,EAAExC,SAAS,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE1C,SAAS,CAAC8C,IAAI,EAAE9C,SAAS,CAACwC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}