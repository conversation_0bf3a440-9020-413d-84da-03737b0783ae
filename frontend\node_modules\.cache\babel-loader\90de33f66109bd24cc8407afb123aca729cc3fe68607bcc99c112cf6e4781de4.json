{"ast": null, "code": "const createAxisDelta = () => ({\n  translate: 0,\n  scale: 1,\n  origin: 0,\n  originPoint: 0\n});\nconst createDelta = () => ({\n  x: createAxisDelta(),\n  y: createAxisDelta()\n});\nconst createAxis = () => ({\n  min: 0,\n  max: 0\n});\nconst createBox = () => ({\n  x: createAxis(),\n  y: createAxis()\n});\nexport { createAxis, createAxisDelta, createBox, createDelta };", "map": {"version": 3, "names": ["createAxisDel<PERSON>", "translate", "scale", "origin", "originPoint", "create<PERSON><PERSON><PERSON>", "x", "y", "createAxis", "min", "max", "createBox"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/projection/geometry/models.mjs"], "sourcesContent": ["const createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\nexport { createAxis, createAxisDelta, createBox, createDelta };\n"], "mappings": "AAAA,MAAMA,eAAe,GAAGA,CAAA,MAAO;EAC3BC,SAAS,EAAE,CAAC;EACZC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,WAAW,EAAE;AACjB,CAAC,CAAC;AACF,MAAMC,WAAW,GAAGA,CAAA,MAAO;EACvBC,CAAC,EAAEN,eAAe,CAAC,CAAC;EACpBO,CAAC,EAAEP,eAAe,CAAC;AACvB,CAAC,CAAC;AACF,MAAMQ,UAAU,GAAGA,CAAA,MAAO;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE;AAAE,CAAC,CAAC;AAC7C,MAAMC,SAAS,GAAGA,CAAA,MAAO;EACrBL,CAAC,EAAEE,UAAU,CAAC,CAAC;EACfD,CAAC,EAAEC,UAAU,CAAC;AAClB,CAAC,CAAC;AAEF,SAASA,UAAU,EAAER,eAAe,EAAEW,SAAS,EAAEN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}