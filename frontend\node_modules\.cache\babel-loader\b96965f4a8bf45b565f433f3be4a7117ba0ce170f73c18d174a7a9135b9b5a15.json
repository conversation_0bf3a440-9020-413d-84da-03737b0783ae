{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ImageDown = createLucideIcon(\"ImageDown\", [[\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}], [\"path\", {\n  d: \"M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10.8\",\n  key: \"sqts6f\"\n}], [\"path\", {\n  d: \"m21 15-3.1-3.1a2 2 0 0 0-2.814.014L6 21\",\n  key: \"1h47z9\"\n}], [\"path\", {\n  d: \"m14 19.5 3 3v-6\",\n  key: \"1x9jmo\"\n}], [\"path\", {\n  d: \"m17 22.5 3-3\",\n  key: \"xzuz0n\"\n}]]);\nexport { ImageDown as default };", "map": {"version": 3, "names": ["ImageDown", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\image-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ImageDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iOSIgcj0iMiIgLz4KICA8cGF0aCBkPSJNMTAuMyAyMUg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ2MTAuOCIgLz4KICA8cGF0aCBkPSJtMjEgMTUtMy4xLTMuMWEyIDIgMCAwIDAtMi44MTQuMDE0TDYgMjEiIC8+CiAgPHBhdGggZD0ibTE0IDE5LjUgMyAzdi02IiAvPgogIDxwYXRoIGQ9Im0xNyAyMi41IDMtMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/image-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ImageDown = createLucideIcon('ImageDown', [\n  ['circle', { cx: '9', cy: '9', r: '2', key: 'af1f0g' }],\n  ['path', { d: 'M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10.8', key: 'sqts6f' }],\n  ['path', { d: 'm21 15-3.1-3.1a2 2 0 0 0-2.814.014L6 21', key: '1h47z9' }],\n  ['path', { d: 'm14 19.5 3 3v-6', key: '1x9jmo' }],\n  ['path', { d: 'm17 22.5 3-3', key: 'xzuz0n' }],\n]);\n\nexport default ImageDown;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gEAAkE;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/F,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}