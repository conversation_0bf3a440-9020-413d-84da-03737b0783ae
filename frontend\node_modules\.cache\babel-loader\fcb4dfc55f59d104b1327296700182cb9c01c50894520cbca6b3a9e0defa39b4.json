{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\AtlasInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { createChart } from 'lightweight-charts';\nimport { Send, TrendingUp, BarChart3, Sparkles } from 'lucide-react';\nimport SpaceBackground from './SpaceBackground';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AtlasInterface = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: 'system',\n    content: \"Certainly! Here is the latest stock quote for AAPL:\",\n    timestamp: new Date()\n  }, {\n    id: 2,\n    type: 'stock-quote',\n    symbol: 'AAPL',\n    price: 149.36,\n    change: 1.32,\n    changePercent: 1.32,\n    company: 'Apple Inc.',\n    chartData: null,\n    // Will be generated when chart initializes\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const chartRef = useRef(null);\n  const chartInstanceRef = useRef(null);\n\n  // Initialize chart when stock quote message is rendered\n  useEffect(() => {\n    const stockMessage = messages.find(msg => msg.type === 'stock-quote');\n    if (stockMessage && chartRef.current && !chartInstanceRef.current) {\n      const chartData = stockMessage.chartData || generateMockChartData();\n      initializeChart(chartData);\n    }\n  }, [messages]);\n  const initializeChart = data => {\n    if (chartInstanceRef.current) {\n      chartInstanceRef.current.remove();\n    }\n    const chart = createChart(chartRef.current, {\n      width: chartRef.current.clientWidth || 260,\n      height: 100,\n      layout: {\n        background: {\n          color: 'transparent'\n        },\n        textColor: '#67e8f9',\n        fontSize: 10\n      },\n      grid: {\n        vertLines: {\n          color: 'rgba(6, 182, 212, 0.05)'\n        },\n        horzLines: {\n          color: 'rgba(6, 182, 212, 0.05)'\n        }\n      },\n      crosshair: {\n        mode: 0\n      },\n      rightPriceScale: {\n        borderColor: 'rgba(6, 182, 212, 0.2)',\n        textColor: '#67e8f9',\n        visible: false\n      },\n      timeScale: {\n        borderColor: 'rgba(6, 182, 212, 0.2)',\n        textColor: '#67e8f9',\n        timeVisible: false,\n        secondsVisible: false,\n        visible: false\n      },\n      handleScroll: false,\n      handleScale: false\n    });\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#10b981',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#10b981',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#10b981'\n    });\n    candlestickSeries.setData(data);\n    chartInstanceRef.current = chart;\n\n    // Auto-fit content\n    chart.timeScale().fitContent();\n\n    // Handle resize\n    const resizeObserver = new ResizeObserver(entries => {\n      if (entries.length === 0 || entries[0].target !== chartRef.current) return;\n      const {\n        width\n      } = entries[0].contentRect;\n      chart.applyOptions({\n        width\n      });\n    });\n    if (chartRef.current) {\n      resizeObserver.observe(chartRef.current);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n    const newMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, newMessage]);\n    const messageToSend = inputMessage;\n    setInputMessage('');\n    setIsTyping(true);\n    try {\n      // Call the real Holly AI backend\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'atlas_session',\n            interface: 'atlas'\n          }\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      setIsTyping(false);\n\n      // Create Holly's response message\n      const hollyResponse = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: data.response || \"I'm having trouble processing that request right now.\",\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n      setMessages(prev => [...prev, hollyResponse]);\n\n      // If Holly provided a stock quote, add it as a separate message\n      if (data.type === 'stock_quote' && data.trading_plan) {\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: data.trading_plan.symbol || 'AAPL',\n          price: data.trading_plan.current_price || 149.36,\n          change: data.trading_plan.price_change || 1.32,\n          changePercent: data.trading_plan.price_change_percent || 1.32,\n          company: data.trading_plan.company_name || 'Apple Inc.',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      } else if (messageToSend.toLowerCase().includes('aapl') || messageToSend.toLowerCase().includes('apple')) {\n        // Show AAPL quote for demo purposes to match reference image\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: 'AAPL',\n          price: 149.36,\n          change: 1.32,\n          changePercent: 1.32,\n          company: 'Apple Inc.',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      }\n    } catch (error) {\n      console.error('Error calling Holly AI:', error);\n      setIsTyping(false);\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: \"I'm having trouble connecting to my AI brain right now. Please try again in a moment.\",\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen space-bg relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(SpaceBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex items-center justify-center min-h-screen p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"w-full max-w-sm mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          className: \"atlas-card relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-6 pt-6 px-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1\",\n              children: \"A.T.L.A.S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-cyan-300/70 text-sm\",\n              children: \"Stock Analysis Chatbot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 pb-6 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -10\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: [message.type === 'system' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-cyan-100/90 text-sm leading-relaxed\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this), message.type === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm max-w-xs\",\n                    children: message.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this), message.type === 'stock-quote' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stock-quote-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start justify-between mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xl font-bold text-white mb-1\",\n                        children: message.symbol\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-cyan-300/70 text-xs\",\n                        children: message.company\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: message.price\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-end text-xs text-green-400\",\n                        children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                          className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 281,\n                          columnNumber: 31\n                        }, this), \"+\", message.changePercent, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"chart-container mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      ref: chartRef,\n                      className: \"w-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn\",\n                      children: \"Show earnings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"action-btn\",\n                      children: \"Analyze trend\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this)]\n              }, message.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -10\n                },\n                className: \"flex items-center space-x-2 text-cyan-400\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.1s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"A.T.L.A.S is typing...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 pb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: inputMessage,\n                onChange: e => setInputMessage(e.target.value),\n                onKeyPress: handleKeyPress,\n                placeholder: \"Send a message...\",\n                className: \"atlas-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleSendMessage,\n                className: \"atlas-send-btn\",\n                children: /*#__PURE__*/_jsxDEV(Send, {\n                  className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n};\n\n// Generate mock chart data with upward trend like AAPL\n_s(AtlasInterface, \"0R+bGVhDffAL8A2UZ6rUCHa74bM=\");\n_c = AtlasInterface;\nfunction generateMockChartData() {\n  const data = [];\n  let basePrice = 145;\n  const endPrice = 149.36;\n  const dataPoints = 30;\n  for (let i = 0; i < dataPoints; i++) {\n    const time = Math.floor(Date.now() / 1000) - (dataPoints - i) * 1800; // 30-minute intervals\n    const progress = i / (dataPoints - 1);\n\n    // Create upward trend with some volatility\n    const trendPrice = basePrice + (endPrice - basePrice) * progress;\n    const volatility = 0.008; // Reduced volatility for smoother trend\n    const randomChange = (Math.random() - 0.5) * volatility * trendPrice;\n    const open = i === 0 ? basePrice : data[i - 1].close;\n    const close = trendPrice + randomChange;\n    const high = Math.max(open, close) + Math.random() * 0.3;\n    const low = Math.min(open, close) - Math.random() * 0.2;\n    data.push({\n      time,\n      open: parseFloat(open.toFixed(2)),\n      high: parseFloat(high.toFixed(2)),\n      low: parseFloat(low.toFixed(2)),\n      close: parseFloat(close.toFixed(2))\n    });\n  }\n  return data;\n}\nexport default AtlasInterface;\nvar _c;\n$RefreshReg$(_c, \"AtlasInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "createChart", "Send", "TrendingUp", "BarChart3", "<PERSON><PERSON><PERSON>", "SpaceBackground", "jsxDEV", "_jsxDEV", "AtlasInterface", "_s", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "symbol", "price", "change", "changePercent", "company", "chartData", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "chartRef", "chartInstanceRef", "stockMessage", "find", "msg", "current", "generateMockChartData", "initializeChart", "data", "remove", "chart", "width", "clientWidth", "height", "layout", "background", "color", "textColor", "fontSize", "grid", "vertLines", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "visible", "timeScale", "timeVisible", "secondsVisible", "handleScroll", "handleScale", "candlestickSeries", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "setData", "<PERSON><PERSON><PERSON><PERSON>", "resizeObserver", "ResizeObserver", "entries", "length", "target", "contentRect", "applyOptions", "observe", "handleSendMessage", "trim", "newMessage", "now", "prev", "messageToSend", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "message", "user_context", "toISOString", "session_id", "interface", "ok", "Error", "status", "json", "hollyResponse", "response_type", "requires_action", "trading_plan", "plan_id", "function_called", "current_price", "price_change", "price_change_percent", "company_name", "toLowerCase", "includes", "error", "console", "errorMessage", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "delay", "map", "exit", "ref", "style", "animationDelay", "value", "onChange", "onKeyPress", "placeholder", "onClick", "_c", "basePrice", "endPrice", "dataPoints", "i", "time", "Math", "floor", "progress", "trendPrice", "volatility", "randomChange", "random", "open", "close", "high", "max", "low", "min", "push", "parseFloat", "toFixed", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/AtlasInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { createChart } from 'lightweight-charts';\nimport { Send, TrendingUp, BarChart3, Sparkles } from 'lucide-react';\nimport SpaceBackground from './SpaceBackground';\n\nconst AtlasInterface = () => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: 'system',\n      content: \"Certainly! Here is the latest stock quote for AAPL:\",\n      timestamp: new Date()\n    },\n    {\n      id: 2,\n      type: 'stock-quote',\n      symbol: 'AAPL',\n      price: 149.36,\n      change: 1.32,\n      changePercent: 1.32,\n      company: 'Apple Inc.',\n      chartData: null, // Will be generated when chart initializes\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const chartRef = useRef(null);\n  const chartInstanceRef = useRef(null);\n\n  // Initialize chart when stock quote message is rendered\n  useEffect(() => {\n    const stockMessage = messages.find(msg => msg.type === 'stock-quote');\n    if (stockMessage && chartRef.current && !chartInstanceRef.current) {\n      const chartData = stockMessage.chartData || generateMockChartData();\n      initializeChart(chartData);\n    }\n  }, [messages]);\n\n  const initializeChart = (data) => {\n    if (chartInstanceRef.current) {\n      chartInstanceRef.current.remove();\n    }\n\n    const chart = createChart(chartRef.current, {\n      width: chartRef.current.clientWidth || 260,\n      height: 100,\n      layout: {\n        background: { color: 'transparent' },\n        textColor: '#67e8f9',\n        fontSize: 10,\n      },\n      grid: {\n        vertLines: { color: 'rgba(6, 182, 212, 0.05)' },\n        horzLines: { color: 'rgba(6, 182, 212, 0.05)' },\n      },\n      crosshair: {\n        mode: 0,\n      },\n      rightPriceScale: {\n        borderColor: 'rgba(6, 182, 212, 0.2)',\n        textColor: '#67e8f9',\n        visible: false,\n      },\n      timeScale: {\n        borderColor: 'rgba(6, 182, 212, 0.2)',\n        textColor: '#67e8f9',\n        timeVisible: false,\n        secondsVisible: false,\n        visible: false,\n      },\n      handleScroll: false,\n      handleScale: false,\n    });\n\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#10b981',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#10b981',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#10b981',\n    });\n\n    candlestickSeries.setData(data);\n    chartInstanceRef.current = chart;\n\n    // Auto-fit content\n    chart.timeScale().fitContent();\n\n    // Handle resize\n    const resizeObserver = new ResizeObserver(entries => {\n      if (entries.length === 0 || entries[0].target !== chartRef.current) return;\n      const { width } = entries[0].contentRect;\n      chart.applyOptions({ width });\n    });\n\n    if (chartRef.current) {\n      resizeObserver.observe(chartRef.current);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const newMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    const messageToSend = inputMessage;\n    setInputMessage('');\n    setIsTyping(true);\n\n    try {\n      // Call the real Holly AI backend\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'atlas_session',\n            interface: 'atlas'\n          }\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      setIsTyping(false);\n\n      // Create Holly's response message\n      const hollyResponse = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: data.response || \"I'm having trouble processing that request right now.\",\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n\n      setMessages(prev => [...prev, hollyResponse]);\n\n      // If Holly provided a stock quote, add it as a separate message\n      if (data.type === 'stock_quote' && data.trading_plan) {\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: data.trading_plan.symbol || 'AAPL',\n          price: data.trading_plan.current_price || 149.36,\n          change: data.trading_plan.price_change || 1.32,\n          changePercent: data.trading_plan.price_change_percent || 1.32,\n          company: data.trading_plan.company_name || 'Apple Inc.',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      } else if (messageToSend.toLowerCase().includes('aapl') || messageToSend.toLowerCase().includes('apple')) {\n        // Show AAPL quote for demo purposes to match reference image\n        const stockMessage = {\n          id: Date.now() + 2,\n          type: 'stock-quote',\n          symbol: 'AAPL',\n          price: 149.36,\n          change: 1.32,\n          changePercent: 1.32,\n          company: 'Apple Inc.',\n          chartData: generateMockChartData(),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, stockMessage]);\n      }\n\n    } catch (error) {\n      console.error('Error calling Holly AI:', error);\n      setIsTyping(false);\n\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'system',\n        content: \"I'm having trouble connecting to my AI brain right now. Please try again in a moment.\",\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen space-bg relative overflow-hidden\">\n      <SpaceBackground />\n\n      {/* Main Container */}\n      <div className=\"relative z-10 flex items-center justify-center min-h-screen p-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"w-full max-w-sm mx-auto\"\n        >\n          {/* Main Chat Card */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"atlas-card relative\"\n          >\n            {/* Header */}\n            <div className=\"text-center mb-6 pt-6 px-6\">\n              <h1 className=\"text-2xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1\">\n                A.T.L.A.S\n              </h1>\n              <p className=\"text-cyan-300/70 text-sm\">\n                Stock Analysis Chatbot\n              </p>\n            </div>\n\n            {/* Messages Container */}\n            <div className=\"px-6 pb-6 space-y-4\"\n          >\n              <AnimatePresence>\n                {messages.map((message) => (\n                  <motion.div\n                    key={message.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    {message.type === 'system' && (\n                      <div className=\"text-cyan-100/90 text-sm leading-relaxed\">\n                        {message.content}\n                      </div>\n                    )}\n\n                    {message.type === 'user' && (\n                      <div className=\"text-right\">\n                        <div className=\"inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm max-w-xs\">\n                          {message.content}\n                        </div>\n                      </div>\n                    )}\n\n                    {message.type === 'stock-quote' && (\n                      <div className=\"stock-quote-card\">\n                        {/* Stock Header */}\n                        <div className=\"flex items-start justify-between mb-4\">\n                          <div>\n                            <div className=\"text-xl font-bold text-white mb-1\">\n                              {message.symbol}\n                            </div>\n                            <div className=\"text-cyan-300/70 text-xs\">\n                              {message.company}\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-xl font-bold text-white\">\n                              {message.price}\n                            </div>\n                            <div className=\"flex items-center justify-end text-xs text-green-400\">\n                              <TrendingUp className=\"w-3 h-3 mr-1\" />\n                              +{message.changePercent}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Chart */}\n                        <div className=\"chart-container mb-4\">\n                          <div ref={chartRef} className=\"w-full\" />\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex gap-2\">\n                          <button className=\"action-btn\">\n                            Show earnings\n                          </button>\n                          <button className=\"action-btn\">\n                            Analyze trend\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n\n              {/* Typing Indicator */}\n              <AnimatePresence>\n                {isTyping && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"flex items-center space-x-2 text-cyan-400\"\n                  >\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                      <div className=\"w-2 h-2 bg-cyan-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                    </div>\n                    <span className=\"text-sm\">A.T.L.A.S is typing...</span>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Input Area */}\n            <div className=\"px-6 pb-6\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  value={inputMessage}\n                  onChange={(e) => setInputMessage(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  placeholder=\"Send a message...\"\n                  className=\"atlas-input\"\n                />\n                <button\n                  onClick={handleSendMessage}\n                  className=\"atlas-send-btn\"\n                >\n                  <Send className=\"w-4 h-4 text-white\" />\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\n// Generate mock chart data with upward trend like AAPL\nfunction generateMockChartData() {\n  const data = [];\n  let basePrice = 145;\n  const endPrice = 149.36;\n  const dataPoints = 30;\n\n  for (let i = 0; i < dataPoints; i++) {\n    const time = Math.floor(Date.now() / 1000) - (dataPoints - i) * 1800; // 30-minute intervals\n    const progress = i / (dataPoints - 1);\n\n    // Create upward trend with some volatility\n    const trendPrice = basePrice + (endPrice - basePrice) * progress;\n    const volatility = 0.008; // Reduced volatility for smoother trend\n    const randomChange = (Math.random() - 0.5) * volatility * trendPrice;\n\n    const open = i === 0 ? basePrice : data[i - 1].close;\n    const close = trendPrice + randomChange;\n    const high = Math.max(open, close) + Math.random() * 0.3;\n    const low = Math.min(open, close) - Math.random() * 0.2;\n\n    data.push({\n      time,\n      open: parseFloat(open.toFixed(2)),\n      high: parseFloat(high.toFixed(2)),\n      low: parseFloat(low.toFixed(2)),\n      close: parseFloat(close.toFixed(2)),\n    });\n  }\n\n  return data;\n}\n\nexport default AtlasInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,cAAc;AACpE,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CACvC;IACEiB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,qDAAqD;IAC9DC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBI,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,IAAI;IAAE;IACjBP,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMgC,QAAQ,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM+B,gBAAgB,GAAG/B,MAAM,CAAC,IAAI,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMiC,YAAY,GAAGnB,QAAQ,CAACoB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAClB,IAAI,KAAK,aAAa,CAAC;IACrE,IAAIgB,YAAY,IAAIF,QAAQ,CAACK,OAAO,IAAI,CAACJ,gBAAgB,CAACI,OAAO,EAAE;MACjE,MAAMV,SAAS,GAAGO,YAAY,CAACP,SAAS,IAAIW,qBAAqB,CAAC,CAAC;MACnEC,eAAe,CAACZ,SAAS,CAAC;IAC5B;EACF,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMwB,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIP,gBAAgB,CAACI,OAAO,EAAE;MAC5BJ,gBAAgB,CAACI,OAAO,CAACI,MAAM,CAAC,CAAC;IACnC;IAEA,MAAMC,KAAK,GAAGrC,WAAW,CAAC2B,QAAQ,CAACK,OAAO,EAAE;MAC1CM,KAAK,EAAEX,QAAQ,CAACK,OAAO,CAACO,WAAW,IAAI,GAAG;MAC1CC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE;QACNC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAc,CAAC;QACpCC,SAAS,EAAE,SAAS;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAE;QACJC,SAAS,EAAE;UAAEJ,KAAK,EAAE;QAA0B,CAAC;QAC/CK,SAAS,EAAE;UAAEL,KAAK,EAAE;QAA0B;MAChD,CAAC;MACDM,SAAS,EAAE;QACTC,IAAI,EAAE;MACR,CAAC;MACDC,eAAe,EAAE;QACfC,WAAW,EAAE,wBAAwB;QACrCR,SAAS,EAAE,SAAS;QACpBS,OAAO,EAAE;MACX,CAAC;MACDC,SAAS,EAAE;QACTF,WAAW,EAAE,wBAAwB;QACrCR,SAAS,EAAE,SAAS;QACpBW,WAAW,EAAE,KAAK;QAClBC,cAAc,EAAE,KAAK;QACrBH,OAAO,EAAE;MACX,CAAC;MACDI,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEF,MAAMC,iBAAiB,GAAGtB,KAAK,CAACuB,oBAAoB,CAAC;MACnDC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,eAAe,EAAE,SAAS;MAC1BC,aAAa,EAAE,SAAS;MACxBC,aAAa,EAAE,SAAS;MACxBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFP,iBAAiB,CAACQ,OAAO,CAAChC,IAAI,CAAC;IAC/BP,gBAAgB,CAACI,OAAO,GAAGK,KAAK;;IAEhC;IACAA,KAAK,CAACiB,SAAS,CAAC,CAAC,CAACc,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAMC,cAAc,GAAG,IAAIC,cAAc,CAACC,OAAO,IAAI;MACnD,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,IAAID,OAAO,CAAC,CAAC,CAAC,CAACE,MAAM,KAAK9C,QAAQ,CAACK,OAAO,EAAE;MACpE,MAAM;QAAEM;MAAM,CAAC,GAAGiC,OAAO,CAAC,CAAC,CAAC,CAACG,WAAW;MACxCrC,KAAK,CAACsC,YAAY,CAAC;QAAErC;MAAM,CAAC,CAAC;IAC/B,CAAC,CAAC;IAEF,IAAIX,QAAQ,CAACK,OAAO,EAAE;MACpBqC,cAAc,CAACO,OAAO,CAACjD,QAAQ,CAACK,OAAO,CAAC;IAC1C;EACF,CAAC;EAED,MAAM6C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACtD,YAAY,CAACuD,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAMC,UAAU,GAAG;MACjBnE,EAAE,EAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC;MACdnE,IAAI,EAAE,MAAM;MACZC,OAAO,EAAES,YAAY;MACrBR,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAACsE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,UAAU,CAAC,CAAC;IAC1C,MAAMG,aAAa,GAAG3D,YAAY;IAClCC,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF;MACA,MAAMyD,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,EAAE;QACjDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,OAAO,EAAER,aAAa;UACtBS,YAAY,EAAE;YACZ5E,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC4E,WAAW,CAAC,CAAC;YACnCC,UAAU,EAAE,eAAe;YAC3BC,SAAS,EAAE;UACb;QACF,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACX,QAAQ,CAACY,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBb,QAAQ,CAACc,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAM9D,IAAI,GAAG,MAAMgD,QAAQ,CAACe,IAAI,CAAC,CAAC;MAClCxE,WAAW,CAAC,KAAK,CAAC;;MAElB;MACA,MAAMyE,aAAa,GAAG;QACpBvF,EAAE,EAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBnE,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAEqB,IAAI,CAACgD,QAAQ,IAAI,uDAAuD;QACjFpE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBoF,aAAa,EAAEjE,IAAI,CAACtB,IAAI;QACxBwF,eAAe,EAAElE,IAAI,CAACkE,eAAe;QACrCC,YAAY,EAAEnE,IAAI,CAACmE,YAAY;QAC/BC,OAAO,EAAEpE,IAAI,CAACoE,OAAO;QACrBC,eAAe,EAAErE,IAAI,CAACqE;MACxB,CAAC;MAED7F,WAAW,CAACsE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEkB,aAAa,CAAC,CAAC;;MAE7C;MACA,IAAIhE,IAAI,CAACtB,IAAI,KAAK,aAAa,IAAIsB,IAAI,CAACmE,YAAY,EAAE;QACpD,MAAMzE,YAAY,GAAG;UACnBjB,EAAE,EAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBnE,IAAI,EAAE,aAAa;UACnBI,MAAM,EAAEkB,IAAI,CAACmE,YAAY,CAACrF,MAAM,IAAI,MAAM;UAC1CC,KAAK,EAAEiB,IAAI,CAACmE,YAAY,CAACG,aAAa,IAAI,MAAM;UAChDtF,MAAM,EAAEgB,IAAI,CAACmE,YAAY,CAACI,YAAY,IAAI,IAAI;UAC9CtF,aAAa,EAAEe,IAAI,CAACmE,YAAY,CAACK,oBAAoB,IAAI,IAAI;UAC7DtF,OAAO,EAAEc,IAAI,CAACmE,YAAY,CAACM,YAAY,IAAI,YAAY;UACvDtF,SAAS,EAAEW,qBAAqB,CAAC,CAAC;UAClClB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDL,WAAW,CAACsE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEpD,YAAY,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIqD,aAAa,CAAC2B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAI5B,aAAa,CAAC2B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxG;QACA,MAAMjF,YAAY,GAAG;UACnBjB,EAAE,EAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBnE,IAAI,EAAE,aAAa;UACnBI,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,IAAI;UACZC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE,YAAY;UACrBC,SAAS,EAAEW,qBAAqB,CAAC,CAAC;UAClClB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDL,WAAW,CAACsE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEpD,YAAY,CAAC,CAAC;MAC9C;IAEF,CAAC,CAAC,OAAOkF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CrF,WAAW,CAAC,KAAK,CAAC;MAElB,MAAMuF,YAAY,GAAG;QACnBrG,EAAE,EAAEI,IAAI,CAACgE,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBnE,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,uFAAuF;QAChGC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDL,WAAW,CAACsE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEgC,YAAY,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBzC,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACEtE,OAAA;IAAKgH,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAC7DjH,OAAA,CAACF,eAAe;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnBrH,OAAA;MAAKgH,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EjH,OAAA,CAACT,MAAM,CAAC+H,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BZ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eAGnCjH,OAAA,CAACT,MAAM,CAAC+H,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,KAAK,EAAE;UAAK,CAAE;UACrCH,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEK,KAAK,EAAE;UAAE,CAAE;UAClCF,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAG/BjH,OAAA;YAAKgH,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCjH,OAAA;cAAIgH,SAAS,EAAC,4GAA4G;cAAAC,QAAA,EAAC;YAE3H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrH,OAAA;cAAGgH,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAExC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNrH,OAAA;YAAKgH,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAElCjH,OAAA,CAACR,eAAe;cAAAyH,QAAA,EACb9G,QAAQ,CAAC4H,GAAG,CAAE5C,OAAO,iBACpBnF,OAAA,CAACT,MAAM,CAAC+H,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BO,IAAI,EAAE;kBAAER,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BE,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAX,QAAA,GAE7B9B,OAAO,CAAC7E,IAAI,KAAK,QAAQ,iBACxBN,OAAA;kBAAKgH,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EACtD9B,OAAO,CAAC5E;gBAAO;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACN,EAEAlC,OAAO,CAAC7E,IAAI,KAAK,MAAM,iBACtBN,OAAA;kBAAKgH,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBjH,OAAA;oBAAKgH,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,EACvH9B,OAAO,CAAC5E;kBAAO;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEAlC,OAAO,CAAC7E,IAAI,KAAK,aAAa,iBAC7BN,OAAA;kBAAKgH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAE/BjH,OAAA;oBAAKgH,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDjH,OAAA;sBAAAiH,QAAA,gBACEjH,OAAA;wBAAKgH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC/C9B,OAAO,CAACzE;sBAAM;wBAAAwG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNrH,OAAA;wBAAKgH,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EACtC9B,OAAO,CAACrE;sBAAO;wBAAAoG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNrH,OAAA;sBAAKgH,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBjH,OAAA;wBAAKgH,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAC1C9B,OAAO,CAACxE;sBAAK;wBAAAuG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACNrH,OAAA;wBAAKgH,SAAS,EAAC,sDAAsD;wBAAAC,QAAA,gBACnEjH,OAAA,CAACL,UAAU;0BAACqH,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,KACtC,EAAClC,OAAO,CAACtE,aAAa,EAAC,GAC1B;sBAAA;wBAAAqG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrH,OAAA;oBAAKgH,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,eACnCjH,OAAA;sBAAKiI,GAAG,EAAE7G,QAAS;sBAAC4F,SAAS,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eAGNrH,OAAA;oBAAKgH,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBjH,OAAA;sBAAQgH,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAE/B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTrH,OAAA;sBAAQgH,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAE/B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GA1DIlC,OAAO,CAAC9E,EAAE;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2DL,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC,eAGlBrH,OAAA,CAACR,eAAe;cAAAyH,QAAA,EACb/F,QAAQ,iBACPlB,OAAA,CAACT,MAAM,CAAC+H,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BO,IAAI,EAAE;kBAAER,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BT,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBAErDjH,OAAA;kBAAKgH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BjH,OAAA;oBAAKgH,SAAS,EAAC;kBAAiD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnErH,OAAA;oBAAKgH,SAAS,EAAC,iDAAiD;oBAACkB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtGrH,OAAA;oBAAKgH,SAAS,EAAC,iDAAiD;oBAACkB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACNrH,OAAA;kBAAMgH,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGNrH,OAAA;YAAKgH,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBjH,OAAA;cAAKgH,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBjH,OAAA;gBACEM,IAAI,EAAC,MAAM;gBACX8H,KAAK,EAAEpH,YAAa;gBACpBqH,QAAQ,EAAGzB,CAAC,IAAK3F,eAAe,CAAC2F,CAAC,CAAC1C,MAAM,CAACkE,KAAK,CAAE;gBACjDE,UAAU,EAAE3B,cAAe;gBAC3B4B,WAAW,EAAC,mBAAmB;gBAC/BvB,SAAS,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACFrH,OAAA;gBACEwI,OAAO,EAAElE,iBAAkB;gBAC3B0C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAE1BjH,OAAA,CAACN,IAAI;kBAACsH,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAnH,EAAA,CA1VMD,cAAc;AAAAwI,EAAA,GAAdxI,cAAc;AA2VpB,SAASyB,qBAAqBA,CAAA,EAAG;EAC/B,MAAME,IAAI,GAAG,EAAE;EACf,IAAI8G,SAAS,GAAG,GAAG;EACnB,MAAMC,QAAQ,GAAG,MAAM;EACvB,MAAMC,UAAU,GAAG,EAAE;EAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAE;IACnC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACvI,IAAI,CAACgE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAACmE,UAAU,GAAGC,CAAC,IAAI,IAAI,CAAC,CAAC;IACtE,MAAMI,QAAQ,GAAGJ,CAAC,IAAID,UAAU,GAAG,CAAC,CAAC;;IAErC;IACA,MAAMM,UAAU,GAAGR,SAAS,GAAG,CAACC,QAAQ,GAAGD,SAAS,IAAIO,QAAQ;IAChE,MAAME,UAAU,GAAG,KAAK,CAAC,CAAC;IAC1B,MAAMC,YAAY,GAAG,CAACL,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,GAAG,IAAIF,UAAU,GAAGD,UAAU;IAEpE,MAAMI,IAAI,GAAGT,CAAC,KAAK,CAAC,GAAGH,SAAS,GAAG9G,IAAI,CAACiH,CAAC,GAAG,CAAC,CAAC,CAACU,KAAK;IACpD,MAAMA,KAAK,GAAGL,UAAU,GAAGE,YAAY;IACvC,MAAMI,IAAI,GAAGT,IAAI,CAACU,GAAG,CAACH,IAAI,EAAEC,KAAK,CAAC,GAAGR,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,GAAG;IACxD,MAAMK,GAAG,GAAGX,IAAI,CAACY,GAAG,CAACL,IAAI,EAAEC,KAAK,CAAC,GAAGR,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,GAAG;IAEvDzH,IAAI,CAACgI,IAAI,CAAC;MACRd,IAAI;MACJQ,IAAI,EAAEO,UAAU,CAACP,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;MACjCN,IAAI,EAAEK,UAAU,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;MACjCJ,GAAG,EAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BP,KAAK,EAAEM,UAAU,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;EACJ;EAEA,OAAOlI,IAAI;AACb;AAEA,eAAe3B,cAAc;AAAC,IAAAwI,EAAA;AAAAsB,YAAA,CAAAtB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}