{"ast": null, "code": "'use client';\n\nexport { default } from './TableRow';\nexport { default as tableRowClasses } from './tableRowClasses';\nexport * from './tableRowClasses';", "map": {"version": 3, "names": ["default", "tableRowClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/TableRow/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TableRow';\nexport { default as tableRowClasses } from './tableRowClasses';\nexport * from './tableRowClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,eAAe,QAAQ,mBAAmB;AAC9D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}