{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst AppWindow = createLucideIcon(\"AppWindow\", [[\"rect\", {\n  x: \"2\",\n  y: \"4\",\n  width: \"20\",\n  height: \"16\",\n  rx: \"2\",\n  key: \"izxlao\"\n}], [\"path\", {\n  d: \"M10 4v4\",\n  key: \"pp8u80\"\n}], [\"path\", {\n  d: \"M2 8h20\",\n  key: \"d11cs7\"\n}], [\"path\", {\n  d: \"M6 4v4\",\n  key: \"1svtjw\"\n}]]);\nexport { AppWindow as default };", "map": {"version": 3, "names": ["AppW<PERSON>ow", "createLucideIcon", "x", "y", "width", "height", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\app-window.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AppWindow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIyIiB5PSI0IiB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xMCA0djQiIC8+CiAgPHBhdGggZD0iTTIgOGgyMCIgLz4KICA8cGF0aCBkPSJNNiA0djQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/app-window\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AppWindow = createLucideIcon('AppWindow', [\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n  ['path', { d: 'M10 4v4', key: 'pp8u80' }],\n  ['path', { d: 'M2 8h20', key: 'd11cs7' }],\n  ['path', { d: 'M6 4v4', key: '1svtjw' }],\n]);\n\nexport default AppWindow;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}