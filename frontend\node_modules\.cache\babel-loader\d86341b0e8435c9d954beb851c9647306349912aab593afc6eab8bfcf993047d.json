{"ast": null, "code": "import React from'react';import{Card,CardContent,Typography,List,ListItem,ListItemText}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PositionsList=_ref=>{let{positions}=_ref;return/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Current Positions\"}),/*#__PURE__*/_jsx(List,{children:(positions===null||positions===void 0?void 0:positions.length)>0?positions.map((position,index)=>/*#__PURE__*/_jsx(ListItem,{children:/*#__PURE__*/_jsx(ListItemText,{primary:\"\".concat(position.symbol,\" - \").concat(position.qty,\" shares\"),secondary:\"Value: $\".concat(position.market_value,\" | P&L: \").concat(position.unrealized_pl)})},index)):/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"No positions\"})})]})});};export default PositionsList;", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "List", "ListItem", "ListItemText", "jsx", "_jsx", "jsxs", "_jsxs", "PositionsList", "_ref", "positions", "children", "variant", "gutterBottom", "length", "map", "position", "index", "primary", "concat", "symbol", "qty", "secondary", "market_value", "unrealized_pl", "color"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/PositionsList.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';\n\nconst PositionsList = ({ positions }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Current Positions\n        </Typography>\n        <List>\n          {positions?.length > 0 ? (\n            positions.map((position, index) => (\n              <ListItem key={index}>\n                <ListItemText\n                  primary={`${position.symbol} - ${position.qty} shares`}\n                  secondary={`Value: $${position.market_value} | P&L: ${position.unrealized_pl}`}\n                />\n              </ListItem>\n            ))\n          ) : (\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              No positions\n            </Typography>\n          )}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default PositionsList;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,CAAEC,UAAU,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,YAAY,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5F,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAAmB,IAAlB,CAAEC,SAAU,CAAC,CAAAD,IAAA,CAClC,mBACEJ,IAAA,CAACP,IAAI,EAAAa,QAAA,cACHJ,KAAA,CAACR,WAAW,EAAAY,QAAA,eACVN,IAAA,CAACL,UAAU,EAACY,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,mBAEtC,CAAY,CAAC,cACbN,IAAA,CAACJ,IAAI,EAAAU,QAAA,CACF,CAAAD,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,MAAM,EAAG,CAAC,CACpBJ,SAAS,CAACK,GAAG,CAAC,CAACC,QAAQ,CAAEC,KAAK,gBAC5BZ,IAAA,CAACH,QAAQ,EAAAS,QAAA,cACPN,IAAA,CAACF,YAAY,EACXe,OAAO,IAAAC,MAAA,CAAKH,QAAQ,CAACI,MAAM,QAAAD,MAAA,CAAMH,QAAQ,CAACK,GAAG,WAAU,CACvDC,SAAS,YAAAH,MAAA,CAAaH,QAAQ,CAACO,YAAY,aAAAJ,MAAA,CAAWH,QAAQ,CAACQ,aAAa,CAAG,CAChF,CAAC,EAJWP,KAKL,CACX,CAAC,cAEFZ,IAAA,CAACL,UAAU,EAACY,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,eAAe,CAAAd,QAAA,CAAC,cAElD,CAAY,CACb,CACG,CAAC,EACI,CAAC,CACV,CAAC,CAEX,CAAC,CAED,cAAe,CAAAH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}