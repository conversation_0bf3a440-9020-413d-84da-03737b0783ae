{"ast": null, "code": "import { motionValue } from './index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useEffect } from 'react';\nimport { warning } from '../utils/errors.mjs';\nimport { scrollInfo } from '../render/dom/scroll/track.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction refWarning(name, ref) {\n  warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n  scrollX: motionValue(0),\n  scrollY: motionValue(0),\n  scrollXProgress: motionValue(0),\n  scrollYProgress: motionValue(0)\n});\nfunction useScroll({\n  container,\n  target,\n  layoutEffect = true,\n  ...options\n} = {}) {\n  const values = useConstant(createScrollMotionValues);\n  const useLifecycleEffect = layoutEffect ? useIsomorphicLayoutEffect : useEffect;\n  useLifecycleEffect(() => {\n    refWarning(\"target\", target);\n    refWarning(\"container\", container);\n    return scrollInfo(({\n      x,\n      y\n    }) => {\n      values.scrollX.set(x.current);\n      values.scrollXProgress.set(x.progress);\n      values.scrollY.set(y.current);\n      values.scrollYProgress.set(y.progress);\n    }, {\n      ...options,\n      container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n      target: (target === null || target === void 0 ? void 0 : target.current) || undefined\n    });\n  }, [container, target, JSON.stringify(options.offset)]);\n  return values;\n}\nexport { useScroll };", "map": {"version": 3, "names": ["motionValue", "useConstant", "useEffect", "warning", "scrollInfo", "useIsomorphicLayoutEffect", "refWarning", "name", "ref", "Boolean", "current", "createScrollMotionValues", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "useScroll", "container", "target", "layoutEffect", "options", "values", "useLifecycleEffect", "x", "y", "set", "progress", "undefined", "JSON", "stringify", "offset"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/value/use-scroll.mjs"], "sourcesContent": ["import { motionValue } from './index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useEffect } from 'react';\nimport { warning } from '../utils/errors.mjs';\nimport { scrollInfo } from '../render/dom/scroll/track.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\n\nfunction refWarning(name, ref) {\n    warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: motionValue(0),\n    scrollY: motionValue(0),\n    scrollXProgress: motionValue(0),\n    scrollYProgress: motionValue(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = useConstant(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? useIsomorphicLayoutEffect\n        : useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return scrollInfo(({ x, y }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n            target: (target === null || target === void 0 ? void 0 : target.current) || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\nexport { useScroll };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,UAAUA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3BL,OAAO,CAACM,OAAO,CAAC,CAACD,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,EAAE,sBAAsBH,IAAI,+MAA+M,CAAC;AACpR;AACA,MAAMI,wBAAwB,GAAGA,CAAA,MAAO;EACpCC,OAAO,EAAEZ,WAAW,CAAC,CAAC,CAAC;EACvBa,OAAO,EAAEb,WAAW,CAAC,CAAC,CAAC;EACvBc,eAAe,EAAEd,WAAW,CAAC,CAAC,CAAC;EAC/Be,eAAe,EAAEf,WAAW,CAAC,CAAC;AAClC,CAAC,CAAC;AACF,SAASgB,SAASA,CAAC;EAAEC,SAAS;EAAEC,MAAM;EAAEC,YAAY,GAAG,IAAI;EAAE,GAAGC;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5E,MAAMC,MAAM,GAAGpB,WAAW,CAACU,wBAAwB,CAAC;EACpD,MAAMW,kBAAkB,GAAGH,YAAY,GACjCd,yBAAyB,GACzBH,SAAS;EACfoB,kBAAkB,CAAC,MAAM;IACrBhB,UAAU,CAAC,QAAQ,EAAEY,MAAM,CAAC;IAC5BZ,UAAU,CAAC,WAAW,EAAEW,SAAS,CAAC;IAClC,OAAOb,UAAU,CAAC,CAAC;MAAEmB,CAAC;MAAEC;IAAE,CAAC,KAAK;MAC5BH,MAAM,CAACT,OAAO,CAACa,GAAG,CAACF,CAAC,CAACb,OAAO,CAAC;MAC7BW,MAAM,CAACP,eAAe,CAACW,GAAG,CAACF,CAAC,CAACG,QAAQ,CAAC;MACtCL,MAAM,CAACR,OAAO,CAACY,GAAG,CAACD,CAAC,CAACd,OAAO,CAAC;MAC7BW,MAAM,CAACN,eAAe,CAACU,GAAG,CAACD,CAAC,CAACE,QAAQ,CAAC;IAC1C,CAAC,EAAE;MACC,GAAGN,OAAO;MACVH,SAAS,EAAE,CAACA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACP,OAAO,KAAKiB,SAAS;MACjGT,MAAM,EAAE,CAACA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACR,OAAO,KAAKiB;IAChF,CAAC,CAAC;EACN,CAAC,EAAE,CAACV,SAAS,EAAEC,MAAM,EAAEU,IAAI,CAACC,SAAS,CAACT,OAAO,CAACU,MAAM,CAAC,CAAC,CAAC;EACvD,OAAOT,MAAM;AACjB;AAEA,SAASL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}