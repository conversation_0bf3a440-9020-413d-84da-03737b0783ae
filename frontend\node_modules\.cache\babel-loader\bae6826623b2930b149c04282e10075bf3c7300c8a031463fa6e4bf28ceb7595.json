{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { drag } from '../../motion/features/drag.mjs';\nimport { layout } from '../../motion/features/layout.mjs';\nimport { domAnimation } from './features-animation.mjs';\n\n/**\n * @public\n */\nconst domMax = _objectSpread(_objectSpread(_objectSpread({}, domAnimation), drag), layout);\nexport { domMax };", "map": {"version": 3, "names": ["drag", "layout", "domAnimation", "domMax", "_objectSpread"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/render/dom/features-max.mjs"], "sourcesContent": ["import { drag } from '../../motion/features/drag.mjs';\nimport { layout } from '../../motion/features/layout.mjs';\nimport { domAnimation } from './features-animation.mjs';\n\n/**\n * @public\n */\nconst domMax = {\n    ...domAnimation,\n    ...drag,\n    ...layout,\n};\n\nexport { domMax };\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,gCAAgC;AACrD,SAASC,MAAM,QAAQ,kCAAkC;AACzD,SAASC,YAAY,QAAQ,0BAA0B;;AAEvD;AACA;AACA;AACA,MAAMC,MAAM,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACLF,YAAY,GACZF,IAAI,GACJC,MAAM,CACZ;AAED,SAASE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}