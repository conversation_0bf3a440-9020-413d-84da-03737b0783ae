{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ShieldHalf = createLucideIcon(\"ShieldHalf\", [[\"path\", {\n  d: \"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10\",\n  key: \"1irkt0\"\n}], [\"path\", {\n  d: \"M12 22V2\",\n  key: \"zs6s6o\"\n}]]);\nexport { ShieldHalf as default };", "map": {"version": 3, "names": ["ShieldHalf", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\shield-half.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ShieldHalf\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+CiAgPHBhdGggZD0iTTEyIDIyVjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield-half\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShieldHalf = createLucideIcon('ShieldHalf', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n  ['path', { d: 'M12 22V2', key: 'zs6s6o' }],\n]);\n\nexport default ShieldHalf;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}