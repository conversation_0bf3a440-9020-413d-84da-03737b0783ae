[tool:pytest]
testpaths = 5_tests_checks
python_files = test_*.py *_test.py comprehensive_*.py
python_classes = Test* *TestSuite
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --maxfail=5
    --disable-warnings
    --color=yes
    --durations=10
markers =
    core: Core system and API endpoint tests
    market: Market data and scanning engine tests
    ai: AI and ML service tests
    trading: Trading and risk engine tests
    options: Options engine tests
    portfolio: Portfolio and optimization tests
    alerts: Proactive alerts and scheduler tests
    backtest: Backtesting and strategy validation tests
    security: Security and error handling tests
    performance: Performance and load tests
    integration: End-to-end integration tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
timeout = 300
