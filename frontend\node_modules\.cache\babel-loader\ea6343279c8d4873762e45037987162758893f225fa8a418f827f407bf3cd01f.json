{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\Dashboard.js\";\nimport React from 'react';\nimport { Card, CardContent, Typography } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = ({\n  accountData,\n  positions,\n  signals\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Account Value: $\", (accountData === null || accountData === void 0 ? void 0 : accountData.portfolio_value) || '0.00']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Positions: \", (positions === null || positions === void 0 ? void 0 : positions.length) || 0]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Signals: \", (signals === null || signals === void 0 ? void 0 : signals.length) || 0]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "jsxDEV", "_jsxDEV", "Dashboard", "accountData", "positions", "signals", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "portfolio_value", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography } from '@mui/material';\n\nconst Dashboard = ({ accountData, positions, signals }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\">Dashboard</Typography>\n        <Typography variant=\"body2\">\n          Account Value: ${accountData?.portfolio_value || '0.00'}\n        </Typography>\n        <Typography variant=\"body2\">\n          Positions: {positions?.length || 0}\n        </Typography>\n        <Typography variant=\"body2\">\n          Signals: {signals?.length || 0}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,SAAS,GAAGA,CAAC;EAAEC,WAAW;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EACzD,oBACEJ,OAAA,CAACJ,IAAI;IAAAS,QAAA,eACHL,OAAA,CAACH,WAAW;MAAAQ,QAAA,gBACVL,OAAA,CAACF,UAAU;QAACQ,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC/CV,OAAA,CAACF,UAAU;QAACQ,OAAO,EAAC,OAAO;QAAAD,QAAA,GAAC,kBACV,EAAC,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAES,eAAe,KAAI,MAAM;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACbV,OAAA,CAACF,UAAU;QAACQ,OAAO,EAAC,OAAO;QAAAD,QAAA,GAAC,aACf,EAAC,CAAAF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,MAAM,KAAI,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACbV,OAAA,CAACF,UAAU;QAACQ,OAAO,EAAC,OAAO;QAAAD,QAAA,GAAC,WACjB,EAAC,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,KAAI,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACG,EAAA,GAjBIZ,SAAS;AAmBf,eAAeA,SAAS;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}