{"ast": null, "code": "import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nfunction animateVariant(visualElement, variant, options = {}) {\n  const resolved = resolveVariant(visualElement, variant, options.custom);\n  let {\n    transition = visualElement.getDefaultTransition() || {}\n  } = resolved || {};\n  if (options.transitionOverride) {\n    transition = options.transitionOverride;\n  }\n  /**\n   * If we have a variant, create a callback that runs it as an animation.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getAnimation = resolved ? () => Promise.all(animateTarget(visualElement, resolved, options)) : () => Promise.resolve();\n  /**\n   * If we have children, create a callback that runs all their animations.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size ? (forwardDelay = 0) => {\n    const {\n      delayChildren = 0,\n      staggerChildren,\n      staggerDirection\n    } = transition;\n    return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n  } : () => Promise.resolve();\n  /**\n   * If the transition explicitly defines a \"when\" option, we need to resolve either\n   * this animation or all children animations before playing the other.\n   */\n  const {\n    when\n  } = transition;\n  if (when) {\n    const [first, last] = when === \"beforeChildren\" ? [getAnimation, getChildAnimations] : [getChildAnimations, getAnimation];\n    return first().then(() => last());\n  } else {\n    return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n  }\n}\nfunction animateChildren(visualElement, variant, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n  const animations = [];\n  const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n  const generateStaggerDuration = staggerDirection === 1 ? (i = 0) => i * staggerChildren : (i = 0) => maxStaggerDuration - i * staggerChildren;\n  Array.from(visualElement.variantChildren).sort(sortByTreeOrder).forEach((child, i) => {\n    child.notify(\"AnimationStart\", variant);\n    animations.push(animateVariant(child, variant, {\n      ...options,\n      delay: delayChildren + generateStaggerDuration(i)\n    }).then(() => child.notify(\"AnimationComplete\", variant)));\n  });\n  return Promise.all(animations);\n}\nfunction sortByTreeOrder(a, b) {\n  return a.sortNodePosition(b);\n}\nexport { animateVariant, sortByTreeOrder };", "map": {"version": 3, "names": ["resolveV<PERSON>t", "animate<PERSON>arget", "animate<PERSON><PERSON><PERSON>", "visualElement", "variant", "options", "resolved", "custom", "transition", "getDefaultTransition", "transitionOverride", "getAnimation", "Promise", "all", "resolve", "getChildAnimations", "variant<PERSON><PERSON><PERSON>n", "size", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "animate<PERSON><PERSON><PERSON><PERSON>", "when", "first", "last", "then", "delay", "animations", "maxStaggerDuration", "generateStaggerDuration", "i", "Array", "from", "sort", "sortByTreeOrder", "for<PERSON>ach", "child", "notify", "push", "a", "b", "sortNodePosition"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs"], "sourcesContent": ["import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\n\nfunction animateVariant(visualElement, variant, options = {}) {\n    const resolved = resolveVariant(visualElement, variant, options.custom);\n    let { transition = visualElement.getDefaultTransition() || {} } = resolved || {};\n    if (options.transitionOverride) {\n        transition = options.transitionOverride;\n    }\n    /**\n     * If we have a variant, create a callback that runs it as an animation.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getAnimation = resolved\n        ? () => Promise.all(animateTarget(visualElement, resolved, options))\n        : () => Promise.resolve();\n    /**\n     * If we have children, create a callback that runs all their animations.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size\n        ? (forwardDelay = 0) => {\n            const { delayChildren = 0, staggerChildren, staggerDirection, } = transition;\n            return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n        }\n        : () => Promise.resolve();\n    /**\n     * If the transition explicitly defines a \"when\" option, we need to resolve either\n     * this animation or all children animations before playing the other.\n     */\n    const { when } = transition;\n    if (when) {\n        const [first, last] = when === \"beforeChildren\"\n            ? [getAnimation, getChildAnimations]\n            : [getChildAnimations, getAnimation];\n        return first().then(() => last());\n    }\n    else {\n        return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n    }\n}\nfunction animateChildren(visualElement, variant, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n    const animations = [];\n    const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n    const generateStaggerDuration = staggerDirection === 1\n        ? (i = 0) => i * staggerChildren\n        : (i = 0) => maxStaggerDuration - i * staggerChildren;\n    Array.from(visualElement.variantChildren)\n        .sort(sortByTreeOrder)\n        .forEach((child, i) => {\n        child.notify(\"AnimationStart\", variant);\n        animations.push(animateVariant(child, variant, {\n            ...options,\n            delay: delayChildren + generateStaggerDuration(i),\n        }).then(() => child.notify(\"AnimationComplete\", variant)));\n    });\n    return Promise.all(animations);\n}\nfunction sortByTreeOrder(a, b) {\n    return a.sortNodePosition(b);\n}\n\nexport { animateVariant, sortByTreeOrder };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iDAAiD;AAChF,SAASC,aAAa,QAAQ,6BAA6B;AAE3D,SAASC,cAAcA,CAACC,aAAa,EAAEC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC1D,MAAMC,QAAQ,GAAGN,cAAc,CAACG,aAAa,EAAEC,OAAO,EAAEC,OAAO,CAACE,MAAM,CAAC;EACvE,IAAI;IAAEC,UAAU,GAAGL,aAAa,CAACM,oBAAoB,CAAC,CAAC,IAAI,CAAC;EAAE,CAAC,GAAGH,QAAQ,IAAI,CAAC,CAAC;EAChF,IAAID,OAAO,CAACK,kBAAkB,EAAE;IAC5BF,UAAU,GAAGH,OAAO,CAACK,kBAAkB;EAC3C;EACA;AACJ;AACA;AACA;EACI,MAAMC,YAAY,GAAGL,QAAQ,GACvB,MAAMM,OAAO,CAACC,GAAG,CAACZ,aAAa,CAACE,aAAa,EAAEG,QAAQ,EAAED,OAAO,CAAC,CAAC,GAClE,MAAMO,OAAO,CAACE,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAMC,kBAAkB,GAAGZ,aAAa,CAACa,eAAe,IAAIb,aAAa,CAACa,eAAe,CAACC,IAAI,GACxF,CAACC,YAAY,GAAG,CAAC,KAAK;IACpB,MAAM;MAAEC,aAAa,GAAG,CAAC;MAAEC,eAAe;MAAEC;IAAkB,CAAC,GAAGb,UAAU;IAC5E,OAAOc,eAAe,CAACnB,aAAa,EAAEC,OAAO,EAAEe,aAAa,GAAGD,YAAY,EAAEE,eAAe,EAAEC,gBAAgB,EAAEhB,OAAO,CAAC;EAC5H,CAAC,GACC,MAAMO,OAAO,CAACE,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAM;IAAES;EAAK,CAAC,GAAGf,UAAU;EAC3B,IAAIe,IAAI,EAAE;IACN,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGF,IAAI,KAAK,gBAAgB,GACzC,CAACZ,YAAY,EAAEI,kBAAkB,CAAC,GAClC,CAACA,kBAAkB,EAAEJ,YAAY,CAAC;IACxC,OAAOa,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,MAAMD,IAAI,CAAC,CAAC,CAAC;EACrC,CAAC,MACI;IACD,OAAOb,OAAO,CAACC,GAAG,CAAC,CAACF,YAAY,CAAC,CAAC,EAAEI,kBAAkB,CAACV,OAAO,CAACsB,KAAK,CAAC,CAAC,CAAC;EAC3E;AACJ;AACA,SAASL,eAAeA,CAACnB,aAAa,EAAEC,OAAO,EAAEe,aAAa,GAAG,CAAC,EAAEC,eAAe,GAAG,CAAC,EAAEC,gBAAgB,GAAG,CAAC,EAAEhB,OAAO,EAAE;EACpH,MAAMuB,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAG,CAAC1B,aAAa,CAACa,eAAe,CAACC,IAAI,GAAG,CAAC,IAAIG,eAAe;EACrF,MAAMU,uBAAuB,GAAGT,gBAAgB,KAAK,CAAC,GAChD,CAACU,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAGX,eAAe,GAC9B,CAACW,CAAC,GAAG,CAAC,KAAKF,kBAAkB,GAAGE,CAAC,GAAGX,eAAe;EACzDY,KAAK,CAACC,IAAI,CAAC9B,aAAa,CAACa,eAAe,CAAC,CACpCkB,IAAI,CAACC,eAAe,CAAC,CACrBC,OAAO,CAAC,CAACC,KAAK,EAAEN,CAAC,KAAK;IACvBM,KAAK,CAACC,MAAM,CAAC,gBAAgB,EAAElC,OAAO,CAAC;IACvCwB,UAAU,CAACW,IAAI,CAACrC,cAAc,CAACmC,KAAK,EAAEjC,OAAO,EAAE;MAC3C,GAAGC,OAAO;MACVsB,KAAK,EAAER,aAAa,GAAGW,uBAAuB,CAACC,CAAC;IACpD,CAAC,CAAC,CAACL,IAAI,CAAC,MAAMW,KAAK,CAACC,MAAM,CAAC,mBAAmB,EAAElC,OAAO,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF,OAAOQ,OAAO,CAACC,GAAG,CAACe,UAAU,CAAC;AAClC;AACA,SAASO,eAAeA,CAACK,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAOD,CAAC,CAACE,gBAAgB,CAACD,CAAC,CAAC;AAChC;AAEA,SAASvC,cAAc,EAAEiC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}