{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst TreeDeciduous = createLucideIcon(\"TreeDeciduous\", [[\"path\", {\n  d: \"M8 19a4 4 0 0 1-2.24-7.32A3.5 3.5 0 0 1 9 6.03V6a3 3 0 1 1 6 0v.04a3.5 3.5 0 0 1 3.24 5.65A4 4 0 0 1 16 19Z\",\n  key: \"oadzkq\"\n}], [\"path\", {\n  d: \"M12 19v3\",\n  key: \"npa21l\"\n}]]);\nexport { TreeDeciduous as default };", "map": {"version": 3, "names": ["TreeDeciduous", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\tree-deciduous.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TreeDeciduous\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAxOWE0IDQgMCAwIDEtMi4yNC03LjMyQTMuNSAzLjUgMCAwIDEgOSA2LjAzVjZhMyAzIDAgMSAxIDYgMHYuMDRhMy41IDMuNSAwIDAgMSAzLjI0IDUuNjVBNCA0IDAgMCAxIDE2IDE5WiIgLz4KICA8cGF0aCBkPSJNMTIgMTl2MyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/tree-deciduous\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TreeDeciduous = createLucideIcon('TreeDeciduous', [\n  [\n    'path',\n    {\n      d: 'M8 19a4 4 0 0 1-2.24-7.32A3.5 3.5 0 0 1 9 6.03V6a3 3 0 1 1 6 0v.04a3.5 3.5 0 0 1 3.24 5.65A4 4 0 0 1 16 19Z',\n      key: 'oadzkq',\n    },\n  ],\n  ['path', { d: 'M12 19v3', key: 'npa21l' }],\n]);\n\nexport default TreeDeciduous;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}