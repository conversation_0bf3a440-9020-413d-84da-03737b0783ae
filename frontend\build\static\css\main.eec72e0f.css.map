{"version": 3, "file": "static/css/main.eec72e0f.css", "mappings": "AACA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yLAAqI,CACrI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,oBACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,gMACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iKACF,CCnEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yLAAqI,CACrI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,oBACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,gMACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iKACF,CCnEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yLAAqI,CACrI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,oBACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,gMACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iKACF,CCnEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yLAAqI,CACrI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,oBACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,gMACF,CAGA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iKACF,CChEA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc;AAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CACd,qBAAoB,CAApB,mDAAoB,EAApB,mDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EACpB,wCAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,8BAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,mGAAmB,CAAnB,mEAAmB,EAAnB,4CAAmB,CAAnB,mBAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,8CAAmB,CAAnB,gBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,4CAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,wEAAmB,CAAnB,yGAAmB,CAAnB,oEAAmB,CAAnB,oEAAmB,CAAnB,qEAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,4CAAmB,CAAnB,0CAAmB,CAAnB,8CAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,8CAAmB,CAAnB,8QAAmB,CAAnB,sQAAmB,CAAnB,kMAAmB,CAAnB,6IAAmB,CAAnB,mMAAmB,CAAnB,kDAAmB,CAGnB,EACE,qBACF,CAEA,UAOE,kCAAmC,CACnC,iCAAkC,CAClC,kBAAmB,CACnB,aAAc,CANd,yIAEY,CAHZ,WAAY,CAFZ,QAAS,CAUT,iBAAkB,CATlB,SAUF,CAEA,MACE,WAAY,CACZ,gBACF,CAGA,UACE,8DAA0E,CAE1E,eAAgB,CADhB,iBAEF,CAEA,iBAOE,2LAGmF,CAJnF,QAAS,CALT,UAAW,CAGX,MAAO,CAOP,mBAAoB,CATpB,iBAAkB,CAGlB,OAAQ,CAFR,KASF,CAGA,YAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAEjC,0BAAwC,CACxC,kBAAmB,CACnB,uDAGF,CAEA,kBACE,sBAAoC,CACpC,sEAGwC,CACxC,uBACF,CAGA,iBAEE,kDAAqD,CACrD,kBAAmB,CAFnB,iBAGF,CAEA,wBAKE,0DAA8D,CAC9D,qBAAsB,CALtB,UAAW,CAEX,OAAQ,CAIR,4EAAsE,CAAtE,oEAAsE,CACtE,kBAAmB,CACnB,0BAA2B,CAL3B,WAAY,CAFZ,iBAQF,CAGA,oBACE,SACF,CAEA,0BACE,oBAAiC,CACjC,iBACF,CAEA,0BACE,kDAAqD,CACrD,iBACF,CAEA,gCACE,kDACF,CAGA,WACE,8BACF,CAGA,UACE,uBACF,CAEA,gBACE,6BAA2C,CAC3C,0BACF,CAGA,mBACE,GAAK,4BAA+B,CACpC,GAAO,wCAA2C,CACpD,CAEA,SAGE,+BAAgC,CAFhC,uDAAoF,CACpF,0BAEF,CAEA,KAGE,oBAAkC,CAElC,iBAAkB,CAClB,aAAc,CALd,iFACW,CAEX,eAGF,CAGA,YAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAkC,CAElC,0BAAwC,CACxC,kBAAmB,CACnB,sEAGwC,CACxC,eAAgB,CAChB,iBACF,CAEA,mBAOE,uDAAoF,CANpF,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,kBAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAmC,CACnC,0BAAwC,CACxC,kBAAmB,CAEnB,YAAa,CADb,YAGF,CAEA,iBACE,oBAA8B,CAG9B,0BAAwC,CAFxC,kBAAmB,CACnB,YAEF,CAEA,YAUE,kCAA2B,CAA3B,0BAA2B,CAR3B,oBAAkC,CAClC,0BAAwC,CAGxC,kBAAmB,CAFnB,aAAc,CAHd,QAAO,CAMP,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAIjB,uBAEF,CAEA,kBACE,oBAAkC,CAClC,sBAAoC,CACpC,6BAA2C,CAC3C,0BACF,CAEA,aAUE,kCAA2B,CAA3B,0BAA2B,CAR3B,oBAA8B,CAC9B,0BAAwC,CACxC,kBAAmB,CAGnB,UAAY,CACZ,cAAe,CAFf,2BAAmB,CAGnB,uBAAyB,CARzB,UAUF,CAEA,0BACE,eACF,CAEA,mBAIE,gBAA8B,CAF9B,sBAAoC,CACpC,6BAA2C,CAF3C,YAIF,CAEA,gBAWE,kBAAmB,CAJnB,kDAAqD,CACrD,WAAY,CACZ,kBAAmB,CAKnB,cAAe,CAJf,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CAXvB,iBAAkB,CAClB,SAAU,CACV,OAAQ,CACR,0BAA2B,CAS3B,uBAAyB,CARzB,UAUF,CAEA,sBAEE,6BAA2C,CAD3C,sCAEF,CAGA,uBAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAkC,CAElC,0BAAwC,CACxC,eAAgB,CAChB,qDAEwC,CACxC,gBAAiB,CACjB,iBACF,CAEA,8BAOE,uDAAoF,CANpF,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CClRA,uBAIE,kDAA6D,CAC7D,kBAAmB,CACnB,4BAA0C,CAC1C,UAAY,CALZ,aAAc,CADd,gBAAiB,CAEjB,YAKF,CAEA,WAEE,kBAAmB,CADnB,iBAEF,CAEA,cAKE,6BAAoC,CAFpC,8CAAiD,CACjD,4BAA6B,CAE7B,oBAAqB,CALrB,gBAAiB,CACjB,kBAKF,CAEA,aACE,gBAAiB,CACjB,UACF,CAEA,SACE,YAAa,CAGb,cAAe,CACf,QAAS,CAHT,sBAAuB,CACvB,kBAGF,CAEA,YAUE,kCAA2B,CAA3B,0BAA2B,CAT3B,oBAAoC,CACpC,sBAA0C,CAG1C,kBAAmB,CAFnB,UAAY,CAGZ,cAAe,CAGf,eAAiB,CADjB,eAAgB,CAJhB,iBAAkB,CAGlB,uBAIF,CAEA,kBACE,gBAAoC,CACpC,kBAAsC,CACtC,0BACF,CAEA,mBACE,oBAAoC,CACpC,kBAAsC,CACtC,2BACF,CAEA,YAIE,kCAA2B,CAA3B,0BAA2B,CAH3B,oBAAoC,CAIpC,sBAA0C,CAH1C,kBAAmB,CACnB,YAGF,CAEA,mBAGE,UAAW,CAFX,gBAAiB,CACjB,kBAEF,CAEA,kBAGE,gBAAiB,CAFjB,kBAAmB,CACnB,UAEF,CAEA,aACE,kBACF,CAEA,mBAIE,UAAW,CAHX,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,uCASE,iCAA0B,CAA1B,yBAA0B,CAH1B,oBAAoC,CAFpC,0BAA0C,CAC1C,kBAAmB,CAEnB,UAAY,CACZ,cAAe,CALf,iBAAkB,CAOlB,uBAAyB,CARzB,UASF,CAEA,gCACE,WACF,CAEA,mDAIE,oBAAqC,CADrC,kBAAsC,CAEtC,6BAA6C,CAH7C,YAIF,CAEA,WACE,iDAAoD,CACpD,WAAY,CAGZ,kBAAmB,CAKnB,2BAAyC,CAPzC,UAAY,CAGZ,cAAe,CACf,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAPhB,iBAAkB,CAKlB,uBAGF,CAEA,gCAEE,+BAAyC,CADzC,0BAEF,CAEA,oBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,iBAEE,oBAAoC,CAGpC,sBAA0C,CAF1C,kBAAmB,CAFnB,eAAgB,CAGhB,YAEF,CAEA,oBACE,UAAW,CAEX,gBAAiB,CADjB,kBAEF,CAEA,kFAOE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,kEAKE,oBAAoC,CAGpC,sBAA0C,CAD1C,kBAAmB,CADnB,YAGF,CAEA,qGAOE,UAAW,CAFX,aAAc,CAGd,eAAiB,CAEjB,mBAAqB,CAJrB,iBAAkB,CAGlB,wBAEF,CAEA,sBACE,oBAAqC,CAGrC,6BAA8B,CAF9B,kBAAmB,CACnB,YAEF,CAEA,yBACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,wBACE,eAAgB,CAChB,QAAS,CACT,UACF,CAEA,eACE,gBAAgC,CAChC,sBAAsC,CAGtC,kBAAmB,CAFnB,UAAW,CAKX,eAAgB,CAFhB,kBAAmB,CAFnB,YAAa,CAGb,iBAEF,CAGA,yBACE,uBAEE,WAAY,CADZ,YAEF,CAEA,cACE,cACF,CAEA,SAEE,kBAAmB,CADnB,qBAEF,CAEA,YAEE,iBAAkB,CADlB,WAEF,CAEA,YACE,YACF,CAEA,kFAKE,yBACF,CACF,CAGA,iBACE,GAAK,SAAY,CACjB,IAAM,UAAc,CACpB,GAAO,SAAY,CACrB,CAEA,oBACE,6BACF,CAGA,gGAKE,oBAAqC,CACrC,0BAA2B,CAC3B,uBACF,CAGA,8HAOE,6BAAoC,CAFpC,8CAAiD,CACjD,4BAA6B,CAE7B,oBAAqB,CAErB,gBAAiB,CADjB,eAEF", "sources": ["../node_modules/@fontsource/inter/400.css", "../node_modules/@fontsource/inter/500.css", "../node_modules/@fontsource/inter/600.css", "../node_modules/@fontsource/inter/700.css", "index.css", "components/AIFeatures.css"], "sourcesContent": ["/* inter-cyrillic-ext-400-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/inter-cyrillic-ext-400-normal.woff2) format('woff2'), url(./files/inter-cyrillic-ext-400-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* inter-cyrillic-400-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/inter-cyrillic-400-normal.woff2) format('woff2'), url(./files/inter-cyrillic-400-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* inter-greek-ext-400-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/inter-greek-ext-400-normal.woff2) format('woff2'), url(./files/inter-greek-ext-400-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* inter-greek-400-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/inter-greek-400-normal.woff2) format('woff2'), url(./files/inter-greek-400-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* inter-vietnamese-400-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/inter-vietnamese-400-normal.woff2) format('woff2'), url(./files/inter-vietnamese-400-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* inter-latin-ext-400-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/inter-latin-ext-400-normal.woff2) format('woff2'), url(./files/inter-latin-ext-400-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* inter-latin-400-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/inter-latin-400-normal.woff2) format('woff2'), url(./files/inter-latin-400-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", "/* inter-cyrillic-ext-500-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/inter-cyrillic-ext-500-normal.woff2) format('woff2'), url(./files/inter-cyrillic-ext-500-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* inter-cyrillic-500-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/inter-cyrillic-500-normal.woff2) format('woff2'), url(./files/inter-cyrillic-500-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* inter-greek-ext-500-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/inter-greek-ext-500-normal.woff2) format('woff2'), url(./files/inter-greek-ext-500-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* inter-greek-500-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/inter-greek-500-normal.woff2) format('woff2'), url(./files/inter-greek-500-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* inter-vietnamese-500-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/inter-vietnamese-500-normal.woff2) format('woff2'), url(./files/inter-vietnamese-500-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* inter-latin-ext-500-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/inter-latin-ext-500-normal.woff2) format('woff2'), url(./files/inter-latin-ext-500-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* inter-latin-500-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/inter-latin-500-normal.woff2) format('woff2'), url(./files/inter-latin-500-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", "/* inter-cyrillic-ext-600-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 600;\n  src: url(./files/inter-cyrillic-ext-600-normal.woff2) format('woff2'), url(./files/inter-cyrillic-ext-600-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* inter-cyrillic-600-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 600;\n  src: url(./files/inter-cyrillic-600-normal.woff2) format('woff2'), url(./files/inter-cyrillic-600-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* inter-greek-ext-600-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 600;\n  src: url(./files/inter-greek-ext-600-normal.woff2) format('woff2'), url(./files/inter-greek-ext-600-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* inter-greek-600-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 600;\n  src: url(./files/inter-greek-600-normal.woff2) format('woff2'), url(./files/inter-greek-600-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* inter-vietnamese-600-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 600;\n  src: url(./files/inter-vietnamese-600-normal.woff2) format('woff2'), url(./files/inter-vietnamese-600-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* inter-latin-ext-600-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 600;\n  src: url(./files/inter-latin-ext-600-normal.woff2) format('woff2'), url(./files/inter-latin-ext-600-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* inter-latin-600-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 600;\n  src: url(./files/inter-latin-600-normal.woff2) format('woff2'), url(./files/inter-latin-600-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", "/* inter-cyrillic-ext-700-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/inter-cyrillic-ext-700-normal.woff2) format('woff2'), url(./files/inter-cyrillic-ext-700-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* inter-cyrillic-700-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/inter-cyrillic-700-normal.woff2) format('woff2'), url(./files/inter-cyrillic-700-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* inter-greek-ext-700-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/inter-greek-ext-700-normal.woff2) format('woff2'), url(./files/inter-greek-ext-700-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* inter-greek-700-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/inter-greek-700-normal.woff2) format('woff2'), url(./files/inter-greek-700-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* inter-vietnamese-700-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/inter-vietnamese-700-normal.woff2) format('woff2'), url(./files/inter-vietnamese-700-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* inter-latin-ext-700-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/inter-latin-ext-700-normal.woff2) format('woff2'), url(./files/inter-latin-ext-700-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* inter-latin-700-normal */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/inter-latin-700-normal.woff2) format('woff2'), url(./files/inter-latin-700-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", "@import '@fontsource/inter/400.css';\n@import '@fontsource/inter/500.css';\n@import '@fontsource/inter/600.css';\n@import '@fontsource/inter/700.css';\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* A.T.L.A.S Space Theme Global Styles */\n* {\n  box-sizing: border-box;\n}\n\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background: #0f172a;\n  color: #f8fafc;\n  overflow-x: hidden;\n}\n\n#root {\n  height: 100%;\n  min-height: 100vh;\n}\n\n/* Space Background */\n.space-bg {\n  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.space-bg::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: \n    radial-gradient(circle at 20% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(34, 211, 238, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 40% 40%, rgba(103, 232, 249, 0.05) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n/* Glassmorphism Card */\n.glass-card {\n  background: rgba(15, 23, 42, 0.7);\n  backdrop-filter: blur(16px);\n  border: 1px solid rgba(6, 182, 212, 0.2);\n  border-radius: 24px;\n  box-shadow: \n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n\n.glass-card:hover {\n  border-color: rgba(6, 182, 212, 0.4);\n  box-shadow: \n    0 8px 32px rgba(0, 0, 0, 0.4),\n    0 0 20px rgba(6, 182, 212, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n/* Gradient Border */\n.gradient-border {\n  position: relative;\n  background: linear-gradient(135deg, #0f172a, #1e293b);\n  border-radius: 24px;\n}\n\n.gradient-border::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  padding: 1px;\n  background: linear-gradient(135deg, #06b6d4, #22d3ee, #67e8f9);\n  border-radius: inherit;\n  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n  mask-composite: xor;\n  -webkit-mask-composite: xor;\n}\n\n/* Scrollbar Styling */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: rgba(15, 23, 42, 0.5);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, #06b6d4, #22d3ee);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, #0891b2, #06b6d4);\n}\n\n/* Text Glow Effect */\n.text-glow {\n  text-shadow: 0 0 10px rgba(6, 182, 212, 0.5);\n}\n\n/* Button Hover Effects */\n.btn-glow {\n  transition: all 0.3s ease;\n}\n\n.btn-glow:hover {\n  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);\n  transform: translateY(-2px);\n}\n\n/* Loading Animation */\n@keyframes shimmer {\n  0% { background-position: -200px 0; }\n  100% { background-position: calc(200px + 100%) 0; }\n}\n\n.shimmer {\n  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.2), transparent);\n  background-size: 200px 100%;\n  animation: shimmer 1.5s infinite;\n}\n\ncode {\n  font-family: 'Fira Code', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n  background: rgba(6, 182, 212, 0.1);\n  padding: 2px 6px;\n  border-radius: 4px;\n  color: #67e8f9;\n}\n\n/* Enhanced A.T.L.A.S Interface Styles */\n.atlas-card {\n  background: rgba(15, 23, 42, 0.85);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(6, 182, 212, 0.3);\n  border-radius: 20px;\n  box-shadow:\n    0 8px 32px rgba(0, 0, 0, 0.4),\n    0 0 40px rgba(6, 182, 212, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  overflow: hidden;\n  position: relative;\n}\n\n.atlas-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.5), transparent);\n}\n\n.stock-quote-card {\n  background: rgba(6, 182, 212, 0.05);\n  border: 1px solid rgba(6, 182, 212, 0.2);\n  border-radius: 16px;\n  padding: 16px;\n  margin: 8px 0;\n  backdrop-filter: blur(10px);\n}\n\n.chart-container {\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 12px;\n  padding: 12px;\n  border: 1px solid rgba(6, 182, 212, 0.1);\n}\n\n.action-btn {\n  flex: 1;\n  background: rgba(6, 182, 212, 0.1);\n  border: 1px solid rgba(6, 182, 212, 0.3);\n  color: #67e8f9;\n  padding: 8px 12px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  backdrop-filter: blur(10px);\n}\n\n.action-btn:hover {\n  background: rgba(6, 182, 212, 0.2);\n  border-color: rgba(6, 182, 212, 0.5);\n  box-shadow: 0 0 15px rgba(6, 182, 212, 0.3);\n  transform: translateY(-1px);\n}\n\n.atlas-input {\n  width: 100%;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(6, 182, 212, 0.3);\n  border-radius: 16px;\n  padding: 12px 16px;\n  padding-right: 48px;\n  color: white;\n  font-size: 14px;\n  transition: all 0.2s ease;\n  backdrop-filter: blur(10px);\n}\n\n.atlas-input::placeholder {\n  color: rgba(103, 232, 249, 0.5);\n}\n\n.atlas-input:focus {\n  outline: none;\n  border-color: rgba(6, 182, 212, 0.6);\n  box-shadow: 0 0 20px rgba(6, 182, 212, 0.2);\n  background: rgba(0, 0, 0, 0.4);\n}\n\n.atlas-send-btn {\n  position: absolute;\n  right: 4px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #06b6d4, #22d3ee);\n  border: none;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.atlas-send-btn:hover {\n  transform: translateY(-50%) scale(1.05);\n  box-shadow: 0 0 15px rgba(6, 182, 212, 0.4);\n}\n\n/* Full-screen A.T.L.A.S Interface */\n.atlas-card-fullscreen {\n  background: rgba(15, 23, 42, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(6, 182, 212, 0.3);\n  border-radius: 0;\n  box-shadow:\n    0 0 50px rgba(6, 182, 212, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  min-height: 100vh;\n  position: relative;\n}\n\n.atlas-card-fullscreen::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.5), transparent);\n}\n", ".ai-features-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 15px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  color: white;\n}\n\n.ai-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.ai-header h2 {\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n  background: linear-gradient(45deg, #fff, #f0f0f0);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.ai-header p {\n  font-size: 1.1rem;\n  opacity: 0.9;\n}\n\n.ai-tabs {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 30px;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.tab-button {\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  padding: 12px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 600;\n  font-size: 0.9rem;\n  backdrop-filter: blur(10px);\n}\n\n.tab-button:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.4);\n  transform: translateY(-2px);\n}\n\n.tab-button.active {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.6);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n}\n\n.ai-content {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 15px;\n  padding: 30px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.ai-tab-content h3 {\n  font-size: 1.8rem;\n  margin-bottom: 10px;\n  color: #fff;\n}\n\n.ai-tab-content p {\n  margin-bottom: 25px;\n  opacity: 0.9;\n  font-size: 1.1rem;\n}\n\n.input-group {\n  margin-bottom: 20px;\n}\n\n.input-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #fff;\n}\n\n.input-group input,\n.input-group select {\n  width: 100%;\n  padding: 12px 15px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 10px;\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  font-size: 1rem;\n  backdrop-filter: blur(5px);\n  transition: all 0.3s ease;\n}\n\n.input-group input::placeholder {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.input-group input:focus,\n.input-group select:focus {\n  outline: none;\n  border-color: rgba(255, 255, 255, 0.6);\n  background: rgba(255, 255, 255, 0.15);\n  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);\n}\n\n.ai-button {\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  border: none;\n  color: white;\n  padding: 15px 30px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 1.1rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n  margin-top: 10px;\n}\n\n.ai-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n}\n\n.ai-button:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.results-section {\n  margin-top: 30px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 15px;\n  padding: 25px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.results-section h4 {\n  color: #fff;\n  margin-bottom: 20px;\n  font-size: 1.4rem;\n}\n\n.regime-summary,\n.sentiment-summary,\n.embeddings-summary,\n.ttm-summary,\n.news-summary {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.regime-item,\n.sentiment-item,\n.embedding-item,\n.ttm-item,\n.news-item {\n  background: rgba(255, 255, 255, 0.1);\n  padding: 15px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.regime-item strong,\n.sentiment-item strong,\n.embedding-item strong,\n.ttm-item strong,\n.news-item strong {\n  display: block;\n  margin-bottom: 5px;\n  color: #fff;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.holly-interpretation {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 20px;\n  border-left: 4px solid #4CAF50;\n}\n\n.holly-interpretation h5 {\n  color: #4CAF50;\n  margin-bottom: 10px;\n  font-size: 1.2rem;\n}\n\n.holly-interpretation p {\n  line-height: 1.6;\n  margin: 0;\n  opacity: 0.9;\n}\n\n.error-message {\n  background: rgba(255, 0, 0, 0.2);\n  border: 1px solid rgba(255, 0, 0, 0.4);\n  color: #fff;\n  padding: 15px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  text-align: center;\n  font-weight: 600;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .ai-features-container {\n    padding: 15px;\n    margin: 10px;\n  }\n  \n  .ai-header h2 {\n    font-size: 2rem;\n  }\n  \n  .ai-tabs {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .tab-button {\n    width: 200px;\n    text-align: center;\n  }\n  \n  .ai-content {\n    padding: 20px;\n  }\n  \n  .regime-summary,\n  .sentiment-summary,\n  .embeddings-summary,\n  .ttm-summary,\n  .news-summary {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* Animation for loading states */\n@keyframes pulse {\n  0% { opacity: 1; }\n  50% { opacity: 0.5; }\n  100% { opacity: 1; }\n}\n\n.ai-button:disabled {\n  animation: pulse 1.5s infinite;\n}\n\n/* Hover effects for result items */\n.regime-item:hover,\n.sentiment-item:hover,\n.embedding-item:hover,\n.ttm-item:hover,\n.news-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n  transition: all 0.3s ease;\n}\n\n/* Gradient text for values */\n.regime-item:not(strong),\n.sentiment-item:not(strong),\n.embedding-item:not(strong),\n.ttm-item:not(strong),\n.news-item:not(strong) {\n  background: linear-gradient(45deg, #fff, #f0f0f0);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n"], "names": [], "sourceRoot": ""}