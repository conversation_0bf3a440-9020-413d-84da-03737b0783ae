{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 3H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M5 10h3.13c.21.78.67 1.47 1.27 2H5zm14 2h-4.4c.6-.53 1.06-1.22 1.27-2H19zm0-4h-5v1c0 1.07-.93 2-2 2s-2-.93-2-2V8H5V5h14zm-5 7v1c0 .47-.19.9-.48 1.25-.37.45-.92.75-1.52.75s-1.15-.3-1.52-.75c-.29-.35-.48-.78-.48-1.25v-1H3v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4zm-9 2h3.13c.02.09.06.17.09.25.24.68.65 1.28 1.18 1.75H5zm14 2h-4.4c.54-.47.95-1.07 1.18-1.75.03-.08.07-.16.09-.25H19z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M8.13 10H5v2h4.4c-.6-.53-1.06-1.22-1.27-2m6.47 2H19v-2h-3.13c-.21.78-.67 1.47-1.27 2m-6.38 5.25c-.03-.08-.06-.16-.09-.25H5v2h4.4c-.53-.47-.94-1.07-1.18-1.75m7.65-.25c-.02.09-.06.17-.09.25-.23.68-.64 1.28-1.18 1.75H19v-2z\",\n  opacity: \".3\"\n}, \"1\")], 'AllInboxTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/AllInboxTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 3H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M5 10h3.13c.21.78.67 1.47 1.27 2H5zm14 2h-4.4c.6-.53 1.06-1.22 1.27-2H19zm0-4h-5v1c0 1.07-.93 2-2 2s-2-.93-2-2V8H5V5h14zm-5 7v1c0 .47-.19.9-.48 1.25-.37.45-.92.75-1.52.75s-1.15-.3-1.52-.75c-.29-.35-.48-.78-.48-1.25v-1H3v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4zm-9 2h3.13c.02.09.06.17.09.25.24.68.65 1.28 1.18 1.75H5zm14 2h-4.4c.54-.47.95-1.07 1.18-1.75.03-.08.07-.16.09-.25H19z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M8.13 10H5v2h4.4c-.6-.53-1.06-1.22-1.27-2m6.47 2H19v-2h-3.13c-.21.78-.67 1.47-1.27 2m-6.38 5.25c-.03-.08-.06-.16-.09-.25H5v2h4.4c-.53-.47-.94-1.07-1.18-1.75m7.65-.25c-.02.09-.06.17-.09.25-.23.68-.64 1.28-1.18 1.75H19v-2z\",\n  opacity: \".3\"\n}, \"1\")], 'AllInboxTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE,8NAA8N;EACjOC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}