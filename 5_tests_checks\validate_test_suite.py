#!/usr/bin/env python3
"""
Test Suite Validation Script
Validates that the comprehensive test suite is properly configured
"""

import sys
import os
import importlib.util

def validate_test_suite():
    """Validate the comprehensive test suite"""
    print("🔍 Validating A.T.L.A.S. Comprehensive Test Suite")
    print("=" * 50)
    
    # Check if test file exists
    test_file_path = os.path.join(os.path.dirname(__file__), "comprehensive_atlas_test_suite.py")
    if not os.path.exists(test_file_path):
        print("❌ Test suite file not found!")
        return False
    
    print("✅ Test suite file found")
    
    # Try to import the test suite
    try:
        spec = importlib.util.spec_from_file_location("test_suite", test_file_path)
        test_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        print("✅ Test suite imports successfully")
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Check if ATLASTestSuite class exists
    if not hasattr(test_module, 'ATLASTestSuite'):
        print("❌ ATLASTestSuite class not found!")
        return False
    
    print("✅ ATLASTestSuite class found")
    
    # Instantiate the test suite
    try:
        suite = test_module.ATLASTestSuite()
        print("✅ Test suite instantiated successfully")
    except Exception as e:
        print(f"❌ Instantiation error: {e}")
        return False
    
    # Count test methods
    test_methods = [method for method in dir(suite) if method.startswith('test_')]
    print(f"✅ Found {len(test_methods)} test methods")
    
    # List test categories
    categories = {
        "Core System": [m for m in test_methods if any(x in m for x in ['health', 'init', 'chat', 'session', 'prepare'])],
        "Market Data": [m for m in test_methods if any(x in m for x in ['quote', 'ttm', 'real_time'])],
        "AI/ML": [m for m in test_methods if any(x in m for x in ['sentiment', 'lstm', 'search'])],
        "Trading": [m for m in test_methods if any(x in m for x in ['trade', 'plan', 'risk'])],
        "Options": [m for m in test_methods if any(x in m for x in ['options', 'greeks', 'unusual'])],
        "Portfolio": [m for m in test_methods if any(x in m for x in ['portfolio', 'optimization'])],
        "Alerts": [m for m in test_methods if any(x in m for x in ['alert', 'briefing'])],
        "Backtest": [m for m in test_methods if any(x in m for x in ['backtest'])],
        "Security": [m for m in test_methods if any(x in m for x in ['auth', 'rate', 'sanitization'])]
    }
    
    print("\n📊 Test Coverage by Category:")
    total_categorized = 0
    for category, methods in categories.items():
        print(f"   {category}: {len(methods)} tests")
        total_categorized += len(methods)
    
    uncategorized = len(test_methods) - total_categorized
    if uncategorized > 0:
        print(f"   Uncategorized: {uncategorized} tests")
    
    # Validate 6-point format validator
    try:
        sample_response = """
        **A.T.L.A.S powered by Predicto**
        
        **1. Why This Trade?**
        Test reason
        
        **2. Win/Loss Probabilities**
        70% chance you hit target, 30% chance you hit stop
        
        **3. Potential Money In or Out**
        You could make $500 or lose $200
        
        **4. Smart Stop Plans**
        Exit at $100 if price drops
        
        **5. Market Context**
        Market is bullish today
        
        **6. Confidence Score**
        Confidence: 85%
        """
        
        validation_result = suite._validate_6_point_format(sample_response)
        if validation_result["score"] == 6:
            print("✅ 6-point format validator working correctly")
        else:
            print(f"⚠️  6-point format validator found {validation_result['score']}/6 sections")
            print(f"   Missing: {validation_result['missing_sections']}")
    
    except Exception as e:
        print(f"❌ 6-point format validator error: {e}")
        return False
    
    print(f"\n🎯 Total Test Methods: {len(test_methods)}")
    print("✅ Test suite validation complete!")
    
    return True

def main():
    """Main validation function"""
    success = validate_test_suite()
    
    if success:
        print("\n🎉 Test suite is ready to run!")
        print("\nTo execute the full test suite:")
        print("   python 5_tests_checks/run_comprehensive_tests.py")
        print("\nOr run individual test categories:")
        print("   python -m pytest 5_tests_checks/comprehensive_atlas_test_suite.py -v")
        return 0
    else:
        print("\n❌ Test suite validation failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
