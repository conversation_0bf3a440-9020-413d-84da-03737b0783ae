{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\HollyChat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Paper, TextField, Button, Box, Typography, List, ListItem, Divider, Alert, CircularProgress, Card, CardContent, CardActions, Chip, Grid, IconButton, Tooltip } from '@mui/material';\nimport { Send as SendIcon, SmartToy as HollyIcon, Person as PersonIcon, TrendingUp as TrendingUpIcon, Assessment as AssessmentIcon, AccountBalance as AccountBalanceIcon, Refresh as RefreshIcon, Help as HelpIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HollyChat = ({\n  onPlanExecuted\n}) => {\n  _s();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: 'assistant',\n    content: \"Hi! I'm <PERSON>, your personal trading assistant. I can help you with anything trading-related - just ask me in plain English!\\n\\nTry asking me:\\n• \\\"Make me $50 today\\\"\\n• \\\"What's AAPL looking like?\\\"\\n• \\\"Find me some momentum plays\\\"\\n• \\\"I need a hedge for my Tesla position\\\"\",\n    timestamp: new Date(),\n    response_type: 'chat'\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [quickActions] = useState([{\n    label: \"Make me $50\",\n    message: \"Make me $50 today\"\n  }, {\n    label: \"Find opportunities\",\n    message: \"Find me some good trading opportunities\"\n  }, {\n    label: \"Market overview\",\n    message: \"What's the market looking like today?\"\n  }, {\n    label: \"TTM Squeeze\",\n    message: \"Show me some TTM Squeeze setups\"\n  }]);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = async (messageText = null) => {\n    const messageToSend = messageText || inputMessage;\n    if (!messageToSend.trim() || isLoading) return;\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageToSend,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'web_session'\n          }\n        })\n      });\n      const data = await response.json();\n      const hollyMessage = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: data.response,\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n      setMessages(prev => [...prev, hollyMessage]);\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again or rephrase your request.',\n        timestamp: new Date(),\n        response_type: 'error',\n        error: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleExecutePlan = async planId => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/v1/holly/execute', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          plan_id: planId,\n          confirm: true\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        const successMessage = {\n          id: Date.now(),\n          type: 'assistant',\n          content: `✅ ${data.message}\\n\\nExecuted orders:\\n${data.executed_orders.map(order => `• ${order.side.toUpperCase()} ${order.quantity} ${order.symbol}`).join('\\n')}`,\n          timestamp: new Date(),\n          response_type: 'execution_result'\n        };\n        setMessages(prev => [...prev, successMessage]);\n        if (onPlanExecuted) {\n          onPlanExecuted();\n        }\n      } else {\n        throw new Error(data.message || 'Failed to execute plan');\n      }\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now(),\n        type: 'assistant',\n        content: `❌ Failed to execute plan: ${error.message}`,\n        timestamp: new Date(),\n        response_type: 'error',\n        error: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getResponseIcon = responseType => {\n    switch (responseType) {\n      case 'trading_plan':\n        return /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n          fontSize: \"small\",\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 16\n        }, this);\n      case 'market_analysis':\n        return /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n          fontSize: \"small\",\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 16\n        }, this);\n      case 'market_data':\n        return /*#__PURE__*/_jsxDEV(AccountBalanceIcon, {\n          fontSize: \"small\",\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(HollyIcon, {\n          fontSize: \"small\",\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const renderTradingPlan = message => {\n    var _plan$summary, _plan$summary2, _plan$summary3, _plan$summary4;\n    if (!message.trading_plan) return null;\n    const plan = message.trading_plan;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 1,\n        backgroundColor: '#e8f5e8',\n        border: '1px solid #4caf50'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n            color: \"success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            color: \"success.main\",\n            children: \"Trading Plan Generated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Trades:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), \" \", ((_plan$summary = plan.summary) === null || _plan$summary === void 0 ? void 0 : _plan$summary.total_trades) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Risk:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), \" $\", ((_plan$summary2 = plan.summary) === null || _plan$summary2 === void 0 ? void 0 : _plan$summary2.total_risk) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Expected Return:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), \" $\", ((_plan$summary3 = plan.summary) === null || _plan$summary3 === void 0 ? void 0 : _plan$summary3.expected_return) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Risk/Reward:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), \" \", ((_plan$summary4 = plan.summary) === null || _plan$summary4 === void 0 ? void 0 : _plan$summary4.risk_reward_ratio) || 0, \":1\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), plan.trades && plan.trades.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: \"Planned Trades:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), plan.trades.map((trade, index) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 1,\n              p: 1,\n              backgroundColor: 'rgba(255,255,255,0.7)',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: trade.action.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this), \" \", trade.quantity, \" \", trade.symbol, \" @ $\", trade.entry_price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this), trade.stop_price && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              children: [\"Stop: $\", trade.stop_price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 21\n            }, this), trade.target_price && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"success\",\n              sx: {\n                ml: 2\n              },\n              children: [\"Target: $\", trade.target_price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"contained\",\n          color: \"success\",\n          onClick: () => handleExecutePlan(message.plan_id),\n          disabled: isLoading,\n          startIcon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 24\n          }, this),\n          children: \"Execute Plan (Paper Trading)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 3,\n    sx: {\n      height: '700px',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        color: 'white',\n        borderBottom: '1px solid #ddd'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(HollyIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Holly AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"Paper Trading\",\n            size: \"small\",\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Get help\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              sx: {\n                color: 'white'\n              },\n              onClick: () => handleSendMessage(\"What can you help me with?\"),\n              children: /*#__PURE__*/_jsxDEV(HelpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Reset conversation\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              sx: {\n                color: 'white'\n              },\n              onClick: async () => {\n                try {\n                  await fetch('/api/v1/holly/reset', {\n                    method: 'POST'\n                  });\n                  setMessages([{\n                    id: 1,\n                    type: 'assistant',\n                    content: \"Hi! I'm Holly AI. How can I help you with trading today?\",\n                    timestamp: new Date(),\n                    response_type: 'chat'\n                  }]);\n                } catch (error) {\n                  console.error('Failed to reset conversation:', error);\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 1,\n        backgroundColor: '#f8f9fa',\n        borderBottom: '1px solid #eee'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: quickActions.map((action, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: action.label,\n          size: \"small\",\n          onClick: () => handleSendMessage(action.message),\n          sx: {\n            cursor: 'pointer'\n          },\n          variant: \"outlined\"\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflow: 'auto',\n        p: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(List, {\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(ListItem, {\n          sx: {\n            flexDirection: 'column',\n            alignItems: 'stretch',\n            py: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n              width: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                maxWidth: '85%',\n                backgroundColor: message.type === 'user' ? '#1976d2' : '#f5f5f5',\n                color: message.type === 'user' ? 'white' : 'black',\n                borderRadius: 2,\n                p: 1.5,\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  mb: 0.5\n                },\n                children: [message.type === 'user' ? /*#__PURE__*/_jsxDEV(PersonIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 48\n                }, this) : getResponseIcon(message.response_type), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: formatTime(message.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), message.function_called && /*#__PURE__*/_jsxDEV(Chip, {\n                  label: message.function_called,\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  whiteSpace: 'pre-wrap'\n                },\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), message.error && /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"error\",\n                sx: {\n                  mt: 1\n                },\n                children: \"There was an error processing your request.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), renderTradingPlan(message)]\n        }, message.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(ListItem, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Holly is thinking...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          multiline: true,\n          maxRows: 3,\n          placeholder: \"Ask Holly anything about trading... (e.g., 'Make me $50 today')\",\n          value: inputMessage,\n          onChange: e => setInputMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          disabled: isLoading,\n          variant: \"outlined\",\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: () => handleSendMessage(),\n          disabled: !inputMessage.trim() || isLoading,\n          sx: {\n            minWidth: 'auto',\n            px: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 0.5,\n          display: 'block'\n        },\n        children: \"Holly understands natural language - just ask what you want to do!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n};\n_s(HollyChat, \"2E9pKksKOAokZlh+HX5onAdA1OY=\");\n_c = HollyChat;\nexport default HollyChat;\nvar _c;\n$RefreshReg$(_c, \"HollyChat\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Paper", "TextField", "<PERSON><PERSON>", "Box", "Typography", "List", "ListItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Chip", "Grid", "IconButton", "<PERSON><PERSON><PERSON>", "Send", "SendIcon", "SmartToy", "HollyIcon", "Person", "PersonIcon", "TrendingUp", "TrendingUpIcon", "Assessment", "AssessmentIcon", "AccountBalance", "AccountBalanceIcon", "Refresh", "RefreshIcon", "Help", "HelpIcon", "jsxDEV", "_jsxDEV", "HollyChat", "onPlanExecuted", "_s", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "response_type", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "quickActions", "label", "message", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "messageText", "messageToSend", "trim", "userMessage", "now", "prev", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "user_context", "toISOString", "session_id", "data", "json", "hollyMessage", "requires_action", "trading_plan", "plan_id", "function_called", "error", "errorMessage", "handleExecutePlan", "planId", "confirm", "success", "successMessage", "executed_orders", "map", "order", "side", "toUpperCase", "quantity", "symbol", "join", "Error", "handleKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "formatTime", "toLocaleTimeString", "hour", "minute", "getResponseIcon", "responseType", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderTradingPlan", "_plan$summary", "_plan$summary2", "_plan$summary3", "_plan$summary4", "plan", "sx", "mt", "backgroundColor", "border", "children", "display", "alignItems", "gap", "mb", "variant", "container", "spacing", "item", "xs", "summary", "total_trades", "total_risk", "expected_return", "risk_reward_ratio", "trades", "length", "fontWeight", "trade", "index", "p", "borderRadius", "action", "entry_price", "stop_price", "target_price", "ml", "size", "onClick", "disabled", "startIcon", "elevation", "height", "flexDirection", "background", "borderBottom", "justifyContent", "title", "console", "flexWrap", "cursor", "flex", "overflow", "py", "width", "max<PERSON><PERSON><PERSON>", "whiteSpace", "severity", "ref", "fullWidth", "multiline", "maxRows", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "min<PERSON><PERSON><PERSON>", "px", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/HollyChat.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Paper,\n  TextField,\n  Button,\n  Box,\n  Typography,\n  List,\n  ListItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  Card,\n  CardContent,\n  CardActions,\n  Chip,\n  Grid,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  Send as SendIcon,\n  SmartToy as HollyI<PERSON>,\n  Person as PersonIcon,\n  TrendingUp as TrendingUpIcon,\n  Assessment as AssessmentIcon,\n  AccountBalance as AccountBalanceIcon,\n  Refresh as RefreshIcon,\n  Help as HelpIcon\n} from '@mui/icons-material';\n\nconst HollyChat = ({ onPlanExecuted }) => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: 'assistant',\n      content: \"Hi! I'm <PERSON>, your personal trading assistant. I can help you with anything trading-related - just ask me in plain English!\\n\\nTry asking me:\\n• \\\"Make me $50 today\\\"\\n• \\\"What's AAPL looking like?\\\"\\n• \\\"Find me some momentum plays\\\"\\n• \\\"I need a hedge for my Tesla position\\\"\",\n      timestamp: new Date(),\n      response_type: 'chat'\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [quickActions] = useState([\n    { label: \"Make me $50\", message: \"Make me $50 today\" },\n    { label: \"Find opportunities\", message: \"Find me some good trading opportunities\" },\n    { label: \"Market overview\", message: \"What's the market looking like today?\" },\n    { label: \"TTM Squeeze\", message: \"Show me some TTM Squeeze setups\" }\n  ]);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async (messageText = null) => {\n    const messageToSend = messageText || inputMessage;\n    if (!messageToSend.trim() || isLoading) return;\n\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageToSend,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'web_session'\n          }\n        })\n      });\n\n      const data = await response.json();\n\n      const hollyMessage = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: data.response,\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n\n      setMessages(prev => [...prev, hollyMessage]);\n\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again or rephrase your request.',\n        timestamp: new Date(),\n        response_type: 'error',\n        error: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleExecutePlan = async (planId) => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/v1/holly/execute', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          plan_id: planId,\n          confirm: true\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        const successMessage = {\n          id: Date.now(),\n          type: 'assistant',\n          content: `✅ ${data.message}\\n\\nExecuted orders:\\n${data.executed_orders.map(order => \n            `• ${order.side.toUpperCase()} ${order.quantity} ${order.symbol}`\n          ).join('\\n')}`,\n          timestamp: new Date(),\n          response_type: 'execution_result'\n        };\n        setMessages(prev => [...prev, successMessage]);\n        \n        if (onPlanExecuted) {\n          onPlanExecuted();\n        }\n      } else {\n        throw new Error(data.message || 'Failed to execute plan');\n      }\n\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now(),\n        type: 'assistant',\n        content: `❌ Failed to execute plan: ${error.message}`,\n        timestamp: new Date(),\n        response_type: 'error',\n        error: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatTime = (timestamp) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  const getResponseIcon = (responseType) => {\n    switch (responseType) {\n      case 'trading_plan':\n        return <TrendingUpIcon fontSize=\"small\" color=\"primary\" />;\n      case 'market_analysis':\n        return <AssessmentIcon fontSize=\"small\" color=\"info\" />;\n      case 'market_data':\n        return <AccountBalanceIcon fontSize=\"small\" color=\"success\" />;\n      default:\n        return <HollyIcon fontSize=\"small\" color=\"primary\" />;\n    }\n  };\n\n  const renderTradingPlan = (message) => {\n    if (!message.trading_plan) return null;\n\n    const plan = message.trading_plan;\n    \n    return (\n      <Card sx={{ mt: 1, backgroundColor: '#e8f5e8', border: '1px solid #4caf50' }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>\n            <TrendingUpIcon color=\"success\" />\n            <Typography variant=\"subtitle2\" color=\"success.main\">\n              Trading Plan Generated\n            </Typography>\n          </Box>\n          \n          <Grid container spacing={2}>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Trades:</strong> {plan.summary?.total_trades || 0}\n              </Typography>\n            </Grid>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Total Risk:</strong> ${plan.summary?.total_risk || 0}\n              </Typography>\n            </Grid>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Expected Return:</strong> ${plan.summary?.expected_return || 0}\n              </Typography>\n            </Grid>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Risk/Reward:</strong> {plan.summary?.risk_reward_ratio || 0}:1\n              </Typography>\n            </Grid>\n          </Grid>\n\n          {plan.trades && plan.trades.length > 0 && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                Planned Trades:\n              </Typography>\n              {plan.trades.map((trade, index) => (\n                <Box key={index} sx={{ mb: 1, p: 1, backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 1 }}>\n                  <Typography variant=\"body2\">\n                    <strong>{trade.action.toUpperCase()}</strong> {trade.quantity} {trade.symbol} @ ${trade.entry_price}\n                  </Typography>\n                  {trade.stop_price && (\n                    <Typography variant=\"caption\" color=\"error\">\n                      Stop: ${trade.stop_price}\n                    </Typography>\n                  )}\n                  {trade.target_price && (\n                    <Typography variant=\"caption\" color=\"success\" sx={{ ml: 2 }}>\n                      Target: ${trade.target_price}\n                    </Typography>\n                  )}\n                </Box>\n              ))}\n            </Box>\n          )}\n        </CardContent>\n        <CardActions>\n          <Button \n            size=\"small\" \n            variant=\"contained\" \n            color=\"success\"\n            onClick={() => handleExecutePlan(message.plan_id)}\n            disabled={isLoading}\n            startIcon={<TrendingUpIcon />}\n          >\n            Execute Plan (Paper Trading)\n          </Button>\n        </CardActions>\n      </Card>\n    );\n  };\n\n  return (\n    <Paper elevation={3} sx={{ height: '700px', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Box sx={{ \n        p: 2, \n        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        color: 'white',\n        borderBottom: '1px solid #ddd' \n      }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <HollyIcon />\n            <Typography variant=\"h6\">Holly AI</Typography>\n            <Chip label=\"Paper Trading\" size=\"small\" color=\"warning\" />\n          </Box>\n          <Box>\n            <Tooltip title=\"Get help\">\n              <IconButton \n                size=\"small\" \n                sx={{ color: 'white' }}\n                onClick={() => handleSendMessage(\"What can you help me with?\")}\n              >\n                <HelpIcon />\n              </IconButton>\n            </Tooltip>\n            <Tooltip title=\"Reset conversation\">\n              <IconButton \n                size=\"small\" \n                sx={{ color: 'white' }}\n                onClick={async () => {\n                  try {\n                    await fetch('/api/v1/holly/reset', { method: 'POST' });\n                    setMessages([{\n                      id: 1,\n                      type: 'assistant',\n                      content: \"Hi! I'm Holly AI. How can I help you with trading today?\",\n                      timestamp: new Date(),\n                      response_type: 'chat'\n                    }]);\n                  } catch (error) {\n                    console.error('Failed to reset conversation:', error);\n                  }\n                }}\n              >\n                <RefreshIcon />\n              </IconButton>\n            </Tooltip>\n          </Box>\n        </Box>\n      </Box>\n\n      {/* Quick Actions */}\n      <Box sx={{ p: 1, backgroundColor: '#f8f9fa', borderBottom: '1px solid #eee' }}>\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {quickActions.map((action, index) => (\n            <Chip\n              key={index}\n              label={action.label}\n              size=\"small\"\n              onClick={() => handleSendMessage(action.message)}\n              sx={{ cursor: 'pointer' }}\n              variant=\"outlined\"\n            />\n          ))}\n        </Box>\n      </Box>\n\n      {/* Messages */}\n      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>\n        <List>\n          {messages.map((message) => (\n            <ListItem key={message.id} sx={{ flexDirection: 'column', alignItems: 'stretch', py: 1 }}>\n              <Box sx={{ \n                display: 'flex', \n                justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n                width: '100%'\n              }}>\n                <Box sx={{ \n                  maxWidth: '85%',\n                  backgroundColor: message.type === 'user' ? '#1976d2' : '#f5f5f5',\n                  color: message.type === 'user' ? 'white' : 'black',\n                  borderRadius: 2,\n                  p: 1.5,\n                  mb: 1\n                }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\n                    {message.type === 'user' ? <PersonIcon fontSize=\"small\" /> : getResponseIcon(message.response_type)}\n                    <Typography variant=\"caption\">\n                      {formatTime(message.timestamp)}\n                    </Typography>\n                    {message.function_called && (\n                      <Chip label={message.function_called} size=\"small\" variant=\"outlined\" />\n                    )}\n                  </Box>\n                  <Typography variant=\"body2\" sx={{ whiteSpace: 'pre-wrap' }}>\n                    {message.content}\n                  </Typography>\n                  \n                  {message.error && (\n                    <Alert severity=\"error\" sx={{ mt: 1 }}>\n                      There was an error processing your request.\n                    </Alert>\n                  )}\n                </Box>\n              </Box>\n\n              {/* Render trading plan if present */}\n              {renderTradingPlan(message)}\n            </ListItem>\n          ))}\n          {isLoading && (\n            <ListItem>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <CircularProgress size={20} />\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Holly is thinking...\n                </Typography>\n              </Box>\n            </ListItem>\n          )}\n        </List>\n        <div ref={messagesEndRef} />\n      </Box>\n\n      <Divider />\n\n      {/* Input */}\n      <Box sx={{ p: 2 }}>\n        <Box sx={{ display: 'flex', gap: 1 }}>\n          <TextField\n            fullWidth\n            multiline\n            maxRows={3}\n            placeholder=\"Ask Holly anything about trading... (e.g., 'Make me $50 today')\"\n            value={inputMessage}\n            onChange={(e) => setInputMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            disabled={isLoading}\n            variant=\"outlined\"\n            size=\"small\"\n          />\n          <Button\n            variant=\"contained\"\n            onClick={() => handleSendMessage()}\n            disabled={!inputMessage.trim() || isLoading}\n            sx={{ minWidth: 'auto', px: 2 }}\n          >\n            <SendIcon />\n          </Button>\n        </Box>\n        <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 0.5, display: 'block' }}>\n          Holly understands natural language - just ask what you want to do!\n        </Typography>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default HollyChat;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,SAAS,EACrBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,cAAc,IAAIC,kBAAkB,EACpCC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,CACvC;IACE2C,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,4RAA4R;IACrSC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;IACrBC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqD,YAAY,CAAC,GAAGrD,QAAQ,CAAC,CAC9B;IAAEsD,KAAK,EAAE,aAAa;IAAEC,OAAO,EAAE;EAAoB,CAAC,EACtD;IAAED,KAAK,EAAE,oBAAoB;IAAEC,OAAO,EAAE;EAA0C,CAAC,EACnF;IAAED,KAAK,EAAE,iBAAiB;IAAEC,OAAO,EAAE;EAAwC,CAAC,EAC9E;IAAED,KAAK,EAAE,aAAa;IAAEC,OAAO,EAAE;EAAkC,CAAC,CACrE,CAAC;EACF,MAAMC,cAAc,GAAGvD,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMwD,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED3D,SAAS,CAAC,MAAM;IACduD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAEd,MAAMqB,iBAAiB,GAAG,MAAAA,CAAOC,WAAW,GAAG,IAAI,KAAK;IACtD,MAAMC,aAAa,GAAGD,WAAW,IAAId,YAAY;IACjD,IAAI,CAACe,aAAa,CAACC,IAAI,CAAC,CAAC,IAAId,SAAS,EAAE;IAExC,MAAMe,WAAW,GAAG;MAClBvB,EAAE,EAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC;MACdvB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEmB,aAAa;MACtBlB,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;IAC3ChB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,EAAE;QACjDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBpB,OAAO,EAAES,aAAa;UACtBY,YAAY,EAAE;YACZ9B,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC,CAAC;YACnCC,UAAU,EAAE;UACd;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAElC,MAAMC,YAAY,GAAG;QACnBtC,EAAE,EAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBvB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEkC,IAAI,CAACV,QAAQ;QACtBvB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,aAAa,EAAE+B,IAAI,CAACnC,IAAI;QACxBsC,eAAe,EAAEH,IAAI,CAACG,eAAe;QACrCC,YAAY,EAAEJ,IAAI,CAACI,YAAY;QAC/BC,OAAO,EAAEL,IAAI,CAACK,OAAO;QACrBC,eAAe,EAAEN,IAAI,CAACM;MACxB,CAAC;MAED3C,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,YAAY,CAAC,CAAC;IAE9C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,MAAMC,YAAY,GAAG;QACnB5C,EAAE,EAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBvB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,2EAA2E;QACpFC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,aAAa,EAAE,OAAO;QACtBsC,KAAK,EAAE;MACT,CAAC;MACD5C,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEmB,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACRnC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMoC,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1CrC,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,uBAAuB,EAAE;QACpDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBS,OAAO,EAAEK,MAAM;UACfC,OAAO,EAAE;QACX,CAAC;MACH,CAAC,CAAC;MAEF,MAAMX,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACY,OAAO,EAAE;QAChB,MAAMC,cAAc,GAAG;UACrBjD,EAAE,EAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC;UACdvB,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE,KAAKkC,IAAI,CAACxB,OAAO,yBAAyBwB,IAAI,CAACc,eAAe,CAACC,GAAG,CAACC,KAAK,IAC/E,KAAKA,KAAK,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,IAAIF,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACI,MAAM,EACjE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;UACdtD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrBC,aAAa,EAAE;QACjB,CAAC;QACDN,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEwB,cAAc,CAAC,CAAC;QAE9C,IAAIrD,cAAc,EAAE;UAClBA,cAAc,CAAC,CAAC;QAClB;MACF,CAAC,MAAM;QACL,MAAM,IAAI8D,KAAK,CAACtB,IAAI,CAACxB,OAAO,IAAI,wBAAwB,CAAC;MAC3D;IAEF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACd,MAAMC,YAAY,GAAG;QACnB5C,EAAE,EAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC;QACdvB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,6BAA6ByC,KAAK,CAAC/B,OAAO,EAAE;QACrDT,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,aAAa,EAAE,OAAO;QACtBsC,KAAK,EAAE;MACT,CAAC;MACD5C,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEmB,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACRnC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkD,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,CAAC,CAAC;MACtB5C,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAM6C,UAAU,GAAI7D,SAAS,IAAK;IAChC,OAAOA,SAAS,CAAC8D,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;EAED,MAAMC,eAAe,GAAIC,YAAY,IAAK;IACxC,QAAQA,YAAY;MAClB,KAAK,cAAc;QACjB,oBAAO3E,OAAA,CAACV,cAAc;UAACsF,QAAQ,EAAC,OAAO;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,iBAAiB;QACpB,oBAAOjF,OAAA,CAACR,cAAc;UAACoF,QAAQ,EAAC,OAAO;UAACC,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,aAAa;QAChB,oBAAOjF,OAAA,CAACN,kBAAkB;UAACkF,QAAQ,EAAC,OAAO;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChE;QACE,oBAAOjF,OAAA,CAACd,SAAS;UAAC0F,QAAQ,EAAC,OAAO;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIhE,OAAO,IAAK;IAAA,IAAAiE,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;IACrC,IAAI,CAACpE,OAAO,CAAC4B,YAAY,EAAE,OAAO,IAAI;IAEtC,MAAMyC,IAAI,GAAGrE,OAAO,CAAC4B,YAAY;IAEjC,oBACE9C,OAAA,CAACxB,IAAI;MAACgH,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,eAAe,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAoB,CAAE;MAAAC,QAAA,gBAC3E5F,OAAA,CAACvB,WAAW;QAAAmH,QAAA,gBACV5F,OAAA,CAAC/B,GAAG;UAACuH,EAAE,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBAChE5F,OAAA,CAACV,cAAc;YAACuF,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClCjF,OAAA,CAAC9B,UAAU;YAAC+H,OAAO,EAAC,WAAW;YAACpB,KAAK,EAAC,cAAc;YAAAe,QAAA,EAAC;UAErD;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENjF,OAAA,CAACpB,IAAI;UAACsH,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAP,QAAA,gBACzB5F,OAAA,CAACpB,IAAI;YAACwH,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACf5F,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,OAAO;cAAAL,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,EAAAE,aAAA,GAAAI,IAAI,CAACe,OAAO,cAAAnB,aAAA,uBAAZA,aAAA,CAAcoB,YAAY,KAAI,CAAC;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAACwH,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACf5F,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,OAAO;cAAAL,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAW;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,MAAE,EAAC,EAAAG,cAAA,GAAAG,IAAI,CAACe,OAAO,cAAAlB,cAAA,uBAAZA,cAAA,CAAcoB,UAAU,KAAI,CAAC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAACwH,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACf5F,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,OAAO;cAAAL,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAgB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,MAAE,EAAC,EAAAI,cAAA,GAAAE,IAAI,CAACe,OAAO,cAAAjB,cAAA,uBAAZA,cAAA,CAAcoB,eAAe,KAAI,CAAC;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAACwH,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACf5F,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,OAAO;cAAAL,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAY;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,EAAAK,cAAA,GAAAC,IAAI,CAACe,OAAO,cAAAhB,cAAA,uBAAZA,cAAA,CAAcoB,iBAAiB,KAAI,CAAC,EAAC,IACtE;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAENM,IAAI,CAACoB,MAAM,IAAIpB,IAAI,CAACoB,MAAM,CAACC,MAAM,GAAG,CAAC,iBACpC5G,OAAA,CAAC/B,GAAG;UAACuH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAG,QAAA,gBACjB5F,OAAA,CAAC9B,UAAU;YAAC+H,OAAO,EAAC,OAAO;YAACT,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEb,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EAAC;UAE/D;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZM,IAAI,CAACoB,MAAM,CAAClD,GAAG,CAAC,CAACqD,KAAK,EAAEC,KAAK,kBAC5B/G,OAAA,CAAC/B,GAAG;YAAauH,EAAE,EAAE;cAAEQ,EAAE,EAAE,CAAC;cAAEgB,CAAC,EAAE,CAAC;cAAEtB,eAAe,EAAE,uBAAuB;cAAEuB,YAAY,EAAE;YAAE,CAAE;YAAArB,QAAA,gBAC9F5F,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,OAAO;cAAAL,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAASkB,KAAK,CAACI,MAAM,CAACtD,WAAW,CAAC;cAAC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KAAC,EAAC6B,KAAK,CAACjD,QAAQ,EAAC,GAAC,EAACiD,KAAK,CAAChD,MAAM,EAAC,MAAI,EAACgD,KAAK,CAACK,WAAW;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,EACZ6B,KAAK,CAACM,UAAU,iBACfpH,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,SAAS;cAACpB,KAAK,EAAC,OAAO;cAAAe,QAAA,GAAC,SACnC,EAACkB,KAAK,CAACM,UAAU;YAAA;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACb,EACA6B,KAAK,CAACO,YAAY,iBACjBrH,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,SAAS;cAACpB,KAAK,EAAC,SAAS;cAACW,EAAE,EAAE;gBAAE8B,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,GAAC,WAClD,EAACkB,KAAK,CAACO,YAAY;YAAA;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CACb;UAAA,GAbO8B,KAAK;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eACdjF,OAAA,CAACtB,WAAW;QAAAkH,QAAA,eACV5F,OAAA,CAAChC,MAAM;UACLuJ,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,WAAW;UACnBpB,KAAK,EAAC,SAAS;UACf2C,OAAO,EAAEA,CAAA,KAAMrE,iBAAiB,CAACjC,OAAO,CAAC6B,OAAO,CAAE;UAClD0E,QAAQ,EAAE3G,SAAU;UACpB4G,SAAS,eAAE1H,OAAA,CAACV,cAAc;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAW,QAAA,EAC/B;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX,CAAC;EAED,oBACEjF,OAAA,CAAClC,KAAK;IAAC6J,SAAS,EAAE,CAAE;IAACnC,EAAE,EAAE;MAAEoC,MAAM,EAAE,OAAO;MAAE/B,OAAO,EAAE,MAAM;MAAEgC,aAAa,EAAE;IAAS,CAAE;IAAAjC,QAAA,gBAErF5F,OAAA,CAAC/B,GAAG;MAACuH,EAAE,EAAE;QACPwB,CAAC,EAAE,CAAC;QACJtB,eAAe,EAAE,mDAAmD;QACpEoC,UAAU,EAAE,mDAAmD;QAC/DjD,KAAK,EAAE,OAAO;QACdkD,YAAY,EAAE;MAChB,CAAE;MAAAnC,QAAA,eACA5F,OAAA,CAAC/B,GAAG;QAACuH,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEkC,cAAc,EAAE;QAAgB,CAAE;QAAApC,QAAA,gBAClF5F,OAAA,CAAC/B,GAAG;UAACuH,EAAE,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAH,QAAA,gBACzD5F,OAAA,CAACd,SAAS;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACbjF,OAAA,CAAC9B,UAAU;YAAC+H,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9CjF,OAAA,CAACrB,IAAI;YAACsC,KAAK,EAAC,eAAe;YAACsG,IAAI,EAAC,OAAO;YAAC1C,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACNjF,OAAA,CAAC/B,GAAG;UAAA2H,QAAA,gBACF5F,OAAA,CAAClB,OAAO;YAACmJ,KAAK,EAAC,UAAU;YAAArC,QAAA,eACvB5F,OAAA,CAACnB,UAAU;cACT0I,IAAI,EAAC,OAAO;cACZ/B,EAAE,EAAE;gBAAEX,KAAK,EAAE;cAAQ,CAAE;cACvB2C,OAAO,EAAEA,CAAA,KAAM/F,iBAAiB,CAAC,4BAA4B,CAAE;cAAAmE,QAAA,eAE/D5F,OAAA,CAACF,QAAQ;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACVjF,OAAA,CAAClB,OAAO;YAACmJ,KAAK,EAAC,oBAAoB;YAAArC,QAAA,eACjC5F,OAAA,CAACnB,UAAU;cACT0I,IAAI,EAAC,OAAO;cACZ/B,EAAE,EAAE;gBAAEX,KAAK,EAAE;cAAQ,CAAE;cACvB2C,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnB,IAAI;kBACF,MAAMvF,KAAK,CAAC,qBAAqB,EAAE;oBAAEC,MAAM,EAAE;kBAAO,CAAC,CAAC;kBACtD7B,WAAW,CAAC,CAAC;oBACXC,EAAE,EAAE,CAAC;oBACLC,IAAI,EAAE,WAAW;oBACjBC,OAAO,EAAE,0DAA0D;oBACnEC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;oBACrBC,aAAa,EAAE;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,OAAOsC,KAAK,EAAE;kBACdiF,OAAO,CAACjF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;gBACvD;cACF,CAAE;cAAA2C,QAAA,eAEF5F,OAAA,CAACJ,WAAW;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA,CAAC/B,GAAG;MAACuH,EAAE,EAAE;QAAEwB,CAAC,EAAE,CAAC;QAAEtB,eAAe,EAAE,SAAS;QAAEqC,YAAY,EAAE;MAAiB,CAAE;MAAAnC,QAAA,eAC5E5F,OAAA,CAAC/B,GAAG;QAACuH,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,CAAC;UAAEoC,QAAQ,EAAE;QAAO,CAAE;QAAAvC,QAAA,EACpD5E,YAAY,CAACyC,GAAG,CAAC,CAACyD,MAAM,EAAEH,KAAK,kBAC9B/G,OAAA,CAACrB,IAAI;UAEHsC,KAAK,EAAEiG,MAAM,CAACjG,KAAM;UACpBsG,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAM/F,iBAAiB,CAACyF,MAAM,CAAChG,OAAO,CAAE;UACjDsE,EAAE,EAAE;YAAE4C,MAAM,EAAE;UAAU,CAAE;UAC1BnC,OAAO,EAAC;QAAU,GALbc,KAAK;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA,CAAC/B,GAAG;MAACuH,EAAE,EAAE;QAAE6C,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAEtB,CAAC,EAAE;MAAE,CAAE;MAAApB,QAAA,gBAC3C5F,OAAA,CAAC7B,IAAI;QAAAyH,QAAA,GACFxF,QAAQ,CAACqD,GAAG,CAAEvC,OAAO,iBACpBlB,OAAA,CAAC5B,QAAQ;UAAkBoH,EAAE,EAAE;YAAEqC,aAAa,EAAE,QAAQ;YAAE/B,UAAU,EAAE,SAAS;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,gBACvF5F,OAAA,CAAC/B,GAAG;YAACuH,EAAE,EAAE;cACPK,OAAO,EAAE,MAAM;cACfmC,cAAc,EAAE9G,OAAO,CAACX,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;cACnEiI,KAAK,EAAE;YACT,CAAE;YAAA5C,QAAA,eACA5F,OAAA,CAAC/B,GAAG;cAACuH,EAAE,EAAE;gBACPiD,QAAQ,EAAE,KAAK;gBACf/C,eAAe,EAAExE,OAAO,CAACX,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;gBAChEsE,KAAK,EAAE3D,OAAO,CAACX,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,OAAO;gBAClD0G,YAAY,EAAE,CAAC;gBACfD,CAAC,EAAE,GAAG;gBACNhB,EAAE,EAAE;cACN,CAAE;cAAAJ,QAAA,gBACA5F,OAAA,CAAC/B,GAAG;gBAACuH,EAAE,EAAE;kBAAEK,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,GACjE1E,OAAO,CAACX,IAAI,KAAK,MAAM,gBAAGP,OAAA,CAACZ,UAAU;kBAACwF,QAAQ,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAGP,eAAe,CAACxD,OAAO,CAACP,aAAa,CAAC,eACnGX,OAAA,CAAC9B,UAAU;kBAAC+H,OAAO,EAAC,SAAS;kBAAAL,QAAA,EAC1BtB,UAAU,CAACpD,OAAO,CAACT,SAAS;gBAAC;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,EACZ/D,OAAO,CAAC8B,eAAe,iBACtBhD,OAAA,CAACrB,IAAI;kBAACsC,KAAK,EAAEC,OAAO,CAAC8B,eAAgB;kBAACuE,IAAI,EAAC,OAAO;kBAACtB,OAAO,EAAC;gBAAU;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACxE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNjF,OAAA,CAAC9B,UAAU;gBAAC+H,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEkD,UAAU,EAAE;gBAAW,CAAE;gBAAA9C,QAAA,EACxD1E,OAAO,CAACV;cAAO;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAEZ/D,OAAO,CAAC+B,KAAK,iBACZjD,OAAA,CAAC1B,KAAK;gBAACqK,QAAQ,EAAC,OAAO;gBAACnD,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAG,QAAA,EAAC;cAEvC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLC,iBAAiB,CAAChE,OAAO,CAAC;QAAA,GApCdA,OAAO,CAACZ,EAAE;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqCf,CACX,CAAC,EACDnE,SAAS,iBACRd,OAAA,CAAC5B,QAAQ;UAAAwH,QAAA,eACP5F,OAAA,CAAC/B,GAAG;YAACuH,EAAE,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAH,QAAA,gBACzD5F,OAAA,CAACzB,gBAAgB;cAACgJ,IAAI,EAAE;YAAG;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjF,OAAA,CAAC9B,UAAU;cAAC+H,OAAO,EAAC,OAAO;cAACpB,KAAK,EAAC,gBAAgB;cAAAe,QAAA,EAAC;YAEnD;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACPjF,OAAA;QAAK4I,GAAG,EAAEzH;MAAe;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENjF,OAAA,CAAC3B,OAAO;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXjF,OAAA,CAAC/B,GAAG;MAACuH,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAAApB,QAAA,gBAChB5F,OAAA,CAAC/B,GAAG;QAACuH,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACnC5F,OAAA,CAACjC,SAAS;UACR8K,SAAS;UACTC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,WAAW,EAAC,iEAAiE;UAC7EC,KAAK,EAAErI,YAAa;UACpBsI,QAAQ,EAAGC,CAAC,IAAKtI,eAAe,CAACsI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACjDI,UAAU,EAAEpF,cAAe;UAC3BwD,QAAQ,EAAE3G,SAAU;UACpBmF,OAAO,EAAC,UAAU;UAClBsB,IAAI,EAAC;QAAO;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACFjF,OAAA,CAAChC,MAAM;UACLiI,OAAO,EAAC,WAAW;UACnBuB,OAAO,EAAEA,CAAA,KAAM/F,iBAAiB,CAAC,CAAE;UACnCgG,QAAQ,EAAE,CAAC7G,YAAY,CAACgB,IAAI,CAAC,CAAC,IAAId,SAAU;UAC5C0E,EAAE,EAAE;YAAE8D,QAAQ,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA3D,QAAA,eAEhC5F,OAAA,CAAChB,QAAQ;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNjF,OAAA,CAAC9B,UAAU;QAAC+H,OAAO,EAAC,SAAS;QAACpB,KAAK,EAAC,gBAAgB;QAACW,EAAE,EAAE;UAAEC,EAAE,EAAE,GAAG;UAAEI,OAAO,EAAE;QAAQ,CAAE;QAAAD,QAAA,EAAC;MAExF;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAC9E,EAAA,CA/YIF,SAAS;AAAAuJ,EAAA,GAATvJ,SAAS;AAiZf,eAAeA,SAAS;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}