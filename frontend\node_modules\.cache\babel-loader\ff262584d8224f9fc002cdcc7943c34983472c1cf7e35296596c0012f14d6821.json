{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BaggageClaim = createLucideIcon(\"BaggageClaim\", [[\"path\", {\n  d: \"M22 18H6a2 2 0 0 1-2-2V7a2 2 0 0 0-2-2\",\n  key: \"4irg2o\"\n}], [\"path\", {\n  d: \"M17 14V4a2 2 0 0 0-2-2h-1a2 2 0 0 0-2 2v10\",\n  key: \"14fcyx\"\n}], [\"rect\", {\n  width: \"13\",\n  height: \"8\",\n  x: \"8\",\n  y: \"6\",\n  rx: \"1\",\n  key: \"o6oiis\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"t9985n\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"e5v82j\"\n}]]);\nexport { BaggageClaim as default };", "map": {"version": 3, "names": ["BaggageClaim", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\baggage-claim.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BaggageClaim\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMThINmEyIDIgMCAwIDEtMi0yVjdhMiAyIDAgMCAwLTItMiIgLz4KICA8cGF0aCBkPSJNMTcgMTRWNGEyIDIgMCAwIDAtMi0yaC0xYTIgMiAwIDAgMC0yIDJ2MTAiIC8+CiAgPHJlY3Qgd2lkdGg9IjEzIiBoZWlnaHQ9IjgiIHg9IjgiIHk9IjYiIHJ4PSIxIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjIiIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjIwIiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/baggage-claim\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BaggageClaim = createLucideIcon('BaggageClaim', [\n  ['path', { d: 'M22 18H6a2 2 0 0 1-2-2V7a2 2 0 0 0-2-2', key: '4irg2o' }],\n  ['path', { d: 'M17 14V4a2 2 0 0 0-2-2h-1a2 2 0 0 0-2 2v10', key: '14fcyx' }],\n  ['rect', { width: '13', height: '8', x: '8', y: '6', rx: '1', key: 'o6oiis' }],\n  ['circle', { cx: '18', cy: '20', r: '2', key: 't9985n' }],\n  ['circle', { cx: '9', cy: '20', r: '2', key: 'e5v82j' }],\n]);\n\nexport default BaggageClaim;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAU;EAAEM,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKR,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEM,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKR,GAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}