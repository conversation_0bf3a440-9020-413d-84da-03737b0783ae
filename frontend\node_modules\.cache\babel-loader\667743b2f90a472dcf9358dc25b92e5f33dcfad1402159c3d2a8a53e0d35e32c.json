{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\SpaceBackground.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SpaceBackground = () => {\n  _s();\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Create particles\n    const createParticles = () => {\n      const particles = [];\n      const particleCount = 150;\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          size: Math.random() * 2 + 0.5,\n          speedX: (Math.random() - 0.5) * 0.5,\n          speedY: (Math.random() - 0.5) * 0.5,\n          opacity: Math.random() * 0.8 + 0.2,\n          color: `rgba(${Math.random() > 0.5 ? '6, 182, 212' : '34, 211, 238'}, ${Math.random() * 0.8 + 0.2})`,\n          twinkle: Math.random() * Math.PI * 2,\n          twinkleSpeed: Math.random() * 0.02 + 0.01\n        });\n      }\n      return particles;\n    };\n    particlesRef.current = createParticles();\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // Draw particles\n      particlesRef.current.forEach((particle, index) => {\n        // Update position\n        particle.x += particle.speedX;\n        particle.y += particle.speedY;\n\n        // Update twinkle\n        particle.twinkle += particle.twinkleSpeed;\n        const twinkleOpacity = Math.sin(particle.twinkle) * 0.3 + 0.7;\n\n        // Wrap around edges\n        if (particle.x < 0) particle.x = canvas.width;\n        if (particle.x > canvas.width) particle.x = 0;\n        if (particle.y < 0) particle.y = canvas.height;\n        if (particle.y > canvas.height) particle.y = 0;\n\n        // Draw particle\n        ctx.save();\n        ctx.globalAlpha = particle.opacity * twinkleOpacity;\n        ctx.fillStyle = particle.color;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n\n        // Add glow effect for larger particles\n        if (particle.size > 1.5) {\n          ctx.shadowBlur = 10;\n          ctx.shadowColor = particle.color;\n          ctx.beginPath();\n          ctx.arc(particle.x, particle.y, particle.size * 0.5, 0, Math.PI * 2);\n          ctx.fill();\n        }\n        ctx.restore();\n      });\n\n      // Draw connections between nearby particles\n      particlesRef.current.forEach((particle, i) => {\n        particlesRef.current.slice(i + 1).forEach(otherParticle => {\n          const dx = particle.x - otherParticle.x;\n          const dy = particle.y - otherParticle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          if (distance < 100) {\n            ctx.save();\n            ctx.globalAlpha = (100 - distance) / 100 * 0.1;\n            ctx.strokeStyle = 'rgba(6, 182, 212, 0.3)';\n            ctx.lineWidth = 0.5;\n            ctx.beginPath();\n            ctx.moveTo(particle.x, particle.y);\n            ctx.lineTo(otherParticle.x, otherParticle.y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        });\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"canvas\", {\n    ref: canvasRef,\n    className: \"fixed inset-0 w-full h-full pointer-events-none\",\n    style: {\n      zIndex: 1\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(SpaceBackground, \"AoANNor1ZemK+BAAyEBUpug8tqg=\");\n_c = SpaceBackground;\nexport default SpaceBackground;\nvar _c;\n$RefreshReg$(_c, \"SpaceBackground\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "SpaceBackground", "_s", "canvasRef", "animationRef", "particlesRef", "canvas", "current", "ctx", "getContext", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "createParticles", "particles", "particleCount", "i", "push", "x", "Math", "random", "y", "size", "speedX", "speedY", "opacity", "color", "twinkle", "PI", "twinkleSpeed", "animate", "clearRect", "for<PERSON>ach", "particle", "index", "twinkleOpacity", "sin", "save", "globalAlpha", "fillStyle", "beginPath", "arc", "fill", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "restore", "slice", "otherParticle", "dx", "dy", "distance", "sqrt", "strokeStyle", "lineWidth", "moveTo", "lineTo", "stroke", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "ref", "className", "style", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/SpaceBackground.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst SpaceBackground = () => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    \n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    \n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Create particles\n    const createParticles = () => {\n      const particles = [];\n      const particleCount = 150;\n      \n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          size: Math.random() * 2 + 0.5,\n          speedX: (Math.random() - 0.5) * 0.5,\n          speedY: (Math.random() - 0.5) * 0.5,\n          opacity: Math.random() * 0.8 + 0.2,\n          color: `rgba(${Math.random() > 0.5 ? '6, 182, 212' : '34, 211, 238'}, ${Math.random() * 0.8 + 0.2})`,\n          twinkle: Math.random() * Math.PI * 2,\n          twinkleSpeed: Math.random() * 0.02 + 0.01\n        });\n      }\n      \n      return particles;\n    };\n\n    particlesRef.current = createParticles();\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      // Draw particles\n      particlesRef.current.forEach((particle, index) => {\n        // Update position\n        particle.x += particle.speedX;\n        particle.y += particle.speedY;\n        \n        // Update twinkle\n        particle.twinkle += particle.twinkleSpeed;\n        const twinkleOpacity = Math.sin(particle.twinkle) * 0.3 + 0.7;\n        \n        // Wrap around edges\n        if (particle.x < 0) particle.x = canvas.width;\n        if (particle.x > canvas.width) particle.x = 0;\n        if (particle.y < 0) particle.y = canvas.height;\n        if (particle.y > canvas.height) particle.y = 0;\n        \n        // Draw particle\n        ctx.save();\n        ctx.globalAlpha = particle.opacity * twinkleOpacity;\n        ctx.fillStyle = particle.color;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Add glow effect for larger particles\n        if (particle.size > 1.5) {\n          ctx.shadowBlur = 10;\n          ctx.shadowColor = particle.color;\n          ctx.beginPath();\n          ctx.arc(particle.x, particle.y, particle.size * 0.5, 0, Math.PI * 2);\n          ctx.fill();\n        }\n        \n        ctx.restore();\n      });\n      \n      // Draw connections between nearby particles\n      particlesRef.current.forEach((particle, i) => {\n        particlesRef.current.slice(i + 1).forEach((otherParticle) => {\n          const dx = particle.x - otherParticle.x;\n          const dy = particle.y - otherParticle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < 100) {\n            ctx.save();\n            ctx.globalAlpha = (100 - distance) / 100 * 0.1;\n            ctx.strokeStyle = 'rgba(6, 182, 212, 0.3)';\n            ctx.lineWidth = 0.5;\n            ctx.beginPath();\n            ctx.moveTo(particle.x, particle.y);\n            ctx.lineTo(otherParticle.x, otherParticle.y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        });\n      });\n      \n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"fixed inset-0 w-full h-full pointer-events-none\"\n      style={{ zIndex: 1 }}\n    />\n  );\n};\n\nexport default SpaceBackground;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,SAAS,GAAGL,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMM,YAAY,GAAGN,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMO,YAAY,GAAGP,MAAM,CAAC,EAAE,CAAC;EAE/BD,SAAS,CAAC,MAAM;IACd,MAAMS,MAAM,GAAGH,SAAS,CAACI,OAAO;IAChC,MAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBJ,MAAM,CAACK,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCP,MAAM,CAACQ,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAMC,aAAa,GAAG,GAAG;MAEzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,aAAa,EAAEC,CAAC,EAAE,EAAE;QACtCF,SAAS,CAACG,IAAI,CAAC;UACbC,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGlB,MAAM,CAACK,KAAK;UAC/Bc,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGlB,MAAM,CAACQ,MAAM;UAChCY,IAAI,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG;UAC7BG,MAAM,EAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UACnCI,MAAM,EAAE,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UACnCK,OAAO,EAAEN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAClCM,KAAK,EAAE,QAAQP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,aAAa,GAAG,cAAc,KAAKD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG;UACpGO,OAAO,EAAER,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGD,IAAI,CAACS,EAAE,GAAG,CAAC;UACpCC,YAAY,EAAEV,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG;QACvC,CAAC,CAAC;MACJ;MAEA,OAAON,SAAS;IAClB,CAAC;IAEDb,YAAY,CAACE,OAAO,GAAGU,eAAe,CAAC,CAAC;;IAExC;IACA,MAAMiB,OAAO,GAAGA,CAAA,KAAM;MACpB1B,GAAG,CAAC2B,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE7B,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACQ,MAAM,CAAC;;MAEhD;MACAT,YAAY,CAACE,OAAO,CAAC6B,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QAChD;QACAD,QAAQ,CAACf,CAAC,IAAIe,QAAQ,CAACV,MAAM;QAC7BU,QAAQ,CAACZ,CAAC,IAAIY,QAAQ,CAACT,MAAM;;QAE7B;QACAS,QAAQ,CAACN,OAAO,IAAIM,QAAQ,CAACJ,YAAY;QACzC,MAAMM,cAAc,GAAGhB,IAAI,CAACiB,GAAG,CAACH,QAAQ,CAACN,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG;;QAE7D;QACA,IAAIM,QAAQ,CAACf,CAAC,GAAG,CAAC,EAAEe,QAAQ,CAACf,CAAC,GAAGhB,MAAM,CAACK,KAAK;QAC7C,IAAI0B,QAAQ,CAACf,CAAC,GAAGhB,MAAM,CAACK,KAAK,EAAE0B,QAAQ,CAACf,CAAC,GAAG,CAAC;QAC7C,IAAIe,QAAQ,CAACZ,CAAC,GAAG,CAAC,EAAEY,QAAQ,CAACZ,CAAC,GAAGnB,MAAM,CAACQ,MAAM;QAC9C,IAAIuB,QAAQ,CAACZ,CAAC,GAAGnB,MAAM,CAACQ,MAAM,EAAEuB,QAAQ,CAACZ,CAAC,GAAG,CAAC;;QAE9C;QACAjB,GAAG,CAACiC,IAAI,CAAC,CAAC;QACVjC,GAAG,CAACkC,WAAW,GAAGL,QAAQ,CAACR,OAAO,GAAGU,cAAc;QACnD/B,GAAG,CAACmC,SAAS,GAAGN,QAAQ,CAACP,KAAK;QAC9BtB,GAAG,CAACoC,SAAS,CAAC,CAAC;QACfpC,GAAG,CAACqC,GAAG,CAACR,QAAQ,CAACf,CAAC,EAAEe,QAAQ,CAACZ,CAAC,EAAEY,QAAQ,CAACX,IAAI,EAAE,CAAC,EAAEH,IAAI,CAACS,EAAE,GAAG,CAAC,CAAC;QAC9DxB,GAAG,CAACsC,IAAI,CAAC,CAAC;;QAEV;QACA,IAAIT,QAAQ,CAACX,IAAI,GAAG,GAAG,EAAE;UACvBlB,GAAG,CAACuC,UAAU,GAAG,EAAE;UACnBvC,GAAG,CAACwC,WAAW,GAAGX,QAAQ,CAACP,KAAK;UAChCtB,GAAG,CAACoC,SAAS,CAAC,CAAC;UACfpC,GAAG,CAACqC,GAAG,CAACR,QAAQ,CAACf,CAAC,EAAEe,QAAQ,CAACZ,CAAC,EAAEY,QAAQ,CAACX,IAAI,GAAG,GAAG,EAAE,CAAC,EAAEH,IAAI,CAACS,EAAE,GAAG,CAAC,CAAC;UACpExB,GAAG,CAACsC,IAAI,CAAC,CAAC;QACZ;QAEAtC,GAAG,CAACyC,OAAO,CAAC,CAAC;MACf,CAAC,CAAC;;MAEF;MACA5C,YAAY,CAACE,OAAO,CAAC6B,OAAO,CAAC,CAACC,QAAQ,EAAEjB,CAAC,KAAK;QAC5Cf,YAAY,CAACE,OAAO,CAAC2C,KAAK,CAAC9B,CAAC,GAAG,CAAC,CAAC,CAACgB,OAAO,CAAEe,aAAa,IAAK;UAC3D,MAAMC,EAAE,GAAGf,QAAQ,CAACf,CAAC,GAAG6B,aAAa,CAAC7B,CAAC;UACvC,MAAM+B,EAAE,GAAGhB,QAAQ,CAACZ,CAAC,GAAG0B,aAAa,CAAC1B,CAAC;UACvC,MAAM6B,QAAQ,GAAG/B,IAAI,CAACgC,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;UAE7C,IAAIC,QAAQ,GAAG,GAAG,EAAE;YAClB9C,GAAG,CAACiC,IAAI,CAAC,CAAC;YACVjC,GAAG,CAACkC,WAAW,GAAG,CAAC,GAAG,GAAGY,QAAQ,IAAI,GAAG,GAAG,GAAG;YAC9C9C,GAAG,CAACgD,WAAW,GAAG,wBAAwB;YAC1ChD,GAAG,CAACiD,SAAS,GAAG,GAAG;YACnBjD,GAAG,CAACoC,SAAS,CAAC,CAAC;YACfpC,GAAG,CAACkD,MAAM,CAACrB,QAAQ,CAACf,CAAC,EAAEe,QAAQ,CAACZ,CAAC,CAAC;YAClCjB,GAAG,CAACmD,MAAM,CAACR,aAAa,CAAC7B,CAAC,EAAE6B,aAAa,CAAC1B,CAAC,CAAC;YAC5CjB,GAAG,CAACoD,MAAM,CAAC,CAAC;YACZpD,GAAG,CAACyC,OAAO,CAAC,CAAC;UACf;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF7C,YAAY,CAACG,OAAO,GAAGsD,qBAAqB,CAAC3B,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC;;IAET;IACA,OAAO,MAAM;MACXtB,MAAM,CAACkD,mBAAmB,CAAC,QAAQ,EAAEpD,YAAY,CAAC;MAClD,IAAIN,YAAY,CAACG,OAAO,EAAE;QACxBwD,oBAAoB,CAAC3D,YAAY,CAACG,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEP,OAAA;IACEgE,GAAG,EAAE7D,SAAU;IACf8D,SAAS,EAAC,iDAAiD;IAC3DC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAE;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEN,CAAC;AAACrE,EAAA,CA5HID,eAAe;AAAAuE,EAAA,GAAfvE,eAAe;AA8HrB,eAAeA,eAAe;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}