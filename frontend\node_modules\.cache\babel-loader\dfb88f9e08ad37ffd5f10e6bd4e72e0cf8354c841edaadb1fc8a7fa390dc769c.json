{"ast": null, "code": "/**\n * This function create an object from keys, value and then assign to target\n *\n * @param {Object} obj : the target object to be assigned\n * @param {string[]} keys\n * @param {string | number} value\n *\n * @example\n * const source = {}\n * assignNestedKeys(source, ['palette', 'primary'], 'var(--palette-primary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)' } }\n *\n * @example\n * const source = { palette: { primary: 'var(--palette-primary)' } }\n * assignNestedKeys(source, ['palette', 'secondary'], 'var(--palette-secondary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)', secondary: 'var(--palette-secondary)' } }\n */\nexport const assignNestedKeys = (obj, keys, value, arrayKeys = []) => {\n  let temp = obj;\n  keys.forEach((k, index) => {\n    if (index === keys.length - 1) {\n      if (Array.isArray(temp)) {\n        temp[Number(k)] = value;\n      } else if (temp && typeof temp === 'object') {\n        temp[k] = value;\n      }\n    } else if (temp && typeof temp === 'object') {\n      if (!temp[k]) {\n        temp[k] = arrayKeys.includes(k) ? [] : {};\n      }\n      temp = temp[k];\n    }\n  });\n};\n\n/**\n *\n * @param {Object} obj : source object\n * @param {Function} callback : a function that will be called when\n *                   - the deepest key in source object is reached\n *                   - the value of the deepest key is NOT `undefined` | `null`\n *\n * @example\n * walkObjectDeep({ palette: { primary: { main: '#000000' } } }, console.log)\n * // ['palette', 'primary', 'main'] '#000000'\n */\nexport const walkObjectDeep = (obj, callback, shouldSkipPaths) => {\n  function recurse(object, parentKeys = [], arrayKeys = []) {\n    Object.entries(object).forEach(([key, value]) => {\n      if (!shouldSkipPaths || shouldSkipPaths && !shouldSkipPaths([...parentKeys, key])) {\n        if (value !== undefined && value !== null) {\n          if (typeof value === 'object' && Object.keys(value).length > 0) {\n            recurse(value, [...parentKeys, key], Array.isArray(value) ? [...arrayKeys, key] : arrayKeys);\n          } else {\n            callback([...parentKeys, key], value, arrayKeys);\n          }\n        }\n      }\n    });\n  }\n  recurse(obj);\n};\nconst getCssValue = (keys, value) => {\n  if (typeof value === 'number') {\n    if (['lineHeight', 'fontWeight', 'opacity', 'zIndex'].some(prop => keys.includes(prop))) {\n      // CSS property that are unitless\n      return value;\n    }\n    const lastKey = keys[keys.length - 1];\n    if (lastKey.toLowerCase().indexOf('opacity') >= 0) {\n      // opacity values are unitless\n      return value;\n    }\n    return `${value}px`;\n  }\n  return value;\n};\n\n/**\n * a function that parse theme and return { css, vars }\n *\n * @param {Object} theme\n * @param {{\n *  prefix?: string,\n *  shouldSkipGeneratingVar?: (objectPathKeys: Array<string>, value: string | number) => boolean\n * }} options.\n *  `prefix`: The prefix of the generated CSS variables. This function does not change the value.\n *\n * @returns {{ css: Object, vars: Object }} `css` is the stylesheet, `vars` is an object to get css variable (same structure as theme).\n *\n * @example\n * const { css, vars } = parser({\n *   fontSize: 12,\n *   lineHeight: 1.2,\n *   palette: { primary: { 500: 'var(--color)' } }\n * }, { prefix: 'foo' })\n *\n * console.log(css) // { '--foo-fontSize': '12px', '--foo-lineHeight': 1.2, '--foo-palette-primary-500': 'var(--color)' }\n * console.log(vars) // { fontSize: 'var(--foo-fontSize)', lineHeight: 'var(--foo-lineHeight)', palette: { primary: { 500: 'var(--foo-palette-primary-500)' } } }\n */\nexport default function cssVarsParser(theme, options) {\n  const {\n    prefix,\n    shouldSkipGeneratingVar\n  } = options || {};\n  const css = {};\n  const vars = {};\n  const varsWithDefaults = {};\n  walkObjectDeep(theme, (keys, value, arrayKeys) => {\n    if (typeof value === 'string' || typeof value === 'number') {\n      if (!shouldSkipGeneratingVar || !shouldSkipGeneratingVar(keys, value)) {\n        // only create css & var if `shouldSkipGeneratingVar` return false\n        const cssVar = `--${prefix ? `${prefix}-` : ''}${keys.join('-')}`;\n        Object.assign(css, {\n          [cssVar]: getCssValue(keys, value)\n        });\n        assignNestedKeys(vars, keys, `var(${cssVar})`, arrayKeys);\n        assignNestedKeys(varsWithDefaults, keys, `var(${cssVar}, ${value})`, arrayKeys);\n      }\n    }\n  }, keys => keys[0] === 'vars' // skip 'vars/*' paths\n  );\n  return {\n    css,\n    vars,\n    varsWithDefaults\n  };\n}", "map": {"version": 3, "names": ["assignNestedKeys", "obj", "keys", "value", "arrayKeys", "temp", "for<PERSON>ach", "k", "index", "length", "Array", "isArray", "Number", "includes", "walkObjectDeep", "callback", "shouldSkipPaths", "recurse", "object", "parentKeys", "Object", "entries", "key", "undefined", "getCssValue", "some", "prop", "last<PERSON>ey", "toLowerCase", "indexOf", "cssVarsParser", "theme", "options", "prefix", "shouldSkipGeneratingVar", "css", "vars", "varsWithDefaults", "cssVar", "join", "assign"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/system/esm/cssVars/cssVarsParser.js"], "sourcesContent": ["/**\n * This function create an object from keys, value and then assign to target\n *\n * @param {Object} obj : the target object to be assigned\n * @param {string[]} keys\n * @param {string | number} value\n *\n * @example\n * const source = {}\n * assignNestedKeys(source, ['palette', 'primary'], 'var(--palette-primary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)' } }\n *\n * @example\n * const source = { palette: { primary: 'var(--palette-primary)' } }\n * assignNestedKeys(source, ['palette', 'secondary'], 'var(--palette-secondary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)', secondary: 'var(--palette-secondary)' } }\n */\nexport const assignNestedKeys = (obj, keys, value, arrayKeys = []) => {\n  let temp = obj;\n  keys.forEach((k, index) => {\n    if (index === keys.length - 1) {\n      if (Array.isArray(temp)) {\n        temp[Number(k)] = value;\n      } else if (temp && typeof temp === 'object') {\n        temp[k] = value;\n      }\n    } else if (temp && typeof temp === 'object') {\n      if (!temp[k]) {\n        temp[k] = arrayKeys.includes(k) ? [] : {};\n      }\n      temp = temp[k];\n    }\n  });\n};\n\n/**\n *\n * @param {Object} obj : source object\n * @param {Function} callback : a function that will be called when\n *                   - the deepest key in source object is reached\n *                   - the value of the deepest key is NOT `undefined` | `null`\n *\n * @example\n * walkObjectDeep({ palette: { primary: { main: '#000000' } } }, console.log)\n * // ['palette', 'primary', 'main'] '#000000'\n */\nexport const walkObjectDeep = (obj, callback, shouldSkipPaths) => {\n  function recurse(object, parentKeys = [], arrayKeys = []) {\n    Object.entries(object).forEach(([key, value]) => {\n      if (!shouldSkipPaths || shouldSkipPaths && !shouldSkipPaths([...parentKeys, key])) {\n        if (value !== undefined && value !== null) {\n          if (typeof value === 'object' && Object.keys(value).length > 0) {\n            recurse(value, [...parentKeys, key], Array.isArray(value) ? [...arrayKeys, key] : arrayKeys);\n          } else {\n            callback([...parentKeys, key], value, arrayKeys);\n          }\n        }\n      }\n    });\n  }\n  recurse(obj);\n};\nconst getCssValue = (keys, value) => {\n  if (typeof value === 'number') {\n    if (['lineHeight', 'fontWeight', 'opacity', 'zIndex'].some(prop => keys.includes(prop))) {\n      // CSS property that are unitless\n      return value;\n    }\n    const lastKey = keys[keys.length - 1];\n    if (lastKey.toLowerCase().indexOf('opacity') >= 0) {\n      // opacity values are unitless\n      return value;\n    }\n    return `${value}px`;\n  }\n  return value;\n};\n\n/**\n * a function that parse theme and return { css, vars }\n *\n * @param {Object} theme\n * @param {{\n *  prefix?: string,\n *  shouldSkipGeneratingVar?: (objectPathKeys: Array<string>, value: string | number) => boolean\n * }} options.\n *  `prefix`: The prefix of the generated CSS variables. This function does not change the value.\n *\n * @returns {{ css: Object, vars: Object }} `css` is the stylesheet, `vars` is an object to get css variable (same structure as theme).\n *\n * @example\n * const { css, vars } = parser({\n *   fontSize: 12,\n *   lineHeight: 1.2,\n *   palette: { primary: { 500: 'var(--color)' } }\n * }, { prefix: 'foo' })\n *\n * console.log(css) // { '--foo-fontSize': '12px', '--foo-lineHeight': 1.2, '--foo-palette-primary-500': 'var(--color)' }\n * console.log(vars) // { fontSize: 'var(--foo-fontSize)', lineHeight: 'var(--foo-lineHeight)', palette: { primary: { 500: 'var(--foo-palette-primary-500)' } } }\n */\nexport default function cssVarsParser(theme, options) {\n  const {\n    prefix,\n    shouldSkipGeneratingVar\n  } = options || {};\n  const css = {};\n  const vars = {};\n  const varsWithDefaults = {};\n  walkObjectDeep(theme, (keys, value, arrayKeys) => {\n    if (typeof value === 'string' || typeof value === 'number') {\n      if (!shouldSkipGeneratingVar || !shouldSkipGeneratingVar(keys, value)) {\n        // only create css & var if `shouldSkipGeneratingVar` return false\n        const cssVar = `--${prefix ? `${prefix}-` : ''}${keys.join('-')}`;\n        Object.assign(css, {\n          [cssVar]: getCssValue(keys, value)\n        });\n        assignNestedKeys(vars, keys, `var(${cssVar})`, arrayKeys);\n        assignNestedKeys(varsWithDefaults, keys, `var(${cssVar}, ${value})`, arrayKeys);\n      }\n    }\n  }, keys => keys[0] === 'vars' // skip 'vars/*' paths\n  );\n  return {\n    css,\n    vars,\n    varsWithDefaults\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,gBAAgB,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,GAAG,EAAE,KAAK;EACpE,IAAIC,IAAI,GAAGJ,GAAG;EACdC,IAAI,CAACI,OAAO,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;IACzB,IAAIA,KAAK,KAAKN,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAIC,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC,EAAE;QACvBA,IAAI,CAACO,MAAM,CAACL,CAAC,CAAC,CAAC,GAAGJ,KAAK;MACzB,CAAC,MAAM,IAAIE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC3CA,IAAI,CAACE,CAAC,CAAC,GAAGJ,KAAK;MACjB;IACF,CAAC,MAAM,IAAIE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC3C,IAAI,CAACA,IAAI,CAACE,CAAC,CAAC,EAAE;QACZF,IAAI,CAACE,CAAC,CAAC,GAAGH,SAAS,CAACS,QAAQ,CAACN,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC3C;MACAF,IAAI,GAAGA,IAAI,CAACE,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,cAAc,GAAGA,CAACb,GAAG,EAAEc,QAAQ,EAAEC,eAAe,KAAK;EAChE,SAASC,OAAOA,CAACC,MAAM,EAAEC,UAAU,GAAG,EAAE,EAAEf,SAAS,GAAG,EAAE,EAAE;IACxDgB,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC,CAACZ,OAAO,CAAC,CAAC,CAACgB,GAAG,EAAEnB,KAAK,CAAC,KAAK;MAC/C,IAAI,CAACa,eAAe,IAAIA,eAAe,IAAI,CAACA,eAAe,CAAC,CAAC,GAAGG,UAAU,EAAEG,GAAG,CAAC,CAAC,EAAE;QACjF,IAAInB,KAAK,KAAKoB,SAAS,IAAIpB,KAAK,KAAK,IAAI,EAAE;UACzC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIiB,MAAM,CAAClB,IAAI,CAACC,KAAK,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;YAC9DQ,OAAO,CAACd,KAAK,EAAE,CAAC,GAAGgB,UAAU,EAAEG,GAAG,CAAC,EAAEZ,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,GAAG,CAAC,GAAGC,SAAS,EAAEkB,GAAG,CAAC,GAAGlB,SAAS,CAAC;UAC9F,CAAC,MAAM;YACLW,QAAQ,CAAC,CAAC,GAAGI,UAAU,EAAEG,GAAG,CAAC,EAAEnB,KAAK,EAAEC,SAAS,CAAC;UAClD;QACF;MACF;IACF,CAAC,CAAC;EACJ;EACAa,OAAO,CAAChB,GAAG,CAAC;AACd,CAAC;AACD,MAAMuB,WAAW,GAAGA,CAACtB,IAAI,EAAEC,KAAK,KAAK;EACnC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC,CAACsB,IAAI,CAACC,IAAI,IAAIxB,IAAI,CAACW,QAAQ,CAACa,IAAI,CAAC,CAAC,EAAE;MACvF;MACA,OAAOvB,KAAK;IACd;IACA,MAAMwB,OAAO,GAAGzB,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG,CAAC,CAAC;IACrC,IAAIkB,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;MACjD;MACA,OAAO1B,KAAK;IACd;IACA,OAAO,GAAGA,KAAK,IAAI;EACrB;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAAS2B,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACpD,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMG,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3BvB,cAAc,CAACiB,KAAK,EAAE,CAAC7B,IAAI,EAAEC,KAAK,EAAEC,SAAS,KAAK;IAChD,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC1D,IAAI,CAAC+B,uBAAuB,IAAI,CAACA,uBAAuB,CAAChC,IAAI,EAAEC,KAAK,CAAC,EAAE;QACrE;QACA,MAAMmC,MAAM,GAAG,KAAKL,MAAM,GAAG,GAAGA,MAAM,GAAG,GAAG,EAAE,GAAG/B,IAAI,CAACqC,IAAI,CAAC,GAAG,CAAC,EAAE;QACjEnB,MAAM,CAACoB,MAAM,CAACL,GAAG,EAAE;UACjB,CAACG,MAAM,GAAGd,WAAW,CAACtB,IAAI,EAAEC,KAAK;QACnC,CAAC,CAAC;QACFH,gBAAgB,CAACoC,IAAI,EAAElC,IAAI,EAAE,OAAOoC,MAAM,GAAG,EAAElC,SAAS,CAAC;QACzDJ,gBAAgB,CAACqC,gBAAgB,EAAEnC,IAAI,EAAE,OAAOoC,MAAM,KAAKnC,KAAK,GAAG,EAAEC,SAAS,CAAC;MACjF;IACF;EACF,CAAC,EAAEF,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;EAC9B,CAAC;EACD,OAAO;IACLiC,GAAG;IACHC,IAAI;IACJC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}