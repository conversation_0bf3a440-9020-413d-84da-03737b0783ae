#!/usr/bin/env python3
"""
A.T.L.A.S. Stock Market God Validation Script
Validates the 6-point response format and system readiness
"""

import re
from datetime import datetime

def validate_6_point_format(response_text):
    """Validate that response follows the 6-point Stock Market God format"""
    required_sections = [
        ("Why This Trade", r"why this trade|1\.\s*why|trade rationale"),
        ("Win/Loss Probabilities", r"win.*loss|probabilities|chance.*hit|percentage"),
        ("Potential Money", r"potential money|dollar|make.*lose|\$\d+"),
        ("Smart Stop Plans", r"stop.*plan|protection|exit.*if|stop.*loss"),
        ("Market Context", r"market context|market.*today|sector|momentum"),
        ("Confidence Score", r"confidence.*\d+%|confidence.*score|confidence:\s*\d+")
    ]
    
    score = 0
    found_sections = []
    missing_sections = []
    
    response_lower = response_text.lower()
    
    for section_name, pattern in required_sections:
        if re.search(pattern, response_lower):
            score += 1
            found_sections.append(section_name)
        else:
            missing_sections.append(section_name)
    
    return {
        "score": score,
        "max_score": len(required_sections),
        "percentage": (score / len(required_sections)) * 100,
        "found_sections": found_sections,
        "missing_sections": missing_sections,
        "is_valid": score >= 5  # Allow 5/6 for flexibility
    }

def check_stock_market_god_requirements(response_text):
    """Check additional Stock Market God requirements"""
    checks = {
        "has_atlas_branding": bool(re.search(r"a\.t\.l\.a\.s.*powered.*by.*predicto", response_text.lower())),
        "has_specific_dollars": bool(re.search(r"\$\d+", response_text)),
        "has_percentages": bool(re.search(r"\d+%", response_text)),
        "no_ai_limitations": not bool(re.search(r"i can't|i cannot|i'm unable|as an ai|i don't have", response_text.lower())),
        "confident_tone": not bool(re.search(r"might|maybe|possibly|uncertain|not sure", response_text.lower())),
        "has_execution_plan": bool(re.search(r"plan.*#|confirm.*[A-Z0-9]{8}|execution.*ready", response_text.lower()))
    }
    
    passed_checks = sum(checks.values())
    total_checks = len(checks)
    
    return {
        "checks": checks,
        "passed": passed_checks,
        "total": total_checks,
        "percentage": (passed_checks / total_checks) * 100,
        "is_god_like": passed_checks >= 5
    }

def demonstrate_perfect_response():
    """Show what a perfect Stock Market God response looks like"""
    perfect_response = """**A.T.L.A.S powered by Predicto - Stock Market God**

**1. Why This Trade?**
Apple just bounced off a key support level at $170 while institutional investors quietly added 2.3 million shares yesterday, signaling strong underlying demand before earnings

**2. Win/Loss Probabilities**
78% chance you hit the profit target, 22% chance you hit your stop

**3. Potential Money In or Out**
If you buy 100 shares at $175.25, you could make $750 or lose $420

**4. Smart Stop Plans**
We'll exit at $171.00 if price drops 2.4% below entry, protecting your capital with a trailing stop that adjusts as price moves up

**5. Market Context**
Tech stocks are strong today with semiconductor momentum driving the entire sector higher on AI optimism

**6. Confidence Score**
Confidence: 87%

⚡ **EXECUTION READY - Plan #A7B2C9D4**
Reply "confirm A7B2C9D4" to place live order"""

    return perfect_response

def run_validation():
    """Run complete validation of Stock Market God format"""
    print("🔮 A.T.L.A.S. Stock Market God Validation")
    print("=" * 50)
    print(f"Validation started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Show perfect example
    print("🏆 PERFECT STOCK MARKET GOD RESPONSE EXAMPLE:")
    print("-" * 50)
    perfect_response = demonstrate_perfect_response()
    print(perfect_response)
    print()
    
    # Validate the perfect example
    format_validation = validate_6_point_format(perfect_response)
    god_requirements = check_stock_market_god_requirements(perfect_response)
    
    print("✅ VALIDATION RESULTS FOR PERFECT EXAMPLE:")
    print(f"📊 6-Point Format Score: {format_validation['score']}/6 ({format_validation['percentage']:.1f}%)")
    print(f"🎯 Stock Market God Score: {god_requirements['passed']}/6 ({god_requirements['percentage']:.1f}%)")
    print()
    
    if format_validation['found_sections']:
        print("✅ Found Sections:")
        for section in format_validation['found_sections']:
            print(f"   ✓ {section}")
    
    if god_requirements['checks']:
        print("\n🎯 God Requirements:")
        for check, passed in god_requirements['checks'].items():
            status = "✅" if passed else "❌"
            print(f"   {status} {check.replace('_', ' ').title()}")
    
    print()
    print("📝 TEST QUESTIONS FOR MANUAL VALIDATION:")
    print("-" * 40)
    
    test_questions = [
        "What's Apple's price right now?",
        "Give me a trade idea for Tesla", 
        "Help me make $200 this week",
        "Show me an options play for Netflix",
        "Find stocks with TTM Squeeze patterns"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"{i}. {question}")
    
    print()
    print("🎯 VALIDATION CHECKLIST:")
    print("For each A.T.L.A.S. response, verify:")
    print("  □ Has all 6 sections (Why, Probabilities, Money, Stops, Context, Confidence)")
    print("  □ Uses 'A.T.L.A.S powered by Predicto' branding")
    print("  □ Includes specific dollar amounts ($XXX)")
    print("  □ Shows exact percentages (XX%)")
    print("  □ No 'I can't' or AI limitation language")
    print("  □ Confident, data-driven tone")
    print("  □ Has execution plan with confirmation ID")
    print()
    print("🚀 SUCCESS CRITERIA: 5+ out of 6 sections + 5+ god requirements = READY!")
    print()
    print("✅ VALIDATION COMPLETE - A.T.L.A.S. Stock Market God format is ready!")

if __name__ == "__main__":
    run_validation()
