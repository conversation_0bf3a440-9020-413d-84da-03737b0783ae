{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Instagram = createLucideIcon(\"Instagram\", [[\"rect\", {\n  width: \"20\",\n  height: \"20\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"5\",\n  ry: \"5\",\n  key: \"2e1cvw\"\n}], [\"path\", {\n  d: \"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\",\n  key: \"9exkf1\"\n}], [\"line\", {\n  x1: \"17.5\",\n  x2: \"17.51\",\n  y1: \"6.5\",\n  y2: \"6.5\",\n  key: \"r4j83e\"\n}]]);\nexport { Instagram as default };", "map": {"version": 3, "names": ["Instagram", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\instagram.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Instagram\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHg9IjIiIHk9IjIiIHJ4PSI1IiByeT0iNSIgLz4KICA8cGF0aCBkPSJNMTYgMTEuMzdBNCA0IDAgMSAxIDEyLjYzIDggNCA0IDAgMCAxIDE2IDExLjM3eiIgLz4KICA8bGluZSB4MT0iMTcuNSIgeDI9IjE3LjUxIiB5MT0iNi41IiB5Mj0iNi41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/instagram\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Instagram = createLucideIcon('Instagram', [\n  ['rect', { width: '20', height: '20', x: '2', y: '2', rx: '5', ry: '5', key: '2e1cvw' }],\n  ['path', { d: 'M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z', key: '9exkf1' }],\n  ['line', { x1: '17.5', x2: '17.51', y1: '6.5', y2: '6.5', key: 'r4j83e' }],\n]);\n\nexport default Instagram;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,MAAQ;EAAEC,CAAA,EAAG,iDAAmD;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,QAAQ;EAAEE,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,KAAO;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}