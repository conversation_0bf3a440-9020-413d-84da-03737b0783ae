{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{Paper,TextField,Button,Box,Typography,List,ListItem,Divider,Alert,CircularProgress,Card,CardContent,CardActions,Chip,Grid,IconButton,Tooltip}from'@mui/material';import{Send as SendIcon,SmartToy as HollyIcon,Person as PersonIcon,TrendingUp as TrendingUpIcon,Assessment as AssessmentIcon,AccountBalance as AccountBalanceIcon,Refresh as RefreshIcon,Help as HelpIcon}from'@mui/icons-material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HollyChat=_ref=>{let{onPlanExecuted}=_ref;const[messages,setMessages]=useState([{id:1,type:'assistant',content:\"Hi! I'm <PERSON>, your personal trading assistant. I can help you with anything trading-related - just ask me in plain English!\\n\\nTry asking me:\\n• \\\"Make me $50 today\\\"\\n• \\\"What's AAPL looking like?\\\"\\n• \\\"Find me some momentum plays\\\"\\n• \\\"I need a hedge for my Tesla position\\\"\",timestamp:new Date(),response_type:'chat'}]);const[inputMessage,setInputMessage]=useState('');const[isLoading,setIsLoading]=useState(false);const[quickActions]=useState([{label:\"Make me $50\",message:\"Make me $50 today\"},{label:\"Find opportunities\",message:\"Find me some good trading opportunities\"},{label:\"Market overview\",message:\"What's the market looking like today?\"},{label:\"TTM Squeeze\",message:\"Show me some TTM Squeeze setups\"}]);const messagesEndRef=useRef(null);const scrollToBottom=()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:\"smooth\"});};useEffect(()=>{scrollToBottom();},[messages]);const handleSendMessage=async function(){let messageText=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;const messageToSend=messageText||inputMessage;if(!messageToSend.trim()||isLoading)return;const userMessage={id:Date.now(),type:'user',content:messageToSend,timestamp:new Date()};setMessages(prev=>[...prev,userMessage]);setInputMessage('');setIsLoading(true);try{const response=await fetch('/api/v1/holly/chat',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({message:messageToSend,user_context:{timestamp:new Date().toISOString(),session_id:'web_session'}})});const data=await response.json();const hollyMessage={id:Date.now()+1,type:'assistant',content:data.response,timestamp:new Date(),response_type:data.type,requires_action:data.requires_action,trading_plan:data.trading_plan,plan_id:data.plan_id,function_called:data.function_called};setMessages(prev=>[...prev,hollyMessage]);}catch(error){const errorMessage={id:Date.now()+1,type:'assistant',content:'Sorry, I encountered an error. Please try again or rephrase your request.',timestamp:new Date(),response_type:'error',error:true};setMessages(prev=>[...prev,errorMessage]);}finally{setIsLoading(false);}};const handleExecutePlan=async planId=>{setIsLoading(true);try{const response=await fetch('/api/v1/holly/execute',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({plan_id:planId,confirm:true})});const data=await response.json();if(data.success){const successMessage={id:Date.now(),type:'assistant',content:\"\\u2705 \".concat(data.message,\"\\n\\nExecuted orders:\\n\").concat(data.executed_orders.map(order=>\"\\u2022 \".concat(order.side.toUpperCase(),\" \").concat(order.quantity,\" \").concat(order.symbol)).join('\\n')),timestamp:new Date(),response_type:'execution_result'};setMessages(prev=>[...prev,successMessage]);if(onPlanExecuted){onPlanExecuted();}}else{throw new Error(data.message||'Failed to execute plan');}}catch(error){const errorMessage={id:Date.now(),type:'assistant',content:\"\\u274C Failed to execute plan: \".concat(error.message),timestamp:new Date(),response_type:'error',error:true};setMessages(prev=>[...prev,errorMessage]);}finally{setIsLoading(false);}};const handleKeyPress=event=>{if(event.key==='Enter'&&!event.shiftKey){event.preventDefault();handleSendMessage();}};const formatTime=timestamp=>{return timestamp.toLocaleTimeString([],{hour:'2-digit',minute:'2-digit'});};const getResponseIcon=responseType=>{switch(responseType){case'trading_plan':return/*#__PURE__*/_jsx(TrendingUpIcon,{fontSize:\"small\",color:\"primary\"});case'market_analysis':return/*#__PURE__*/_jsx(AssessmentIcon,{fontSize:\"small\",color:\"info\"});case'market_data':return/*#__PURE__*/_jsx(AccountBalanceIcon,{fontSize:\"small\",color:\"success\"});default:return/*#__PURE__*/_jsx(HollyIcon,{fontSize:\"small\",color:\"primary\"});}};const renderTradingPlan=message=>{var _plan$summary,_plan$summary2,_plan$summary3,_plan$summary4;if(!message.trading_plan)return null;const plan=message.trading_plan;return/*#__PURE__*/_jsxs(Card,{sx:{mt:1,backgroundColor:'#e8f5e8',border:'1px solid #4caf50'},children:[/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,mb:1},children:[/*#__PURE__*/_jsx(TrendingUpIcon,{color:\"success\"}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"success.main\",children:\"Trading Plan Generated\"})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Trades:\"}),\" \",((_plan$summary=plan.summary)===null||_plan$summary===void 0?void 0:_plan$summary.total_trades)||0]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Risk:\"}),\" $\",((_plan$summary2=plan.summary)===null||_plan$summary2===void 0?void 0:_plan$summary2.total_risk)||0]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Expected Return:\"}),\" $\",((_plan$summary3=plan.summary)===null||_plan$summary3===void 0?void 0:_plan$summary3.expected_return)||0]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Risk/Reward:\"}),\" \",((_plan$summary4=plan.summary)===null||_plan$summary4===void 0?void 0:_plan$summary4.risk_reward_ratio)||0,\":1\"]})})]}),plan.trades&&plan.trades.length>0&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontWeight:'bold',mb:1},children:\"Planned Trades:\"}),plan.trades.map((trade,index)=>/*#__PURE__*/_jsxs(Box,{sx:{mb:1,p:1,backgroundColor:'rgba(255,255,255,0.7)',borderRadius:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:trade.action.toUpperCase()}),\" \",trade.quantity,\" \",trade.symbol,\" @ $\",trade.entry_price]}),trade.stop_price&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"error\",children:[\"Stop: $\",trade.stop_price]}),trade.target_price&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"success\",sx:{ml:2},children:[\"Target: $\",trade.target_price]})]},index))]})]}),/*#__PURE__*/_jsx(CardActions,{children:/*#__PURE__*/_jsx(Button,{size:\"small\",variant:\"contained\",color:\"success\",onClick:()=>handleExecutePlan(message.plan_id),disabled:isLoading,startIcon:/*#__PURE__*/_jsx(TrendingUpIcon,{}),children:\"Execute Plan (Paper Trading)\"})})]});};return/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{height:'700px',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Box,{sx:{p:2,backgroundColor:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',color:'white',borderBottom:'1px solid #ddd'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(HollyIcon,{}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Holly AI\"}),/*#__PURE__*/_jsx(Chip,{label:\"Paper Trading\",size:\"small\",color:\"warning\"})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Tooltip,{title:\"Get help\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",sx:{color:'white'},onClick:()=>handleSendMessage(\"What can you help me with?\"),children:/*#__PURE__*/_jsx(HelpIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Reset conversation\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",sx:{color:'white'},onClick:async()=>{try{await fetch('/api/v1/holly/reset',{method:'POST'});setMessages([{id:1,type:'assistant',content:\"Hi! I'm Holly AI. How can I help you with trading today?\",timestamp:new Date(),response_type:'chat'}]);}catch(error){console.error('Failed to reset conversation:',error);}},children:/*#__PURE__*/_jsx(RefreshIcon,{})})})]})]})}),/*#__PURE__*/_jsx(Box,{sx:{p:1,backgroundColor:'#f8f9fa',borderBottom:'1px solid #eee'},children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',gap:1,flexWrap:'wrap'},children:quickActions.map((action,index)=>/*#__PURE__*/_jsx(Chip,{label:action.label,size:\"small\",onClick:()=>handleSendMessage(action.message),sx:{cursor:'pointer'},variant:\"outlined\"},index))})}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1,overflow:'auto',p:1},children:[/*#__PURE__*/_jsxs(List,{children:[messages.map(message=>/*#__PURE__*/_jsxs(ListItem,{sx:{flexDirection:'column',alignItems:'stretch',py:1},children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:message.type==='user'?'flex-end':'flex-start',width:'100%'},children:/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:'85%',backgroundColor:message.type==='user'?'#1976d2':'#f5f5f5',color:message.type==='user'?'white':'black',borderRadius:2,p:1.5,mb:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,mb:0.5},children:[message.type==='user'?/*#__PURE__*/_jsx(PersonIcon,{fontSize:\"small\"}):getResponseIcon(message.response_type),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",children:formatTime(message.timestamp)}),message.function_called&&/*#__PURE__*/_jsx(Chip,{label:message.function_called,size:\"small\",variant:\"outlined\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{whiteSpace:'pre-wrap'},children:message.content}),message.error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mt:1},children:\"There was an error processing your request.\"})]})}),renderTradingPlan(message)]},message.id)),isLoading&&/*#__PURE__*/_jsx(ListItem,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(CircularProgress,{size:20}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Holly is thinking...\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(Box,{sx:{p:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,multiline:true,maxRows:3,placeholder:\"Ask Holly anything about trading... (e.g., 'Make me $50 today')\",value:inputMessage,onChange:e=>setInputMessage(e.target.value),onKeyPress:handleKeyPress,disabled:isLoading,variant:\"outlined\",size:\"small\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:()=>handleSendMessage(),disabled:!inputMessage.trim()||isLoading,sx:{minWidth:'auto',px:2},children:/*#__PURE__*/_jsx(SendIcon,{})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{mt:0.5,display:'block'},children:\"Holly understands natural language - just ask what you want to do!\"})]})]});};export default HollyChat;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Paper", "TextField", "<PERSON><PERSON>", "Box", "Typography", "List", "ListItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Chip", "Grid", "IconButton", "<PERSON><PERSON><PERSON>", "Send", "SendIcon", "SmartToy", "HollyIcon", "Person", "PersonIcon", "TrendingUp", "TrendingUpIcon", "Assessment", "AssessmentIcon", "AccountBalance", "AccountBalanceIcon", "Refresh", "RefreshIcon", "Help", "HelpIcon", "jsx", "_jsx", "jsxs", "_jsxs", "HollyChat", "_ref", "onPlanExecuted", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "response_type", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "quickActions", "label", "message", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "messageText", "arguments", "length", "undefined", "messageToSend", "trim", "userMessage", "now", "prev", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "user_context", "toISOString", "session_id", "data", "json", "hollyMessage", "requires_action", "trading_plan", "plan_id", "function_called", "error", "errorMessage", "handleExecutePlan", "planId", "confirm", "success", "successMessage", "concat", "executed_orders", "map", "order", "side", "toUpperCase", "quantity", "symbol", "join", "Error", "handleKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "formatTime", "toLocaleTimeString", "hour", "minute", "getResponseIcon", "responseType", "fontSize", "color", "renderTradingPlan", "_plan$summary", "_plan$summary2", "_plan$summary3", "_plan$summary4", "plan", "sx", "mt", "backgroundColor", "border", "children", "display", "alignItems", "gap", "mb", "variant", "container", "spacing", "item", "xs", "summary", "total_trades", "total_risk", "expected_return", "risk_reward_ratio", "trades", "fontWeight", "trade", "index", "p", "borderRadius", "action", "entry_price", "stop_price", "target_price", "ml", "size", "onClick", "disabled", "startIcon", "elevation", "height", "flexDirection", "background", "borderBottom", "justifyContent", "title", "console", "flexWrap", "cursor", "flex", "overflow", "py", "width", "max<PERSON><PERSON><PERSON>", "whiteSpace", "severity", "ref", "fullWidth", "multiline", "maxRows", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "min<PERSON><PERSON><PERSON>", "px"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/HollyChat.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Paper,\n  TextField,\n  Button,\n  Box,\n  Typography,\n  List,\n  ListItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  Card,\n  CardContent,\n  CardActions,\n  Chip,\n  Grid,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  Send as SendIcon,\n  SmartToy as HollyI<PERSON>,\n  Person as PersonIcon,\n  TrendingUp as TrendingUpIcon,\n  Assessment as AssessmentIcon,\n  AccountBalance as AccountBalanceIcon,\n  Refresh as RefreshIcon,\n  Help as HelpIcon\n} from '@mui/icons-material';\n\nconst HollyChat = ({ onPlanExecuted }) => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: 'assistant',\n      content: \"Hi! I'm <PERSON>, your personal trading assistant. I can help you with anything trading-related - just ask me in plain English!\\n\\nTry asking me:\\n• \\\"Make me $50 today\\\"\\n• \\\"What's AAPL looking like?\\\"\\n• \\\"Find me some momentum plays\\\"\\n• \\\"I need a hedge for my Tesla position\\\"\",\n      timestamp: new Date(),\n      response_type: 'chat'\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [quickActions] = useState([\n    { label: \"Make me $50\", message: \"Make me $50 today\" },\n    { label: \"Find opportunities\", message: \"Find me some good trading opportunities\" },\n    { label: \"Market overview\", message: \"What's the market looking like today?\" },\n    { label: \"TTM Squeeze\", message: \"Show me some TTM Squeeze setups\" }\n  ]);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async (messageText = null) => {\n    const messageToSend = messageText || inputMessage;\n    if (!messageToSend.trim() || isLoading) return;\n\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageToSend,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/v1/holly/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageToSend,\n          user_context: {\n            timestamp: new Date().toISOString(),\n            session_id: 'web_session'\n          }\n        })\n      });\n\n      const data = await response.json();\n\n      const hollyMessage = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: data.response,\n        timestamp: new Date(),\n        response_type: data.type,\n        requires_action: data.requires_action,\n        trading_plan: data.trading_plan,\n        plan_id: data.plan_id,\n        function_called: data.function_called\n      };\n\n      setMessages(prev => [...prev, hollyMessage]);\n\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again or rephrase your request.',\n        timestamp: new Date(),\n        response_type: 'error',\n        error: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleExecutePlan = async (planId) => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/v1/holly/execute', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          plan_id: planId,\n          confirm: true\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        const successMessage = {\n          id: Date.now(),\n          type: 'assistant',\n          content: `✅ ${data.message}\\n\\nExecuted orders:\\n${data.executed_orders.map(order => \n            `• ${order.side.toUpperCase()} ${order.quantity} ${order.symbol}`\n          ).join('\\n')}`,\n          timestamp: new Date(),\n          response_type: 'execution_result'\n        };\n        setMessages(prev => [...prev, successMessage]);\n        \n        if (onPlanExecuted) {\n          onPlanExecuted();\n        }\n      } else {\n        throw new Error(data.message || 'Failed to execute plan');\n      }\n\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now(),\n        type: 'assistant',\n        content: `❌ Failed to execute plan: ${error.message}`,\n        timestamp: new Date(),\n        response_type: 'error',\n        error: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatTime = (timestamp) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  const getResponseIcon = (responseType) => {\n    switch (responseType) {\n      case 'trading_plan':\n        return <TrendingUpIcon fontSize=\"small\" color=\"primary\" />;\n      case 'market_analysis':\n        return <AssessmentIcon fontSize=\"small\" color=\"info\" />;\n      case 'market_data':\n        return <AccountBalanceIcon fontSize=\"small\" color=\"success\" />;\n      default:\n        return <HollyIcon fontSize=\"small\" color=\"primary\" />;\n    }\n  };\n\n  const renderTradingPlan = (message) => {\n    if (!message.trading_plan) return null;\n\n    const plan = message.trading_plan;\n    \n    return (\n      <Card sx={{ mt: 1, backgroundColor: '#e8f5e8', border: '1px solid #4caf50' }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>\n            <TrendingUpIcon color=\"success\" />\n            <Typography variant=\"subtitle2\" color=\"success.main\">\n              Trading Plan Generated\n            </Typography>\n          </Box>\n          \n          <Grid container spacing={2}>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Trades:</strong> {plan.summary?.total_trades || 0}\n              </Typography>\n            </Grid>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Total Risk:</strong> ${plan.summary?.total_risk || 0}\n              </Typography>\n            </Grid>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Expected Return:</strong> ${plan.summary?.expected_return || 0}\n              </Typography>\n            </Grid>\n            <Grid item xs={6}>\n              <Typography variant=\"body2\">\n                <strong>Risk/Reward:</strong> {plan.summary?.risk_reward_ratio || 0}:1\n              </Typography>\n            </Grid>\n          </Grid>\n\n          {plan.trades && plan.trades.length > 0 && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                Planned Trades:\n              </Typography>\n              {plan.trades.map((trade, index) => (\n                <Box key={index} sx={{ mb: 1, p: 1, backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 1 }}>\n                  <Typography variant=\"body2\">\n                    <strong>{trade.action.toUpperCase()}</strong> {trade.quantity} {trade.symbol} @ ${trade.entry_price}\n                  </Typography>\n                  {trade.stop_price && (\n                    <Typography variant=\"caption\" color=\"error\">\n                      Stop: ${trade.stop_price}\n                    </Typography>\n                  )}\n                  {trade.target_price && (\n                    <Typography variant=\"caption\" color=\"success\" sx={{ ml: 2 }}>\n                      Target: ${trade.target_price}\n                    </Typography>\n                  )}\n                </Box>\n              ))}\n            </Box>\n          )}\n        </CardContent>\n        <CardActions>\n          <Button \n            size=\"small\" \n            variant=\"contained\" \n            color=\"success\"\n            onClick={() => handleExecutePlan(message.plan_id)}\n            disabled={isLoading}\n            startIcon={<TrendingUpIcon />}\n          >\n            Execute Plan (Paper Trading)\n          </Button>\n        </CardActions>\n      </Card>\n    );\n  };\n\n  return (\n    <Paper elevation={3} sx={{ height: '700px', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Box sx={{ \n        p: 2, \n        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        color: 'white',\n        borderBottom: '1px solid #ddd' \n      }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <HollyIcon />\n            <Typography variant=\"h6\">Holly AI</Typography>\n            <Chip label=\"Paper Trading\" size=\"small\" color=\"warning\" />\n          </Box>\n          <Box>\n            <Tooltip title=\"Get help\">\n              <IconButton \n                size=\"small\" \n                sx={{ color: 'white' }}\n                onClick={() => handleSendMessage(\"What can you help me with?\")}\n              >\n                <HelpIcon />\n              </IconButton>\n            </Tooltip>\n            <Tooltip title=\"Reset conversation\">\n              <IconButton \n                size=\"small\" \n                sx={{ color: 'white' }}\n                onClick={async () => {\n                  try {\n                    await fetch('/api/v1/holly/reset', { method: 'POST' });\n                    setMessages([{\n                      id: 1,\n                      type: 'assistant',\n                      content: \"Hi! I'm Holly AI. How can I help you with trading today?\",\n                      timestamp: new Date(),\n                      response_type: 'chat'\n                    }]);\n                  } catch (error) {\n                    console.error('Failed to reset conversation:', error);\n                  }\n                }}\n              >\n                <RefreshIcon />\n              </IconButton>\n            </Tooltip>\n          </Box>\n        </Box>\n      </Box>\n\n      {/* Quick Actions */}\n      <Box sx={{ p: 1, backgroundColor: '#f8f9fa', borderBottom: '1px solid #eee' }}>\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {quickActions.map((action, index) => (\n            <Chip\n              key={index}\n              label={action.label}\n              size=\"small\"\n              onClick={() => handleSendMessage(action.message)}\n              sx={{ cursor: 'pointer' }}\n              variant=\"outlined\"\n            />\n          ))}\n        </Box>\n      </Box>\n\n      {/* Messages */}\n      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>\n        <List>\n          {messages.map((message) => (\n            <ListItem key={message.id} sx={{ flexDirection: 'column', alignItems: 'stretch', py: 1 }}>\n              <Box sx={{ \n                display: 'flex', \n                justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n                width: '100%'\n              }}>\n                <Box sx={{ \n                  maxWidth: '85%',\n                  backgroundColor: message.type === 'user' ? '#1976d2' : '#f5f5f5',\n                  color: message.type === 'user' ? 'white' : 'black',\n                  borderRadius: 2,\n                  p: 1.5,\n                  mb: 1\n                }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\n                    {message.type === 'user' ? <PersonIcon fontSize=\"small\" /> : getResponseIcon(message.response_type)}\n                    <Typography variant=\"caption\">\n                      {formatTime(message.timestamp)}\n                    </Typography>\n                    {message.function_called && (\n                      <Chip label={message.function_called} size=\"small\" variant=\"outlined\" />\n                    )}\n                  </Box>\n                  <Typography variant=\"body2\" sx={{ whiteSpace: 'pre-wrap' }}>\n                    {message.content}\n                  </Typography>\n                  \n                  {message.error && (\n                    <Alert severity=\"error\" sx={{ mt: 1 }}>\n                      There was an error processing your request.\n                    </Alert>\n                  )}\n                </Box>\n              </Box>\n\n              {/* Render trading plan if present */}\n              {renderTradingPlan(message)}\n            </ListItem>\n          ))}\n          {isLoading && (\n            <ListItem>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <CircularProgress size={20} />\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Holly is thinking...\n                </Typography>\n              </Box>\n            </ListItem>\n          )}\n        </List>\n        <div ref={messagesEndRef} />\n      </Box>\n\n      <Divider />\n\n      {/* Input */}\n      <Box sx={{ p: 2 }}>\n        <Box sx={{ display: 'flex', gap: 1 }}>\n          <TextField\n            fullWidth\n            multiline\n            maxRows={3}\n            placeholder=\"Ask Holly anything about trading... (e.g., 'Make me $50 today')\"\n            value={inputMessage}\n            onChange={(e) => setInputMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            disabled={isLoading}\n            variant=\"outlined\"\n            size=\"small\"\n          />\n          <Button\n            variant=\"contained\"\n            onClick={() => handleSendMessage()}\n            disabled={!inputMessage.trim() || isLoading}\n            sx={{ minWidth: 'auto', px: 2 }}\n          >\n            <SendIcon />\n          </Button>\n        </Box>\n        <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 0.5, display: 'block' }}>\n          Holly understands natural language - just ask what you want to do!\n        </Typography>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default HollyChat;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OACEC,KAAK,CACLC,SAAS,CACTC,MAAM,CACNC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,KAAK,CACLC,gBAAgB,CAChBC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,IAAI,CACJC,IAAI,CACJC,UAAU,CACVC,OAAO,KACF,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,QAAQ,GAAI,CAAAC,SAAS,CACrBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,cAAc,GAAI,CAAAC,kBAAkB,CACpCC,OAAO,GAAI,CAAAC,WAAW,CACtBC,IAAI,GAAI,CAAAC,QAAQ,KACX,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7B,KAAM,CAAAC,SAAS,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,cAAe,CAAC,CAAAD,IAAA,CACnC,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAG5C,QAAQ,CAAC,CACvC,CACE6C,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAE,4RAA4R,CACrSC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,aAAa,CAAE,MACjB,CAAC,CACF,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqD,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACuD,YAAY,CAAC,CAAGvD,QAAQ,CAAC,CAC9B,CAAEwD,KAAK,CAAE,aAAa,CAAEC,OAAO,CAAE,mBAAoB,CAAC,CACtD,CAAED,KAAK,CAAE,oBAAoB,CAAEC,OAAO,CAAE,yCAA0C,CAAC,CACnF,CAAED,KAAK,CAAE,iBAAiB,CAAEC,OAAO,CAAE,uCAAwC,CAAC,CAC9E,CAAED,KAAK,CAAE,aAAa,CAAEC,OAAO,CAAE,iCAAkC,CAAC,CACrE,CAAC,CACF,KAAM,CAAAC,cAAc,CAAGzD,MAAM,CAAC,IAAI,CAAC,CAEnC,KAAM,CAAA0D,cAAc,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAC3B,CAAAA,qBAAA,CAAAF,cAAc,CAACG,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAED7D,SAAS,CAAC,IAAM,CACdyD,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAAChB,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAqB,iBAAiB,CAAG,cAAAA,CAAA,CAA8B,IAAvB,CAAAC,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACjD,KAAM,CAAAG,aAAa,CAAGJ,WAAW,EAAId,YAAY,CACjD,GAAI,CAACkB,aAAa,CAACC,IAAI,CAAC,CAAC,EAAIjB,SAAS,CAAE,OAExC,KAAM,CAAAkB,WAAW,CAAG,CAClB1B,EAAE,CAAEI,IAAI,CAACuB,GAAG,CAAC,CAAC,CACd1B,IAAI,CAAE,MAAM,CACZC,OAAO,CAAEsB,aAAa,CACtBrB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CAEDL,WAAW,CAAC6B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEF,WAAW,CAAC,CAAC,CAC3CnB,eAAe,CAAC,EAAE,CAAC,CACnBE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF,KAAM,CAAAoB,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,oBAAoB,CAAE,CACjDC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBvB,OAAO,CAAEY,aAAa,CACtBY,YAAY,CAAE,CACZjC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACiC,WAAW,CAAC,CAAC,CACnCC,UAAU,CAAE,aACd,CACF,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAV,QAAQ,CAACW,IAAI,CAAC,CAAC,CAElC,KAAM,CAAAC,YAAY,CAAG,CACnBzC,EAAE,CAAEI,IAAI,CAACuB,GAAG,CAAC,CAAC,CAAG,CAAC,CAClB1B,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAEqC,IAAI,CAACV,QAAQ,CACtB1B,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,aAAa,CAAEkC,IAAI,CAACtC,IAAI,CACxByC,eAAe,CAAEH,IAAI,CAACG,eAAe,CACrCC,YAAY,CAAEJ,IAAI,CAACI,YAAY,CAC/BC,OAAO,CAAEL,IAAI,CAACK,OAAO,CACrBC,eAAe,CAAEN,IAAI,CAACM,eACxB,CAAC,CAED9C,WAAW,CAAC6B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEa,YAAY,CAAC,CAAC,CAE9C,CAAE,MAAOK,KAAK,CAAE,CACd,KAAM,CAAAC,YAAY,CAAG,CACnB/C,EAAE,CAAEI,IAAI,CAACuB,GAAG,CAAC,CAAC,CAAG,CAAC,CAClB1B,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAE,2EAA2E,CACpFC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,aAAa,CAAE,OAAO,CACtByC,KAAK,CAAE,IACT,CAAC,CACD/C,WAAW,CAAC6B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEmB,YAAY,CAAC,CAAC,CAC9C,CAAC,OAAS,CACRtC,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAuC,iBAAiB,CAAG,KAAO,CAAAC,MAAM,EAAK,CAC1CxC,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CACF,KAAM,CAAAoB,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,uBAAuB,CAAE,CACpDC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBS,OAAO,CAAEK,MAAM,CACfC,OAAO,CAAE,IACX,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAV,QAAQ,CAACW,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACY,OAAO,CAAE,CAChB,KAAM,CAAAC,cAAc,CAAG,CACrBpD,EAAE,CAAEI,IAAI,CAACuB,GAAG,CAAC,CAAC,CACd1B,IAAI,CAAE,WAAW,CACjBC,OAAO,WAAAmD,MAAA,CAAOd,IAAI,CAAC3B,OAAO,2BAAAyC,MAAA,CAAyBd,IAAI,CAACe,eAAe,CAACC,GAAG,CAACC,KAAK,YAAAH,MAAA,CAC1EG,KAAK,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,MAAAL,MAAA,CAAIG,KAAK,CAACG,QAAQ,MAAAN,MAAA,CAAIG,KAAK,CAACI,MAAM,CACjE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAE,CACd1D,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,aAAa,CAAE,kBACjB,CAAC,CACDN,WAAW,CAAC6B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEwB,cAAc,CAAC,CAAC,CAE9C,GAAIvD,cAAc,CAAE,CAClBA,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,IAAM,CACL,KAAM,IAAI,CAAAiE,KAAK,CAACvB,IAAI,CAAC3B,OAAO,EAAI,wBAAwB,CAAC,CAC3D,CAEF,CAAE,MAAOkC,KAAK,CAAE,CACd,KAAM,CAAAC,YAAY,CAAG,CACnB/C,EAAE,CAAEI,IAAI,CAACuB,GAAG,CAAC,CAAC,CACd1B,IAAI,CAAE,WAAW,CACjBC,OAAO,mCAAAmD,MAAA,CAA+BP,KAAK,CAAClC,OAAO,CAAE,CACrDT,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,aAAa,CAAE,OAAO,CACtByC,KAAK,CAAE,IACT,CAAC,CACD/C,WAAW,CAAC6B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEmB,YAAY,CAAC,CAAC,CAC9C,CAAC,OAAS,CACRtC,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAsD,cAAc,CAAIC,KAAK,EAAK,CAChC,GAAIA,KAAK,CAACC,GAAG,GAAK,OAAO,EAAI,CAACD,KAAK,CAACE,QAAQ,CAAE,CAC5CF,KAAK,CAACG,cAAc,CAAC,CAAC,CACtBhD,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAiD,UAAU,CAAIjE,SAAS,EAAK,CAChC,MAAO,CAAAA,SAAS,CAACkE,kBAAkB,CAAC,EAAE,CAAE,CAAEC,IAAI,CAAE,SAAS,CAAEC,MAAM,CAAE,SAAU,CAAC,CAAC,CACjF,CAAC,CAED,KAAM,CAAAC,eAAe,CAAIC,YAAY,EAAK,CACxC,OAAQA,YAAY,EAClB,IAAK,cAAc,CACjB,mBAAOjF,IAAA,CAACV,cAAc,EAAC4F,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CAC5D,IAAK,iBAAiB,CACpB,mBAAOnF,IAAA,CAACR,cAAc,EAAC0F,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,MAAM,CAAE,CAAC,CACzD,IAAK,aAAa,CAChB,mBAAOnF,IAAA,CAACN,kBAAkB,EAACwF,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CAChE,QACE,mBAAOnF,IAAA,CAACd,SAAS,EAACgG,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CACzD,CACF,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIhE,OAAO,EAAK,KAAAiE,aAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CACrC,GAAI,CAACpE,OAAO,CAAC+B,YAAY,CAAE,MAAO,KAAI,CAEtC,KAAM,CAAAsC,IAAI,CAAGrE,OAAO,CAAC+B,YAAY,CAEjC,mBACEjD,KAAA,CAAC1B,IAAI,EAACkH,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,eAAe,CAAE,SAAS,CAAEC,MAAM,CAAE,mBAAoB,CAAE,CAAAC,QAAA,eAC3E5F,KAAA,CAACzB,WAAW,EAAAqH,QAAA,eACV5F,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAChE9F,IAAA,CAACV,cAAc,EAAC6F,KAAK,CAAC,SAAS,CAAE,CAAC,cAClCnF,IAAA,CAAC9B,UAAU,EAACiI,OAAO,CAAC,WAAW,CAAChB,KAAK,CAAC,cAAc,CAAAW,QAAA,CAAC,wBAErD,CAAY,CAAC,EACV,CAAC,cAEN5F,KAAA,CAACtB,IAAI,EAACwH,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAP,QAAA,eACzB9F,IAAA,CAACpB,IAAI,EAAC0H,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cACf5F,KAAA,CAAChC,UAAU,EAACiI,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9F,IAAA,WAAA8F,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAC,EAAAT,aAAA,CAAAI,IAAI,CAACe,OAAO,UAAAnB,aAAA,iBAAZA,aAAA,CAAcoB,YAAY,GAAI,CAAC,EAC/C,CAAC,CACT,CAAC,cACPzG,IAAA,CAACpB,IAAI,EAAC0H,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cACf5F,KAAA,CAAChC,UAAU,EAACiI,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9F,IAAA,WAAA8F,QAAA,CAAQ,aAAW,CAAQ,CAAC,KAAE,CAAC,EAAAR,cAAA,CAAAG,IAAI,CAACe,OAAO,UAAAlB,cAAA,iBAAZA,cAAA,CAAcoB,UAAU,GAAI,CAAC,EAClD,CAAC,CACT,CAAC,cACP1G,IAAA,CAACpB,IAAI,EAAC0H,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cACf5F,KAAA,CAAChC,UAAU,EAACiI,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9F,IAAA,WAAA8F,QAAA,CAAQ,kBAAgB,CAAQ,CAAC,KAAE,CAAC,EAAAP,cAAA,CAAAE,IAAI,CAACe,OAAO,UAAAjB,cAAA,iBAAZA,cAAA,CAAcoB,eAAe,GAAI,CAAC,EAC5D,CAAC,CACT,CAAC,cACP3G,IAAA,CAACpB,IAAI,EAAC0H,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cACf5F,KAAA,CAAChC,UAAU,EAACiI,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9F,IAAA,WAAA8F,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAAC,EAAAN,cAAA,CAAAC,IAAI,CAACe,OAAO,UAAAhB,cAAA,iBAAZA,cAAA,CAAcoB,iBAAiB,GAAI,CAAC,CAAC,IACtE,EAAY,CAAC,CACT,CAAC,EACH,CAAC,CAENnB,IAAI,CAACoB,MAAM,EAAIpB,IAAI,CAACoB,MAAM,CAAC/E,MAAM,CAAG,CAAC,eACpC5B,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAG,QAAA,eACjB9F,IAAA,CAAC9B,UAAU,EAACiI,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEZ,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,iBAE/D,CAAY,CAAC,CACZL,IAAI,CAACoB,MAAM,CAAC9C,GAAG,CAAC,CAACgD,KAAK,CAAEC,KAAK,gBAC5B9G,KAAA,CAACjC,GAAG,EAAayH,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAC,CAAEe,CAAC,CAAE,CAAC,CAAErB,eAAe,CAAE,uBAAuB,CAAEsB,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,eAC9F5F,KAAA,CAAChC,UAAU,EAACiI,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9F,IAAA,WAAA8F,QAAA,CAASiB,KAAK,CAACI,MAAM,CAACjD,WAAW,CAAC,CAAC,CAAS,CAAC,IAAC,CAAC6C,KAAK,CAAC5C,QAAQ,CAAC,GAAC,CAAC4C,KAAK,CAAC3C,MAAM,CAAC,MAAI,CAAC2C,KAAK,CAACK,WAAW,EACzF,CAAC,CACZL,KAAK,CAACM,UAAU,eACfnH,KAAA,CAAChC,UAAU,EAACiI,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,OAAO,CAAAW,QAAA,EAAC,SACnC,CAACiB,KAAK,CAACM,UAAU,EACd,CACb,CACAN,KAAK,CAACO,YAAY,eACjBpH,KAAA,CAAChC,UAAU,EAACiI,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,SAAS,CAACO,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,EAAC,WAClD,CAACiB,KAAK,CAACO,YAAY,EAClB,CACb,GAbON,KAcL,CACN,CAAC,EACC,CACN,EACU,CAAC,cACdhH,IAAA,CAACtB,WAAW,EAAAoH,QAAA,cACV9F,IAAA,CAAChC,MAAM,EACLwJ,IAAI,CAAC,OAAO,CACZrB,OAAO,CAAC,WAAW,CACnBhB,KAAK,CAAC,SAAS,CACfsC,OAAO,CAAEA,CAAA,GAAMjE,iBAAiB,CAACpC,OAAO,CAACgC,OAAO,CAAE,CAClDsE,QAAQ,CAAE1G,SAAU,CACpB2G,SAAS,cAAE3H,IAAA,CAACV,cAAc,GAAE,CAAE,CAAAwG,QAAA,CAC/B,8BAED,CAAQ,CAAC,CACE,CAAC,EACV,CAAC,CAEX,CAAC,CAED,mBACE5F,KAAA,CAACpC,KAAK,EAAC8J,SAAS,CAAE,CAAE,CAAClC,EAAE,CAAE,CAAEmC,MAAM,CAAE,OAAO,CAAE9B,OAAO,CAAE,MAAM,CAAE+B,aAAa,CAAE,QAAS,CAAE,CAAAhC,QAAA,eAErF9F,IAAA,CAAC/B,GAAG,EAACyH,EAAE,CAAE,CACPuB,CAAC,CAAE,CAAC,CACJrB,eAAe,CAAE,mDAAmD,CACpEmC,UAAU,CAAE,mDAAmD,CAC/D5C,KAAK,CAAE,OAAO,CACd6C,YAAY,CAAE,gBAChB,CAAE,CAAAlC,QAAA,cACA5F,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEiC,cAAc,CAAE,eAAgB,CAAE,CAAAnC,QAAA,eAClF5F,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAH,QAAA,eACzD9F,IAAA,CAACd,SAAS,GAAE,CAAC,cACbc,IAAA,CAAC9B,UAAU,EAACiI,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,UAAQ,CAAY,CAAC,cAC9C9F,IAAA,CAACrB,IAAI,EAACwC,KAAK,CAAC,eAAe,CAACqG,IAAI,CAAC,OAAO,CAACrC,KAAK,CAAC,SAAS,CAAE,CAAC,EACxD,CAAC,cACNjF,KAAA,CAACjC,GAAG,EAAA6H,QAAA,eACF9F,IAAA,CAAClB,OAAO,EAACoJ,KAAK,CAAC,UAAU,CAAApC,QAAA,cACvB9F,IAAA,CAACnB,UAAU,EACT2I,IAAI,CAAC,OAAO,CACZ9B,EAAE,CAAE,CAAEP,KAAK,CAAE,OAAQ,CAAE,CACvBsC,OAAO,CAAEA,CAAA,GAAM9F,iBAAiB,CAAC,4BAA4B,CAAE,CAAAmE,QAAA,cAE/D9F,IAAA,CAACF,QAAQ,GAAE,CAAC,CACF,CAAC,CACN,CAAC,cACVE,IAAA,CAAClB,OAAO,EAACoJ,KAAK,CAAC,oBAAoB,CAAApC,QAAA,cACjC9F,IAAA,CAACnB,UAAU,EACT2I,IAAI,CAAC,OAAO,CACZ9B,EAAE,CAAE,CAAEP,KAAK,CAAE,OAAQ,CAAE,CACvBsC,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CACF,KAAM,CAAAnF,KAAK,CAAC,qBAAqB,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAAC,CACtDhC,WAAW,CAAC,CAAC,CACXC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAE,0DAA0D,CACnEC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBC,aAAa,CAAE,MACjB,CAAC,CAAC,CAAC,CACL,CAAE,MAAOyC,KAAK,CAAE,CACd6E,OAAO,CAAC7E,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CAAE,CAAAwC,QAAA,cAEF9F,IAAA,CAACJ,WAAW,GAAE,CAAC,CACL,CAAC,CACN,CAAC,EACP,CAAC,EACH,CAAC,CACH,CAAC,cAGNI,IAAA,CAAC/B,GAAG,EAACyH,EAAE,CAAE,CAAEuB,CAAC,CAAE,CAAC,CAAErB,eAAe,CAAE,SAAS,CAAEoC,YAAY,CAAE,gBAAiB,CAAE,CAAAlC,QAAA,cAC5E9F,IAAA,CAAC/B,GAAG,EAACyH,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,GAAG,CAAE,CAAC,CAAEmC,QAAQ,CAAE,MAAO,CAAE,CAAAtC,QAAA,CACpD5E,YAAY,CAAC6C,GAAG,CAAC,CAACoD,MAAM,CAAEH,KAAK,gBAC9BhH,IAAA,CAACrB,IAAI,EAEHwC,KAAK,CAAEgG,MAAM,CAAChG,KAAM,CACpBqG,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAM9F,iBAAiB,CAACwF,MAAM,CAAC/F,OAAO,CAAE,CACjDsE,EAAE,CAAE,CAAE2C,MAAM,CAAE,SAAU,CAAE,CAC1BlC,OAAO,CAAC,UAAU,EALba,KAMN,CACF,CAAC,CACC,CAAC,CACH,CAAC,cAGN9G,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAE4C,IAAI,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAM,CAAEtB,CAAC,CAAE,CAAE,CAAE,CAAAnB,QAAA,eAC3C5F,KAAA,CAAC/B,IAAI,EAAA2H,QAAA,EACFxF,QAAQ,CAACyD,GAAG,CAAE3C,OAAO,eACpBlB,KAAA,CAAC9B,QAAQ,EAAkBsH,EAAE,CAAE,CAAEoC,aAAa,CAAE,QAAQ,CAAE9B,UAAU,CAAE,SAAS,CAAEwC,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eACvF9F,IAAA,CAAC/B,GAAG,EAACyH,EAAE,CAAE,CACPK,OAAO,CAAE,MAAM,CACfkC,cAAc,CAAE7G,OAAO,CAACX,IAAI,GAAK,MAAM,CAAG,UAAU,CAAG,YAAY,CACnEgI,KAAK,CAAE,MACT,CAAE,CAAA3C,QAAA,cACA5F,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CACPgD,QAAQ,CAAE,KAAK,CACf9C,eAAe,CAAExE,OAAO,CAACX,IAAI,GAAK,MAAM,CAAG,SAAS,CAAG,SAAS,CAChE0E,KAAK,CAAE/D,OAAO,CAACX,IAAI,GAAK,MAAM,CAAG,OAAO,CAAG,OAAO,CAClDyG,YAAY,CAAE,CAAC,CACfD,CAAC,CAAE,GAAG,CACNf,EAAE,CAAE,CACN,CAAE,CAAAJ,QAAA,eACA5F,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAAJ,QAAA,EACjE1E,OAAO,CAACX,IAAI,GAAK,MAAM,cAAGT,IAAA,CAACZ,UAAU,EAAC8F,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAGF,eAAe,CAAC5D,OAAO,CAACP,aAAa,CAAC,cACnGb,IAAA,CAAC9B,UAAU,EAACiI,OAAO,CAAC,SAAS,CAAAL,QAAA,CAC1BlB,UAAU,CAACxD,OAAO,CAACT,SAAS,CAAC,CACpB,CAAC,CACZS,OAAO,CAACiC,eAAe,eACtBrD,IAAA,CAACrB,IAAI,EAACwC,KAAK,CAAEC,OAAO,CAACiC,eAAgB,CAACmE,IAAI,CAAC,OAAO,CAACrB,OAAO,CAAC,UAAU,CAAE,CACxE,EACE,CAAC,cACNnG,IAAA,CAAC9B,UAAU,EAACiI,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEiD,UAAU,CAAE,UAAW,CAAE,CAAA7C,QAAA,CACxD1E,OAAO,CAACV,OAAO,CACN,CAAC,CAEZU,OAAO,CAACkC,KAAK,eACZtD,IAAA,CAAC1B,KAAK,EAACsK,QAAQ,CAAC,OAAO,CAAClD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAG,QAAA,CAAC,6CAEvC,CAAO,CACR,EACE,CAAC,CACH,CAAC,CAGLV,iBAAiB,CAAChE,OAAO,CAAC,GApCdA,OAAO,CAACZ,EAqCb,CACX,CAAC,CACDQ,SAAS,eACRhB,IAAA,CAAC5B,QAAQ,EAAA0H,QAAA,cACP5F,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAH,QAAA,eACzD9F,IAAA,CAACzB,gBAAgB,EAACiJ,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BxH,IAAA,CAAC9B,UAAU,EAACiI,OAAO,CAAC,OAAO,CAAChB,KAAK,CAAC,gBAAgB,CAAAW,QAAA,CAAC,sBAEnD,CAAY,CAAC,EACV,CAAC,CACE,CACX,EACG,CAAC,cACP9F,IAAA,QAAK6I,GAAG,CAAExH,cAAe,CAAE,CAAC,EACzB,CAAC,cAENrB,IAAA,CAAC3B,OAAO,GAAE,CAAC,cAGX6B,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEuB,CAAC,CAAE,CAAE,CAAE,CAAAnB,QAAA,eAChB5F,KAAA,CAACjC,GAAG,EAACyH,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,GAAG,CAAE,CAAE,CAAE,CAAAH,QAAA,eACnC9F,IAAA,CAACjC,SAAS,EACR+K,SAAS,MACTC,SAAS,MACTC,OAAO,CAAE,CAAE,CACXC,WAAW,CAAC,iEAAiE,CAC7EC,KAAK,CAAEpI,YAAa,CACpBqI,QAAQ,CAAGC,CAAC,EAAKrI,eAAe,CAACqI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDI,UAAU,CAAE/E,cAAe,CAC3BmD,QAAQ,CAAE1G,SAAU,CACpBmF,OAAO,CAAC,UAAU,CAClBqB,IAAI,CAAC,OAAO,CACb,CAAC,cACFxH,IAAA,CAAChC,MAAM,EACLmI,OAAO,CAAC,WAAW,CACnBsB,OAAO,CAAEA,CAAA,GAAM9F,iBAAiB,CAAC,CAAE,CACnC+F,QAAQ,CAAE,CAAC5G,YAAY,CAACmB,IAAI,CAAC,CAAC,EAAIjB,SAAU,CAC5C0E,EAAE,CAAE,CAAE6D,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA1D,QAAA,cAEhC9F,IAAA,CAAChB,QAAQ,GAAE,CAAC,CACN,CAAC,EACN,CAAC,cACNgB,IAAA,CAAC9B,UAAU,EAACiI,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,gBAAgB,CAACO,EAAE,CAAE,CAAEC,EAAE,CAAE,GAAG,CAAEI,OAAO,CAAE,OAAQ,CAAE,CAAAD,QAAA,CAAC,oEAExF,CAAY,CAAC,EACV,CAAC,EACD,CAAC,CAEZ,CAAC,CAED,cAAe,CAAA3F,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}