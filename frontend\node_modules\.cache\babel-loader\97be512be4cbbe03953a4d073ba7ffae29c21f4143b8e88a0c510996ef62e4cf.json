{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"focusVisibleClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from './cardActionAreaClasses';\nimport ButtonBase from '../ButtonBase';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  display: 'block',\n  textAlign: 'inherit',\n  borderRadius: 'inherit',\n  // for Safari to work https://github.com/mui/material-ui/issues/36285.\n  width: '100%',\n  [`&:hover .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.hoverOpacity,\n    '@media (hover: none)': {\n      opacity: 0\n    }\n  },\n  [`&.${cardActionAreaClasses.focusVisible} .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.focusOpacity\n  }\n}));\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight',\n  overridesResolver: (props, styles) => styles.focusHighlight\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  pointerEvents: 'none',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  borderRadius: 'inherit',\n  opacity: 0,\n  backgroundColor: 'currentcolor',\n  transition: theme.transitions.create('opacity', {\n    duration: theme.transitions.duration.short\n  })\n}));\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n      children,\n      className,\n      focusVisibleClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(CardActionAreaRoot, _extends({\n    className: clsx(classes.root, className),\n    focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [children, /*#__PURE__*/_jsx(CardActionAreaFocusHighlight, {\n      className: classes.focusHighlight,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "useDefaultProps", "styled", "cardActionAreaClasses", "getCardActionAreaUtilityClass", "ButtonBase", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "focusHighlight", "CardActionAreaRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "display", "textAlign", "borderRadius", "width", "opacity", "vars", "palette", "action", "hoverOpacity", "focusVisible", "focusOpacity", "CardActionAreaFocusHighlight", "overflow", "pointerEvents", "position", "top", "right", "bottom", "left", "backgroundColor", "transition", "transitions", "create", "duration", "short", "CardActionArea", "forwardRef", "inProps", "ref", "children", "className", "focusVisibleClassName", "other", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/CardActionArea/CardActionArea.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"focusVisibleClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from './cardActionAreaClasses';\nimport ButtonBase from '../ButtonBase';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  display: 'block',\n  textAlign: 'inherit',\n  borderRadius: 'inherit',\n  // for Safari to work https://github.com/mui/material-ui/issues/36285.\n  width: '100%',\n  [`&:hover .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.hoverOpacity,\n    '@media (hover: none)': {\n      opacity: 0\n    }\n  },\n  [`&.${cardActionAreaClasses.focusVisible} .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.focusOpacity\n  }\n}));\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight',\n  overridesResolver: (props, styles) => styles.focusHighlight\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  pointerEvents: 'none',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  borderRadius: 'inherit',\n  opacity: 0,\n  backgroundColor: 'currentcolor',\n  transition: theme.transitions.create('opacity', {\n    duration: theme.transitions.duration.short\n  })\n}));\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n      children,\n      className,\n      focusVisibleClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(CardActionAreaRoot, _extends({\n    className: clsx(classes.root, className),\n    focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [children, /*#__PURE__*/_jsx(CardActionAreaFocusHighlight, {\n      className: classes.focusHighlight,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,uBAAuB,CAAC;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,qBAAqB,IAAIC,6BAA6B,QAAQ,yBAAyB;AAC9F,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOf,cAAc,CAACa,KAAK,EAAET,6BAA6B,EAAEQ,OAAO,CAAC;AACtE,CAAC;AACD,MAAMI,kBAAkB,GAAGd,MAAM,CAACG,UAAU,EAAE;EAC5CY,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFQ;AACF,CAAC,MAAM;EACLC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,SAAS;EACpBC,YAAY,EAAE,SAAS;EACvB;EACAC,KAAK,EAAE,MAAM;EACb,CAAC,YAAYvB,qBAAqB,CAACY,cAAc,EAAE,GAAG;IACpDY,OAAO,EAAE,CAACL,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEO,OAAO,CAACC,MAAM,CAACC,YAAY;IAC1D,sBAAsB,EAAE;MACtBJ,OAAO,EAAE;IACX;EACF,CAAC;EACD,CAAC,KAAKxB,qBAAqB,CAAC6B,YAAY,KAAK7B,qBAAqB,CAACY,cAAc,EAAE,GAAG;IACpFY,OAAO,EAAE,CAACL,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEO,OAAO,CAACC,MAAM,CAACG;EAChD;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,4BAA4B,GAAGhC,MAAM,CAAC,MAAM,EAAE;EAClDe,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFO;AACF,CAAC,MAAM;EACLa,QAAQ,EAAE,QAAQ;EAClBC,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPhB,YAAY,EAAE,SAAS;EACvBE,OAAO,EAAE,CAAC;EACVe,eAAe,EAAE,cAAc;EAC/BC,UAAU,EAAErB,KAAK,CAACsB,WAAW,CAACC,MAAM,CAAC,SAAS,EAAE;IAC9CC,QAAQ,EAAExB,KAAK,CAACsB,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMC,cAAc,GAAG,aAAanD,KAAK,CAACoD,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAM/B,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAE8B,OAAO;IACdjC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFmC,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,GAAGlC,KAAK;IACTmC,KAAK,GAAG5D,6BAA6B,CAACyB,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAMe,UAAU,GAAGS,KAAK;EACxB,MAAMR,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,KAAK,CAACO,kBAAkB,EAAEtB,QAAQ,CAAC;IACrD2D,SAAS,EAAEtD,IAAI,CAACa,OAAO,CAACE,IAAI,EAAEuC,SAAS,CAAC;IACxCC,qBAAqB,EAAEvD,IAAI,CAACuD,qBAAqB,EAAE1C,OAAO,CAACoB,YAAY,CAAC;IACxEmB,GAAG,EAAEA,GAAG;IACRxC,UAAU,EAAEA;EACd,CAAC,EAAE4C,KAAK,EAAE;IACRH,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAa7C,IAAI,CAAC2B,4BAA4B,EAAE;MACnEmB,SAAS,EAAEzC,OAAO,CAACG,cAAc;MACjCJ,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF6C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,cAAc,CAACW,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEP,QAAQ,EAAEtD,SAAS,CAAC8D,IAAI;EACxB;AACF;AACA;EACEhD,OAAO,EAAEd,SAAS,CAAC+D,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAEvD,SAAS,CAACgE,MAAM;EAC3B;AACF;AACA;EACER,qBAAqB,EAAExD,SAAS,CAACgE,MAAM;EACvC;AACF;AACA;EACEC,EAAE,EAAEjE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACmE,OAAO,CAACnE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC+D,MAAM,EAAE/D,SAAS,CAACqE,IAAI,CAAC,CAAC,CAAC,EAAErE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC+D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}