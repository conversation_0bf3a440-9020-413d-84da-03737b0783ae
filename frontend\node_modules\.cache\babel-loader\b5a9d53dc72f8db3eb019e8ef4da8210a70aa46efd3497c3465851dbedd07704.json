{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveProps from '@mui/utils/resolveProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PropsContext = /*#__PURE__*/React.createContext(undefined);\nfunction DefaultPropsProvider({\n  value,\n  children\n}) {\n  return /*#__PURE__*/_jsx(PropsContext.Provider, {\n    value: value,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? DefaultPropsProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  value: PropTypes.object\n} : void 0;\nfunction getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name]) {\n    return props;\n  }\n  const config = theme.components[name];\n  if (config.defaultProps) {\n    // compatible with v5 signature\n    return resolveProps(config.defaultProps, props);\n  }\n  if (!config.styleOverrides && !config.variants) {\n    // v6 signature, no property 'defaultProps'\n    return resolveProps(config, props);\n  }\n  return props;\n}\nexport function useDefaultProps({\n  props,\n  name\n}) {\n  const ctx = React.useContext(PropsContext);\n  return getThemeProps({\n    props,\n    name,\n    theme: {\n      components: ctx\n    }\n  });\n}\nexport default DefaultPropsProvider;", "map": {"version": 3, "names": ["React", "PropTypes", "resolveProps", "jsx", "_jsx", "PropsContext", "createContext", "undefined", "DefaultPropsProvider", "value", "children", "Provider", "process", "env", "NODE_ENV", "propTypes", "node", "object", "getThemeProps", "params", "theme", "name", "props", "components", "config", "defaultProps", "styleOverrides", "variants", "useDefaultProps", "ctx", "useContext"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/system/esm/DefaultPropsProvider/DefaultPropsProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveProps from '@mui/utils/resolveProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PropsContext = /*#__PURE__*/React.createContext(undefined);\nfunction DefaultPropsProvider({\n  value,\n  children\n}) {\n  return /*#__PURE__*/_jsx(PropsContext.Provider, {\n    value: value,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? DefaultPropsProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  value: PropTypes.object\n} : void 0;\nfunction getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name]) {\n    return props;\n  }\n  const config = theme.components[name];\n  if (config.defaultProps) {\n    // compatible with v5 signature\n    return resolveProps(config.defaultProps, props);\n  }\n  if (!config.styleOverrides && !config.variants) {\n    // v6 signature, no property 'defaultProps'\n    return resolveProps(config, props);\n  }\n  return props;\n}\nexport function useDefaultProps({\n  props,\n  name\n}) {\n  const ctx = React.useContext(PropsContext);\n  return getThemeProps({\n    props,\n    name,\n    theme: {\n      components: ctx\n    }\n  });\n}\nexport default DefaultPropsProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaL,KAAK,CAACM,aAAa,CAACC,SAAS,CAAC;AAChE,SAASC,oBAAoBA,CAAC;EAC5BC,KAAK;EACLC;AACF,CAAC,EAAE;EACD,OAAO,aAAaN,IAAI,CAACC,YAAY,CAACM,QAAQ,EAAE;IAC9CF,KAAK,EAAEA,KAAK;IACZC,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGN,oBAAoB,CAACO,SAAS,CAAC,yBAAyB;EAC9F;EACA;EACA;EACA;EACA;AACF;AACA;EACEL,QAAQ,EAAET,SAAS,CAACe,IAAI;EACxB;AACF;AACA;EACEP,KAAK,EAAER,SAAS,CAACgB;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC7B,MAAM;IACJC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGH,MAAM;EACV,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAACG,UAAU,IAAI,CAACH,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC,EAAE;IAC1D,OAAOC,KAAK;EACd;EACA,MAAME,MAAM,GAAGJ,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC;EACrC,IAAIG,MAAM,CAACC,YAAY,EAAE;IACvB;IACA,OAAOvB,YAAY,CAACsB,MAAM,CAACC,YAAY,EAAEH,KAAK,CAAC;EACjD;EACA,IAAI,CAACE,MAAM,CAACE,cAAc,IAAI,CAACF,MAAM,CAACG,QAAQ,EAAE;IAC9C;IACA,OAAOzB,YAAY,CAACsB,MAAM,EAAEF,KAAK,CAAC;EACpC;EACA,OAAOA,KAAK;AACd;AACA,OAAO,SAASM,eAAeA,CAAC;EAC9BN,KAAK;EACLD;AACF,CAAC,EAAE;EACD,MAAMQ,GAAG,GAAG7B,KAAK,CAAC8B,UAAU,CAACzB,YAAY,CAAC;EAC1C,OAAOa,aAAa,CAAC;IACnBI,KAAK;IACLD,IAAI;IACJD,KAAK,EAAE;MACLG,UAAU,EAAEM;IACd;EACF,CAAC,CAAC;AACJ;AACA,eAAerB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}