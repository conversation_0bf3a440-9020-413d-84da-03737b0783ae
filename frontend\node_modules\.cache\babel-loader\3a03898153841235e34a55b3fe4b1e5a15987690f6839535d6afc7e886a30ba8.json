{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12.65 10C11.7 7.31 8.9 5.5 5.77 6.12c-2.29.46-4.15 2.29-4.63 4.58C.32 14.57 3.26 18 7 18c2.61 0 4.83-1.67 5.65-4H17v2c0 1.1.9 2 2 2s2-.9 2-2v-2c1.1 0 2-.9 2-2s-.9-2-2-2zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2\"\n}), 'VpnKeyRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/icons-material/esm/VpnKeyRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12.65 10C11.7 7.31 8.9 5.5 5.77 6.12c-2.29.46-4.15 2.29-4.63 4.58C.32 14.57 3.26 18 7 18c2.61 0 4.83-1.67 5.65-4H17v2c0 1.1.9 2 2 2s2-.9 2-2v-2c1.1 0 2-.9 2-2s-.9-2-2-2zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2\"\n}), 'VpnKeyRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}