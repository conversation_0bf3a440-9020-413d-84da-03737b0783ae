{"ast": null, "code": "/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CalendarCheck = createLucideIcon(\"CalendarCheck\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"4\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"eu3xkr\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"16\",\n  y1: \"2\",\n  y2: \"6\",\n  key: \"m3sa8f\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"8\",\n  y1: \"2\",\n  y2: \"6\",\n  key: \"18kwsl\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"10\",\n  y2: \"10\",\n  key: \"xt86sb\"\n}], [\"path\", {\n  d: \"m9 16 2 2 4-4\",\n  key: \"19s6y9\"\n}]]);\nexport { CalendarCheck as default };", "map": {"version": 3, "names": ["CalendarCheck", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\node_modules\\lucide-react\\src\\icons\\calendar-check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CalendarCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iOCIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIzIiB4Mj0iMjEiIHkxPSIxMCIgeTI9IjEwIiAvPgogIDxwYXRoIGQ9Im05IDE2IDIgMiA0LTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/calendar-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CalendarCheck = createLucideIcon('CalendarCheck', [\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', ry: '2', key: 'eu3xkr' }],\n  ['line', { x1: '16', x2: '16', y1: '2', y2: '6', key: 'm3sa8f' }],\n  ['line', { x1: '8', x2: '8', y1: '2', y2: '6', key: '18kwsl' }],\n  ['line', { x1: '3', x2: '21', y1: '10', y2: '10', key: 'xt86sb' }],\n  ['path', { d: 'm9 16 2 2 4-4', key: '19s6y9' }],\n]);\n\nexport default CalendarCheck;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,MAAQ;EAAEC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAEK,CAAA,EAAG,eAAiB;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}