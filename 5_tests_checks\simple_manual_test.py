#!/usr/bin/env python3
import requests
import json

def test_single_prompt(prompt):
    print(f"\n{'='*60}")
    print(f"PROMPT: {prompt}")
    print('='*60)
    
    try:
        response = requests.post(
            "http://localhost:8080/api/v1/chat",
            json={
                "message": prompt,
                "session_id": "manual_test",
                "context": {"test_mode": True}
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get("response", "")
            print("RESPONSE:")
            print(response_text)
            print(f"\nLength: {len(response_text)} characters")
            
            # Quick analysis
            has_prices = "$" in response_text
            has_trading = any(word in response_text.lower() for word in ["buy", "sell", "trade", "target", "entry", "stop"])
            has_confidence = "confidence" in response_text.lower()
            
            print(f"\nAnalysis:")
            print(f"  Has prices: {has_prices}")
            print(f"  Has trading terms: {has_trading}")
            print(f"  Has confidence: {has_confidence}")
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"ERROR: {e}")

# Test key prompts
prompts = [
    "What's Apple trading at right now?",
    "Are there any stocks that look like they might jump soon?",
    "I want to make $100 by tomorrow—what trade should I place?",
    "Can you suggest an options trade on NVIDIA that could make me money this week?",
    "Please buy 10 shares of Amazon for me now."
]

print("🚀 A.T.L.A.S. MANUAL TEST - SEEING ACTUAL RESPONSES")

for prompt in prompts:
    test_single_prompt(prompt)
    
print(f"\n{'='*60}")
print("TEST COMPLETE")
print('='*60)
