{"ast": null, "code": "import { MS, MOZ, WEBKIT, RULESET, KEYFRAMES, DECLARATION } from './Enum.js';\nimport { match, charat, substr, strlen, sizeof, replace, combine } from './Utility.js';\nimport { copy, tokenize } from './Tokenizer.js';\nimport { serialize } from './Serializer.js';\nimport { prefix } from './Prefixer.js';\n\n/**\n * @param {function[]} collection\n * @return {function}\n */\nexport function middleware(collection) {\n  var length = sizeof(collection);\n  return function (element, index, children, callback) {\n    var output = '';\n    for (var i = 0; i < length; i++) output += collection[i](element, index, children, callback) || '';\n    return output;\n  };\n}\n\n/**\n * @param {function} callback\n * @return {function}\n */\nexport function rulesheet(callback) {\n  return function (element) {\n    if (!element.root) if (element = element.return) callback(element);\n  };\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n */\nexport function prefixer(element, index, children, callback) {\n  if (element.length > -1) if (!element.return) switch (element.type) {\n    case DECLARATION:\n      element.return = prefix(element.value, element.length, children);\n      return;\n    case KEYFRAMES:\n      return serialize([copy(element, {\n        value: replace(element.value, '@', '@' + WEBKIT)\n      })], callback);\n    case RULESET:\n      if (element.length) return combine(element.props, function (value) {\n        switch (match(value, /(::plac\\w+|:read-\\w+)/)) {\n          // :read-(only|write)\n          case ':read-only':\n          case ':read-write':\n            return serialize([copy(element, {\n              props: [replace(value, /:(read-\\w+)/, ':' + MOZ + '$1')]\n            })], callback);\n          // :placeholder\n          case '::placeholder':\n            return serialize([copy(element, {\n              props: [replace(value, /:(plac\\w+)/, ':' + WEBKIT + 'input-$1')]\n            }), copy(element, {\n              props: [replace(value, /:(plac\\w+)/, ':' + MOZ + '$1')]\n            }), copy(element, {\n              props: [replace(value, /:(plac\\w+)/, MS + 'input-$1')]\n            })], callback);\n        }\n        return '';\n      });\n  }\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n */\nexport function namespace(element) {\n  switch (element.type) {\n    case RULESET:\n      element.props = element.props.map(function (value) {\n        return combine(tokenize(value), function (value, index, children) {\n          switch (charat(value, 0)) {\n            // \\f\n            case 12:\n              return substr(value, 1, strlen(value));\n            // \\0 ( + > ~\n            case 0:\n            case 40:\n            case 43:\n            case 62:\n            case 126:\n              return value;\n            // :\n            case 58:\n              if (children[++index] === 'global') children[index] = '', children[++index] = '\\f' + substr(children[index], index = 1, -1);\n            // \\s\n            case 32:\n              return index === 1 ? '' : value;\n            default:\n              switch (index) {\n                case 0:\n                  element = value;\n                  return sizeof(children) > 1 ? '' : value;\n                case index = sizeof(children) - 1:\n                case 2:\n                  return index === 2 ? value + element + element : value + element;\n                default:\n                  return value;\n              }\n          }\n        });\n      });\n  }\n}", "map": {"version": 3, "names": ["MS", "MOZ", "WEBKIT", "RULESET", "KEYFRAMES", "DECLARATION", "match", "charat", "substr", "strlen", "sizeof", "replace", "combine", "copy", "tokenize", "serialize", "prefix", "middleware", "collection", "length", "element", "index", "children", "callback", "output", "i", "rulesheet", "root", "return", "prefixer", "type", "value", "props", "namespace", "map"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/stylis/src/Middleware.js"], "sourcesContent": ["import {MS, MOZ, WEBKIT, RULESET, KEYFRAMES, DECLARATION} from './Enum.js'\nimport {match, charat, substr, strlen, sizeof, replace, combine} from './Utility.js'\nimport {copy, tokenize} from './Tokenizer.js'\nimport {serialize} from './Serializer.js'\nimport {prefix} from './Prefixer.js'\n\n/**\n * @param {function[]} collection\n * @return {function}\n */\nexport function middleware (collection) {\n\tvar length = sizeof(collection)\n\n\treturn function (element, index, children, callback) {\n\t\tvar output = ''\n\n\t\tfor (var i = 0; i < length; i++)\n\t\t\toutput += collection[i](element, index, children, callback) || ''\n\n\t\treturn output\n\t}\n}\n\n/**\n * @param {function} callback\n * @return {function}\n */\nexport function rulesheet (callback) {\n\treturn function (element) {\n\t\tif (!element.root)\n\t\t\tif (element = element.return)\n\t\t\t\tcallback(element)\n\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n */\nexport function prefixer (element, index, children, callback) {\n\tif (element.length > -1)\n\t\tif (!element.return)\n\t\t\tswitch (element.type) {\n\t\t\t\tcase DECLARATION: element.return = prefix(element.value, element.length, children)\n\t\t\t\t\treturn\n\t\t\t\tcase KEYFRAMES:\n\t\t\t\t\treturn serialize([copy(element, {value: replace(element.value, '@', '@' + WEBKIT)})], callback)\n\t\t\t\tcase RULESET:\n\t\t\t\t\tif (element.length)\n\t\t\t\t\t\treturn combine(element.props, function (value) {\n\t\t\t\t\t\t\tswitch (match(value, /(::plac\\w+|:read-\\w+)/)) {\n\t\t\t\t\t\t\t\t// :read-(only|write)\n\t\t\t\t\t\t\t\tcase ':read-only': case ':read-write':\n\t\t\t\t\t\t\t\t\treturn serialize([copy(element, {props: [replace(value, /:(read-\\w+)/, ':' + MOZ + '$1')]})], callback)\n\t\t\t\t\t\t\t\t// :placeholder\n\t\t\t\t\t\t\t\tcase '::placeholder':\n\t\t\t\t\t\t\t\t\treturn serialize([\n\t\t\t\t\t\t\t\t\t\tcopy(element, {props: [replace(value, /:(plac\\w+)/, ':' + WEBKIT + 'input-$1')]}),\n\t\t\t\t\t\t\t\t\t\tcopy(element, {props: [replace(value, /:(plac\\w+)/, ':' + MOZ + '$1')]}),\n\t\t\t\t\t\t\t\t\t\tcopy(element, {props: [replace(value, /:(plac\\w+)/, MS + 'input-$1')]})\n\t\t\t\t\t\t\t\t\t], callback)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\treturn ''\n\t\t\t\t\t\t})\n\t\t\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n */\nexport function namespace (element) {\n\tswitch (element.type) {\n\t\tcase RULESET:\n\t\t\telement.props = element.props.map(function (value) {\n\t\t\t\treturn combine(tokenize(value), function (value, index, children) {\n\t\t\t\t\tswitch (charat(value, 0)) {\n\t\t\t\t\t\t// \\f\n\t\t\t\t\t\tcase 12:\n\t\t\t\t\t\t\treturn substr(value, 1, strlen(value))\n\t\t\t\t\t\t// \\0 ( + > ~\n\t\t\t\t\t\tcase 0: case 40: case 43: case 62: case 126:\n\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t// :\n\t\t\t\t\t\tcase 58:\n\t\t\t\t\t\t\tif (children[++index] === 'global')\n\t\t\t\t\t\t\t\tchildren[index] = '', children[++index] = '\\f' + substr(children[index], index = 1, -1)\n\t\t\t\t\t\t// \\s\n\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\treturn index === 1 ? '' : value\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tswitch (index) {\n\t\t\t\t\t\t\t\tcase 0: element = value\n\t\t\t\t\t\t\t\t\treturn sizeof(children) > 1 ? '' : value\n\t\t\t\t\t\t\t\tcase index = sizeof(children) - 1: case 2:\n\t\t\t\t\t\t\t\t\treturn index === 2 ? value + element + element : value + element\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t})\n\t}\n}\n"], "mappings": "AAAA,SAAQA,EAAE,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,QAAO,WAAW;AAC1E,SAAQC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAO,cAAc;AACpF,SAAQC,IAAI,EAAEC,QAAQ,QAAO,gBAAgB;AAC7C,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,MAAM,QAAO,eAAe;;AAEpC;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAAEC,UAAU,EAAE;EACvC,IAAIC,MAAM,GAAGT,MAAM,CAACQ,UAAU,CAAC;EAE/B,OAAO,UAAUE,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACpD,IAAIC,MAAM,GAAG,EAAE;IAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAEM,CAAC,EAAE,EAC9BD,MAAM,IAAIN,UAAU,CAACO,CAAC,CAAC,CAACL,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,IAAI,EAAE;IAElE,OAAOC,MAAM;EACd,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,SAASA,CAAEH,QAAQ,EAAE;EACpC,OAAO,UAAUH,OAAO,EAAE;IACzB,IAAI,CAACA,OAAO,CAACO,IAAI,EAChB,IAAIP,OAAO,GAAGA,OAAO,CAACQ,MAAM,EAC3BL,QAAQ,CAACH,OAAO,CAAC;EACpB,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,QAAQA,CAAET,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC7D,IAAIH,OAAO,CAACD,MAAM,GAAG,CAAC,CAAC,EACtB,IAAI,CAACC,OAAO,CAACQ,MAAM,EAClB,QAAQR,OAAO,CAACU,IAAI;IACnB,KAAKzB,WAAW;MAAEe,OAAO,CAACQ,MAAM,GAAGZ,MAAM,CAACI,OAAO,CAACW,KAAK,EAAEX,OAAO,CAACD,MAAM,EAAEG,QAAQ,CAAC;MACjF;IACD,KAAKlB,SAAS;MACb,OAAOW,SAAS,CAAC,CAACF,IAAI,CAACO,OAAO,EAAE;QAACW,KAAK,EAAEpB,OAAO,CAACS,OAAO,CAACW,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG7B,MAAM;MAAC,CAAC,CAAC,CAAC,EAAEqB,QAAQ,CAAC;IAChG,KAAKpB,OAAO;MACX,IAAIiB,OAAO,CAACD,MAAM,EACjB,OAAOP,OAAO,CAACQ,OAAO,CAACY,KAAK,EAAE,UAAUD,KAAK,EAAE;QAC9C,QAAQzB,KAAK,CAACyB,KAAK,EAAE,uBAAuB,CAAC;UAC5C;UACA,KAAK,YAAY;UAAE,KAAK,aAAa;YACpC,OAAOhB,SAAS,CAAC,CAACF,IAAI,CAACO,OAAO,EAAE;cAACY,KAAK,EAAE,CAACrB,OAAO,CAACoB,KAAK,EAAE,aAAa,EAAE,GAAG,GAAG9B,GAAG,GAAG,IAAI,CAAC;YAAC,CAAC,CAAC,CAAC,EAAEsB,QAAQ,CAAC;UACxG;UACA,KAAK,eAAe;YACnB,OAAOR,SAAS,CAAC,CAChBF,IAAI,CAACO,OAAO,EAAE;cAACY,KAAK,EAAE,CAACrB,OAAO,CAACoB,KAAK,EAAE,YAAY,EAAE,GAAG,GAAG7B,MAAM,GAAG,UAAU,CAAC;YAAC,CAAC,CAAC,EACjFW,IAAI,CAACO,OAAO,EAAE;cAACY,KAAK,EAAE,CAACrB,OAAO,CAACoB,KAAK,EAAE,YAAY,EAAE,GAAG,GAAG9B,GAAG,GAAG,IAAI,CAAC;YAAC,CAAC,CAAC,EACxEY,IAAI,CAACO,OAAO,EAAE;cAACY,KAAK,EAAE,CAACrB,OAAO,CAACoB,KAAK,EAAE,YAAY,EAAE/B,EAAE,GAAG,UAAU,CAAC;YAAC,CAAC,CAAC,CACvE,EAAEuB,QAAQ,CAAC;QACd;QAEA,OAAO,EAAE;MACV,CAAC,CAAC;EACL;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,SAASA,CAAEb,OAAO,EAAE;EACnC,QAAQA,OAAO,CAACU,IAAI;IACnB,KAAK3B,OAAO;MACXiB,OAAO,CAACY,KAAK,GAAGZ,OAAO,CAACY,KAAK,CAACE,GAAG,CAAC,UAAUH,KAAK,EAAE;QAClD,OAAOnB,OAAO,CAACE,QAAQ,CAACiB,KAAK,CAAC,EAAE,UAAUA,KAAK,EAAEV,KAAK,EAAEC,QAAQ,EAAE;UACjE,QAAQf,MAAM,CAACwB,KAAK,EAAE,CAAC,CAAC;YACvB;YACA,KAAK,EAAE;cACN,OAAOvB,MAAM,CAACuB,KAAK,EAAE,CAAC,EAAEtB,MAAM,CAACsB,KAAK,CAAC,CAAC;YACvC;YACA,KAAK,CAAC;YAAE,KAAK,EAAE;YAAE,KAAK,EAAE;YAAE,KAAK,EAAE;YAAE,KAAK,GAAG;cAC1C,OAAOA,KAAK;YACb;YACA,KAAK,EAAE;cACN,IAAIT,QAAQ,CAAC,EAAED,KAAK,CAAC,KAAK,QAAQ,EACjCC,QAAQ,CAACD,KAAK,CAAC,GAAG,EAAE,EAAEC,QAAQ,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,GAAGb,MAAM,CAACc,QAAQ,CAACD,KAAK,CAAC,EAAEA,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACzF;YACA,KAAK,EAAE;cACN,OAAOA,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGU,KAAK;YAChC;cACC,QAAQV,KAAK;gBACZ,KAAK,CAAC;kBAAED,OAAO,GAAGW,KAAK;kBACtB,OAAOrB,MAAM,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAGS,KAAK;gBACzC,KAAKV,KAAK,GAAGX,MAAM,CAACY,QAAQ,CAAC,GAAG,CAAC;gBAAE,KAAK,CAAC;kBACxC,OAAOD,KAAK,KAAK,CAAC,GAAGU,KAAK,GAAGX,OAAO,GAAGA,OAAO,GAAGW,KAAK,GAAGX,OAAO;gBACjE;kBACC,OAAOW,KAAK;cACd;UACF;QACD,CAAC,CAAC;MACH,CAAC,CAAC;EACJ;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}