{"ast": null, "code": "'use client';\n\nexport { default } from './Backdrop';\nexport { default as backdropClasses } from './backdropClasses';\nexport * from './backdropClasses';", "map": {"version": 3, "names": ["default", "backdropClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/Backdrop/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Backdrop';\nexport { default as backdropClasses } from './backdropClasses';\nexport * from './backdropClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,eAAe,QAAQ,mBAAmB;AAC9D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}