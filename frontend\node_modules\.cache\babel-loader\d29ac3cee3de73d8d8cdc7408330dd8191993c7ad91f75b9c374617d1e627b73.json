{"ast": null, "code": "'use client';\n\nexport { default } from './OutlinedInput';\nexport { default as outlinedInputClasses } from './outlinedInputClasses';\nexport * from './outlinedInputClasses';", "map": {"version": 3, "names": ["default", "outlinedInputClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/OutlinedInput/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './OutlinedInput';\nexport { default as outlinedInputClasses } from './outlinedInputClasses';\nexport * from './outlinedInputClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,oBAAoB,QAAQ,wBAAwB;AACxE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}