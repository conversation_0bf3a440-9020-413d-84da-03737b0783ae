{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardMediaUtilityClass(slot) {\n  return generateUtilityClass('MuiCardMedia', slot);\n}\nconst cardMediaClasses = generateUtilityClasses('MuiCardMedia', ['root', 'media', 'img']);\nexport default cardMediaClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getCardMediaUtilityClass", "slot", "cardMediaClasses"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/CardMedia/cardMediaClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardMediaUtilityClass(slot) {\n  return generateUtilityClass('MuiCardMedia', slot);\n}\nconst cardMediaClasses = generateUtilityClasses('MuiCardMedia', ['root', 'media', 'img']);\nexport default cardMediaClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACzF,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}