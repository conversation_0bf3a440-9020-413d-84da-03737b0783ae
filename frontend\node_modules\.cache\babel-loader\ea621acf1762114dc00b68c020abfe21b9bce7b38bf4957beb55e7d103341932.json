{"ast": null, "code": "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "map": {"version": 3, "names": ["getVariation", "placement", "split"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@popperjs/core/lib/utils/getVariation.js"], "sourcesContent": ["export default function getVariation(placement) {\n  return placement.split('-')[1];\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,SAAS,EAAE;EAC9C,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}