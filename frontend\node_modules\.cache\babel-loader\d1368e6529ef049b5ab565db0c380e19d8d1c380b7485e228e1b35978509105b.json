{"ast": null, "code": "'use client';\n\nimport systemUseThemeProps from '@mui/system/useThemeProps';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps(_ref) {\n  let {\n    props,\n    name\n  } = _ref;\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme,\n    themeId: THEME_ID\n  });\n}", "map": {"version": 3, "names": ["systemUseThemeProps", "defaultTheme", "THEME_ID", "useThemeProps", "_ref", "props", "name", "themeId"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/styles/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport systemUseThemeProps from '@mui/system/useThemeProps';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps({\n  props,\n  name\n}) {\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme,\n    themeId: THEME_ID\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,mBAAmB,MAAM,2BAA2B;AAC3D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,eAAe,SAASC,aAAaA,CAAAC,IAAA,EAGlC;EAAA,IAHmC;IACpCC,KAAK;IACLC;EACF,CAAC,GAAAF,IAAA;EACC,OAAOJ,mBAAmB,CAAC;IACzBK,KAAK;IACLC,IAAI;IACJL,YAAY;IACZM,OAAO,EAAEL;EACX,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}